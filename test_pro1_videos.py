#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pro1视频测试脚本
Test script for Pro1 videos (1.mp4 and 2.mp4)
"""

import os
import sys

def check_pro1_videos():
    """检查Pro1视频文件"""
    print("🔍 检查Pro1视频文件...")
    print("=" * 50)
    
    pro1_videos = ['1.mp4', '2.mp4']
    video_info = []
    
    for video in pro1_videos:
        if os.path.exists(video):
            size_mb = os.path.getsize(video) / (1024*1024)
            video_info.append({
                'name': video,
                'size_mb': size_mb,
                'exists': True
            })
            print(f"✅ {video}: {size_mb:.1f}MB")
        else:
            video_info.append({
                'name': video,
                'size_mb': 0,
                'exists': False
            })
            print(f"❌ {video}: 文件不存在")
    
    return video_info

def check_other_files():
    """检查其他必要文件"""
    print("\n🔍 检查其他必要文件...")
    print("=" * 50)
    
    other_files = {
        'multi_channel_area_alarm_detector_fixed.py': '主程序文件',
        '1.mp3': '警报音文件'
    }
    
    for filename, description in other_files.items():
        if os.path.exists(filename):
            size_kb = os.path.getsize(filename) / 1024
            print(f"✅ {filename}: {description} ({size_kb:.1f}KB)")
        else:
            print(f"❌ {filename}: {description} (未找到)")

def show_test_instructions():
    """显示测试说明"""
    print("\n📖 Pro1视频测试说明:")
    print("=" * 50)
    
    instructions = [
        "1. 启动系统:",
        "   python multi_channel_area_alarm_detector_fixed.py",
        "",
        "2. 启动Pro1测试模式:",
        "   点击界面上的 '🎬 Pro1视频测试模式' 按钮",
        "",
        "3. 观察测试效果:",
        "   - 通道1,3,5,7,9,11,13,15: 使用1.mp4",
        "   - 通道2,4,6,8,10,12,14,16: 使用2.mp4",
        "   - 每个通道显示播放进度百分比",
        "   - 视频播放完毕自动循环重新开始",
        "",
        "4. 测试功能验证:",
        "   ✅ 烟雾检测: 观察视频中的烟雾识别",
        "   ✅ 面积警报: 检测面积>5cm²时触发警报",
        "   ✅ 时间区域忽略: 左上角时间标记被屏蔽",
        "   ✅ 不重复报警: 同一事件不重复播放警报音",
        "   ✅ 循环播放: 视频自动循环播放",
        "   ✅ 警报音: 播放1.mp3文件",
        "   ✅ 图像保存: 警报图像保存到detection_results/",
        "",
        "5. 控制操作:",
        "   - '全部停止警报': 停止所有通道警报音",
        "   - 窗口关闭: 自动清理所有资源",
        "",
        "6. 状态观察:",
        "   - 绿色通道号: 正常状态",
        "   - 红色通道号: 警报状态",
        "   - 'LOCAL VIDEO (Loop)': 本地视频循环标识",
        "   - 播放进度: 实时显示百分比"
    ]
    
    for instruction in instructions:
        print(instruction)

def show_expected_output():
    """显示预期输出"""
    print("\n📋 预期控制台输出:")
    print("=" * 50)
    
    expected_output = [
        "🎬 启动Pro1视频测试模式...",
        "   ✅ 检测到 1.mp4 (45.2MB)",
        "   ✅ 检测到 2.mp4 (38.7MB)",
        "🔄 使用2个Pro1视频文件启动16路测试...",
        "   通道 1: 1.mp4 -> Pro1测试视频A-通道1",
        "   通道 2: 2.mp4 -> Pro1测试视频B-通道2",
        "   通道 3: 1.mp4 -> Pro1测试视频A-通道3",
        "   通道 4: 2.mp4 -> Pro1测试视频B-通道4",
        "   ...",
        "   通道16: 2.mp4 -> Pro1测试视频B-通道16",
        "",
        "✅ Pro1测试模式启动完成，已启动 16 个通道",
        "📋 测试功能说明:",
        "   - 所有检测功能与实时视频完全相同",
        "   - 面积阈值警报：5平方厘米",
        "   - 时间区域忽略：左上角时间标记",
        "   - 不重复报警：智能警报管理",
        "   - 循环播放：视频播放完毕自动重新开始",
        "   - 警报音：1.mp3文件",
        "   - 图像保存：detection_results文件夹",
        "",
        "📹 本地视频信息: 1500帧, 25.00fps",
        "✅ 通道0(Pro1测试视频A-通道1)初始化完成",
        "📹 本地视频信息: 1200帧, 30.00fps",
        "✅ 通道1(Pro1测试视频B-通道2)初始化完成",
        "",
        "🔄 通道0本地视频播放完毕，重新开始循环播放",
        "🔄 通道1本地视频播放完毕，重新开始循环播放"
    ]
    
    for line in expected_output:
        print(line)

def show_test_checklist():
    """显示测试检查清单"""
    print("\n✅ 测试检查清单:")
    print("=" * 50)
    
    checklist = [
        "□ 系统启动成功",
        "□ Pro1测试模式按钮可见",
        "□ 点击按钮后检测到1.mp4和2.mp4",
        "□ 16个通道全部启动成功",
        "□ 视频A和视频B正确分配到对应通道",
        "□ 每个通道显示播放进度",
        "□ 视频播放流畅无卡顿",
        "□ 循环播放功能正常",
        "□ 'LOCAL VIDEO (Loop)'标识显示",
        "□ 烟雾检测功能正常",
        "□ 面积计算和显示正确",
        "□ 警报触发功能正常",
        "□ 1.mp3警报音播放正常",
        "□ 不重复报警功能正常",
        "□ 警报图像保存功能正常",
        "□ 停止警报按钮功能正常",
        "□ 时间区域忽略功能正常",
        "□ 通道状态指示正确",
        "□ 系统资源占用合理",
        "□ 长时间运行稳定"
    ]
    
    for item in checklist:
        print(item)

def create_test_report_template():
    """创建测试报告模板"""
    report_content = """# Pro1视频测试报告

## 测试环境
- 测试时间: ___________
- 系统版本: 16路实时监控面积警报烟雾检测系统 V2.0
- 测试视频: 1.mp4, 2.mp4
- 警报音文件: 1.mp3

## 文件检查
- [ ] 1.mp4 存在且大小正常
- [ ] 2.mp4 存在且大小正常
- [ ] 1.mp3 存在且大小正常
- [ ] 主程序文件存在

## 功能测试

### 1. 启动测试
- [ ] 系统正常启动
- [ ] Pro1测试模式按钮可见
- [ ] 点击按钮后正常检测视频文件

### 2. 通道分配测试
- [ ] 16个通道全部启动
- [ ] 奇数通道使用1.mp4
- [ ] 偶数通道使用2.mp4
- [ ] 通道名称正确显示

### 3. 播放功能测试
- [ ] 视频播放流畅
- [ ] 播放进度正确显示
- [ ] 循环播放功能正常
- [ ] 'LOCAL VIDEO (Loop)'标识显示

### 4. 检测功能测试
- [ ] 烟雾检测算法正常
- [ ] 面积计算准确
- [ ] 时间区域正确忽略
- [ ] 检测框颜色正确

### 5. 警报功能测试
- [ ] 面积>5cm²时触发警报
- [ ] 1.mp3警报音正常播放
- [ ] 通道状态变红
- [ ] 警报图像自动保存

### 6. 不重复报警测试
- [ ] 同一事件不重复播放警报音
- [ ] 烟雾消失时自动停止警报
- [ ] 超时自动停止功能正常

### 7. 控制功能测试
- [ ] 停止警报按钮功能正常
- [ ] 系统关闭时资源清理正常

## 性能测试
- CPU使用率: _____%
- 内存使用量: _____MB
- 平均FPS: _____
- 长时间运行稳定性: [ ] 正常 [ ] 异常

## 问题记录
1. ________________________________
2. ________________________________
3. ________________________________

## 测试结论
- [ ] 测试通过
- [ ] 测试失败

## 备注
_________________________________
_________________________________
"""
    
    try:
        with open('Pro1测试报告模板.md', 'w', encoding='utf-8') as f:
            f.write(report_content)
        print("\n📝 已创建测试报告模板: Pro1测试报告模板.md")
    except Exception as e:
        print(f"\n⚠️ 创建测试报告模板失败: {e}")

def main():
    """主函数"""
    print("🧪 Pro1视频测试脚本")
    print("=" * 50)
    
    # 检查Pro1视频文件
    video_info = check_pro1_videos()
    
    # 检查其他文件
    check_other_files()
    
    # 显示测试说明
    show_test_instructions()
    
    # 显示预期输出
    show_expected_output()
    
    # 显示测试清单
    show_test_checklist()
    
    # 创建测试报告模板
    create_test_report_template()
    
    # 检查是否可以启动测试
    available_videos = [v for v in video_info if v['exists']]
    
    print(f"\n🎯 测试准备状态:")
    print("=" * 50)
    
    if len(available_videos) >= 1:
        print(f"✅ 发现 {len(available_videos)} 个Pro1视频文件，可以进行测试")
        print("📋 建议测试流程:")
        print("1. 运行主程序: python multi_channel_area_alarm_detector_fixed.py")
        print("2. 点击 'Pro1视频测试模式' 按钮")
        print("3. 观察16路视频播放效果")
        print("4. 测试各项检测和警报功能")
        print("5. 填写测试报告: Pro1测试报告模板.md")
        
        try:
            choice = input("\n是否现在启动Pro1测试? (y/n): ").strip().lower()
            if choice in ['y', 'yes', '是']:
                print("正在启动Pro1测试系统...")
                os.system('python multi_channel_area_alarm_detector_fixed.py')
            else:
                print("测试准备完成，请手动启动测试")
        except KeyboardInterrupt:
            print("\n测试被中断")
    else:
        print("❌ 未找到Pro1视频文件，无法进行测试")
        print("请确保以下文件存在:")
        print("- 1.mp4 (Pro1测试视频A)")
        print("- 2.mp4 (Pro1测试视频B)")
    
    print("\n📋 相关文件:")
    print("- multi_channel_area_alarm_detector_fixed.py  # 主程序")
    print("- test_pro1_videos.py                         # 本测试脚本")
    print("- Pro1视频测试功能说明.md                     # 详细说明")
    print("- Pro1测试报告模板.md                         # 测试报告模板")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复配置文件脚本
"""

import os

def check_video_files():
    """检查可用的视频文件"""
    video_files = []
    test_files = ['1.mp4', '2.mp4', 'test.mp4', 'demo.mp4', 'sample.mp4']
    
    print("🔍 检查可用的视频文件:")
    for file in test_files:
        if os.path.exists(file):
            size_mb = os.path.getsize(file) / (1024*1024)
            video_files.append(file)
            print(f"   ✅ {file} ({size_mb:.1f}MB)")
        else:
            print(f"   ❌ {file} (不存在)")
    
    return video_files

def create_working_config():
    """创建可工作的配置文件"""
    print("\n🔧 创建可工作的配置文件...")
    
    # 检查视频文件
    video_files = check_video_files()
    
    config_lines = []
    
    if video_files:
        print(f"📹 使用{len(video_files)}个本地视频文件配置16路监控")
        
        # 使用本地视频文件
        for i in range(16):
            video_file = video_files[i % len(video_files)]
            config_lines.append(video_file)
            config_lines.append(f"本地视频{i+1}({video_file})")
            print(f"   通道{i+1:2d}: {video_file}")
    else:
        print("⚠️ 未找到本地视频文件，创建RTSP配置模板")
        
        # 创建RTSP配置模板
        for i in range(16):
            rtsp_url = f"rtsp://admin:admin@192.168.1.{200+i}:554/Streaming/Channels/101"
            config_lines.append(rtsp_url)
            config_lines.append(f"监控相机{i+1}")
            print(f"   通道{i+1:2d}: {rtsp_url}")
    
    # 写入配置文件
    try:
        with open('video_paths.txt', 'w', encoding='utf-8') as f:
            for line in config_lines:
                f.write(line + '\n')
        
        print(f"✅ 配置文件已创建: video_paths.txt")
        print(f"   总行数: {len(config_lines)}")
        print(f"   通道数: {len(config_lines) // 2}")
        
        return True
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return False

def test_video_connection():
    """测试视频连接"""
    print("\n🧪 测试视频连接...")
    
    try:
        with open('video_paths.txt', 'r', encoding='utf-8') as f:
            lines = [line.strip() for line in f.readlines() if line.strip()]
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False
    
    if len(lines) < 2:
        print("❌ 配置文件格式错误，行数不足")
        return False
    
    # 测试第一个通道
    video_path = lines[0]
    channel_name = lines[1]
    
    print(f"📹 测试通道1: {channel_name}")
    print(f"   路径: {video_path}")
    
    import cv2
    cap = cv2.VideoCapture(video_path)
    
    if cap.isOpened():
        ret, frame = cap.read()
        if ret and frame is not None:
            h, w = frame.shape[:2]
            print(f"   ✅ 连接成功，分辨率: {w}x{h}")
            cap.release()
            return True
        else:
            print(f"   ❌ 无法读取帧")
    else:
        print(f"   ❌ 无法打开视频源")
    
    cap.release()
    return False

def main():
    """主函数"""
    print("🔧 修复16路监控系统配置文件")
    print("=" * 50)
    
    # 1. 检查当前配置
    print("📋 检查当前配置:")
    try:
        with open('video_paths.txt', 'r', encoding='utf-8') as f:
            lines = [line.strip() for line in f.readlines() if line.strip()]
        print(f"   当前配置行数: {len(lines)}")
        print(f"   识别通道数: {len(lines) // 2}")
        
        if len(lines) < 32:  # 16通道需要32行
            print("   ⚠️ 配置不完整，需要修复")
            need_fix = True
        else:
            print("   ✅ 配置完整")
            need_fix = False
    except:
        print("   ❌ 配置文件不存在或损坏")
        need_fix = True
    
    # 2. 修复配置
    if need_fix:
        if create_working_config():
            print("\n✅ 配置文件修复完成")
        else:
            print("\n❌ 配置文件修复失败")
            return
    
    # 3. 测试连接
    if test_video_connection():
        print("\n✅ 视频连接测试通过")
        
        print("\n🚀 现在可以启动系统:")
        print("   python multi_channel_area_alarm_detector_fixed.py")
        
        # 询问是否启动
        try:
            choice = input("\n是否现在启动系统? (y/n): ").strip().lower()
            if choice in ['y', 'yes', '是']:
                print("🚀 启动系统...")
                os.system('python multi_channel_area_alarm_detector_fixed.py')
        except KeyboardInterrupt:
            print("\n修复完成")
    else:
        print("\n⚠️ 视频连接测试失败")
        print("📋 建议:")
        print("   1. 检查视频文件是否存在")
        print("   2. 检查RTSP地址是否正确")
        print("   3. 检查网络连接")

if __name__ == "__main__":
    main()

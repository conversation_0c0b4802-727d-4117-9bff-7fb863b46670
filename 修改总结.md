# multi_channel_area_alarm_detector_fixed.py 功能修改总结

## 修改目标
根据用户要求，实现以下功能：
1. **后3帧烟雾浓度比较逻辑**：当后3帧的图像均比当前图像的烟雾浓度大时，发出报警
2. **报警持续时间控制**：每次报警持续5秒
3. **防重复报警**：若当前已存在报警音，则不重复报警
4. **界面提示**：在界面左下角提示当前某某通道发出烟雾警报，直到下次报警更新提示
5. **日志记录和图像保存**：写入日志，图像保存到detection_results文件夹

## 主要修改内容

### 1. AreaAlarmVideoThread类修改

#### 1.1 添加新信号
```python
alarm_status_signal = pyqtSignal(int, str, bool)  # 通道号, 通道名称, 是否报警
```

#### 1.2 添加帧历史缓存机制
```python
# 帧历史缓存（用于后3帧比较逻辑）
self.frame_history = []  # 存储最近4帧的烟雾浓度数据
self.max_history_frames = 4  # 保存最近4帧（当前帧+后3帧）

# 全局报警音控制（防止多通道同时播放）
self.global_alarm_playing = False
```

#### 1.3 修改报警持续时间
```python
self.max_alarm_duration = 5.0  # 最大警报持续时间（5秒）
```

#### 1.4 新增烟雾浓度计算方法
```python
def calculate_smoke_density(self, total_area, contour_count):
    """计算烟雾浓度"""
    # 综合考虑面积和轮廓数量来计算烟雾浓度
    area_density = total_area / (self.video_width * self.video_height)  # 面积密度
    contour_density = contour_count / 100.0  # 轮廓密度
    
    # 加权计算总烟雾浓度
    smoke_density = (area_density * 0.7 + contour_density * 0.3) * 100
    return smoke_density
```

#### 1.5 新增帧历史更新方法
```python
def update_frame_history(self, smoke_density):
    """更新帧历史缓存"""
    # 添加当前帧的烟雾浓度
    self.frame_history.append(smoke_density)
    
    # 保持历史帧数量不超过最大值
    if len(self.frame_history) > self.max_history_frames:
        self.frame_history.pop(0)  # 移除最旧的帧
```

#### 1.6 修改报警检查逻辑
```python
def check_area_alarm(self, total_area):
    """检查是否需要触发面积警报（基于后3帧比较逻辑）"""
    current_time = time.time()
    
    # 检查是否需要停止当前播放的警报音（5秒后自动停止）
    if self.is_alarm_playing:
        if current_time - self.alarm_start_time >= self.max_alarm_duration:
            self.stop_alarm_sound()
            print(f"⏰ 通道{self.thread_id}警报音已达到5秒持续时间，自动停止")
            return False
    
    # 检查是否有足够的历史帧进行比较（需要至少4帧：当前帧+后3帧）
    if len(self.frame_history) < self.max_history_frames:
        return False
    
    # 获取当前帧和后3帧的烟雾浓度
    current_density = self.frame_history[-1]  # 当前帧（最新的）
    prev_3_densities = self.frame_history[-4:-1]  # 后3帧
    
    # 检查后3帧的烟雾浓度是否均比当前帧大
    all_prev_greater = all(prev_density > current_density for prev_density in prev_3_densities)
    
    # 同时检查面积阈值
    area_threshold_met = total_area >= self.area_threshold_pixels
    
    if all_prev_greater and area_threshold_met:
        # 如果当前没有在播放警报音，且满足冷却时间，才触发新的警报
        if not self.is_alarm_playing and not self.global_alarm_playing:
            if current_time - self.last_alarm_time >= self.alarm_cooldown:
                self.trigger_alarm(total_area, current_density, prev_3_densities)
                self.last_alarm_time = current_time
                return True
    
    return False
```

#### 1.7 修改报警触发方法
```python
def trigger_alarm(self, total_area, current_density, prev_3_densities):
    """触发警报"""
    current_time = time.time()
    self.alarm_triggered = True
    self.is_alarm_playing = True
    self.global_alarm_playing = True  # 设置全局报警状态
    self.alarm_start_time = current_time

    area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels

    # 记录详细的日志信息
    prev_densities_str = ", ".join([f"{d:.2f}" for d in prev_3_densities])
    log_message = (f"🚨 通道{self.thread_id}({self.thread_name}) 烟雾警报! "
                  f"检测面积: {area_cm2:.2f}cm² (阈值: {self.area_threshold_cm2}cm²) "
                  f"当前烟雾浓度: {current_density:.2f}, 后3帧浓度: [{prev_densities_str}]")
    print(log_message)
    self.log_signal.emit(log_message)
    write_log(log_message)

    # 发送警报信号
    self.alarm_signal.emit(self.thread_id, area_cm2)
    
    # 发送界面状态更新信号
    self.alarm_status_signal.emit(self.thread_id, self.thread_name, True)

    # 播放警报音（只有在没有全局播放时才播放）
    if self.alarm_sound and not self.stop_detection_flag and not self.is_alarm_playing_audio():
        try:
            self.alarm_sound.play(-1)  # 循环播放
            print(f"🔊 通道{self.thread_id}开始播放警报音（持续5秒）")
        except Exception as e:
            print(f"⚠️ 通道{self.thread_id}播放警报音失败: {e}")
            self.is_alarm_playing = False
            self.global_alarm_playing = False
```

#### 1.8 修改停止警报方法
```python
def stop_alarm_sound(self):
    """停止警报音"""
    self.is_alarm_playing = False
    self.alarm_triggered = False
    self.global_alarm_playing = False  # 重置全局报警状态
    
    # 发送界面状态更新信号（停止报警）
    self.alarm_status_signal.emit(self.thread_id, self.thread_name, False)

    if HAS_PYGAME and self.alarm_sound:
        try:
            # 只停止这个通道的声音
            pygame.mixer.stop()
            print(f"🔇 通道{self.thread_id}警报音已停止")
        except Exception as e:
            print(f"⚠️ 通道{self.thread_id}停止警报音失败: {e}")
```

#### 1.9 修改图像保存方法
```python
def save_alarm_image(self, frame, total_area):
    """保存警报图像到detection_results文件夹"""
    # 确保detection_results文件夹存在
    main_output_dir = 'detection_results'
    if not os.path.exists(main_output_dir):
        os.makedirs(main_output_dir)
        
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels

    filename = f"ALARM_CH{self.thread_id+1}_{timestamp}_area_{area_cm2:.2f}cm2.jpg"
    filepath = os.path.join(main_output_dir, filename)

    # 在图像上添加警报信息
    alarm_frame = frame.copy()
    alarm_text = f"SMOKE ALARM CH{self.thread_id+1} - Area: {area_cm2:.2f}cm²"
    time_text = f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

    # 添加警报信息到图像上
    cv2.putText(alarm_frame, alarm_text, (50, 100),
               cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 255), 3)
    cv2.putText(alarm_frame, time_text, (50, 140),
               cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)

    cv2.imwrite(filepath, alarm_frame)
    print(f"💾 通道{self.thread_id+1}警报图像已保存到detection_results文件夹: {filename}")

    return filepath
```

#### 1.10 添加静音和恢复方法
```python
def mute_alarm(self):
    """静音警报"""
    self.stop_detection_flag = True
    if self.alarm_triggered and self.alarm_sound:
        self.stop_alarm_sound()

def resume_detection(self):
    """恢复检测"""
    self.stop_detection_flag = False
```

### 2. MainWindow类修改

#### 2.1 添加报警状态标签
```python
# 添加报警提示标签（左下角）
self.alarm_status_label = QLabel("系统状态: 正常监控中")
self.alarm_status_label.setStyleSheet("""
    QLabel {
        color: #2ecc71;
        font-size: 18px;
        font-weight: bold;
        padding: 10px;
        background-color: rgba(52, 73, 94, 0.8);
        border-radius: 5px;
        border: 2px solid #2ecc71;
    }
""")
main_layout.addWidget(self.alarm_status_label)
```

#### 2.2 添加报警状态更新方法
```python
def update_alarm_status(self, channel_id, channel_name, is_alarm):
    """更新界面左下角的报警状态提示"""
    if is_alarm:
        # 报警状态
        self.alarm_status_label.setText(f"🚨 警报: 通道{channel_id+1}({channel_name}) 检测到烟雾警报！")
        self.alarm_status_label.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                font-size: 18px;
                font-weight: bold;
                padding: 10px;
                background-color: rgba(231, 76, 60, 0.2);
                border-radius: 5px;
                border: 2px solid #e74c3c;
            }
        """)
    else:
        # 恢复正常状态
        self.alarm_status_label.setText("系统状态: 正常监控中")
        self.alarm_status_label.setStyleSheet("""
            QLabel {
                color: #2ecc71;
                font-size: 18px;
                font-weight: bold;
                padding: 10px;
                background-color: rgba(52, 73, 94, 0.8);
                border-radius: 5px;
                border: 2px solid #2ecc71;
            }
        """)
```

#### 2.3 连接新信号
```python
processor.alarm_status_signal.connect(self.update_alarm_status)
```

## 功能实现总结

✅ **后3帧烟雾浓度比较逻辑**：实现了帧历史缓存机制，保存最近4帧的烟雾浓度数据，当后3帧均比当前帧浓度大时触发报警

✅ **报警持续时间控制**：每次报警持续5秒后自动停止

✅ **防重复报警**：通过global_alarm_playing标志防止多通道同时播放警报音

✅ **界面提示**：在界面左下角显示当前报警通道信息，报警状态会实时更新

✅ **日志记录**：详细记录报警信息，包括烟雾浓度数据

✅ **图像保存**：确保警报图像保存到detection_results文件夹，并在图像上添加警报信息

## 使用说明

1. 运行修改后的程序，系统会自动创建detection_results文件夹
2. 当检测到烟雾时，系统会：
   - 检查后3帧是否均比当前帧烟雾浓度大
   - 满足条件时触发5秒警报音
   - 在界面左下角显示报警信息
   - 保存警报图像到detection_results文件夹
   - 记录详细日志信息
3. 报警会自动在5秒后停止，防止重复播放
4. 可以通过界面按钮手动停止报警

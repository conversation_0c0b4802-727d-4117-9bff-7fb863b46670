#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频分析工具 - 分析1.mp4和2.mp4文件
Video Analyzer Tool for analyzing 1.mp4 and 2.mp4 files
"""

import os
import sys
from datetime import datetime
import json

# 添加CUDA路径
try:
    os.add_dll_directory(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin')
    os.add_dll_directory(r'E:\JSZK\DICHENG\opencv_contrib_cuda_4.6.0.20221106_win_amd64\install\x64\vc17\bin')
except:
    pass

import cv2
import numpy as np

try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False
    print("matplotlib未安装，将跳过图表生成")

class VideoAnalyzer:
    """视频分析类"""
    
    def __init__(self, video_path):
        self.video_path = video_path
        self.cap = cv2.VideoCapture(video_path)
        self.video_info = {}
        self.frame_samples = []
        
    def get_video_info(self):
        """获取视频基本信息"""
        if not self.cap.isOpened():
            print(f"无法打开视频文件: {self.video_path}")
            return None
            
        self.video_info = {
            'file_path': self.video_path,
            'file_size_mb': os.path.getsize(self.video_path) / (1024 * 1024),
            'frame_count': int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT)),
            'fps': self.cap.get(cv2.CAP_PROP_FPS),
            'width': int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            'height': int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
            'duration_seconds': 0,
            'codec': int(self.cap.get(cv2.CAP_PROP_FOURCC))
        }
        
        if self.video_info['fps'] > 0:
            self.video_info['duration_seconds'] = self.video_info['frame_count'] / self.video_info['fps']
            
        return self.video_info
    
    def sample_frames(self, num_samples=10):
        """采样视频帧进行分析"""
        if not self.cap.isOpened():
            return []
            
        frame_count = self.video_info.get('frame_count', 0)
        if frame_count == 0:
            return []
            
        # 均匀采样
        sample_indices = np.linspace(0, frame_count-1, num_samples, dtype=int)
        
        for i, frame_idx in enumerate(sample_indices):
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = self.cap.read()
            
            if ret:
                # 分析帧的特征
                frame_analysis = self.analyze_frame(frame, frame_idx)
                self.frame_samples.append(frame_analysis)
                
                # 保存采样帧
                sample_filename = f"sample_{os.path.basename(self.video_path)}_{i:02d}.jpg"
                cv2.imwrite(sample_filename, frame)
                
        return self.frame_samples
    
    def analyze_frame(self, frame, frame_idx):
        """分析单帧特征"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 计算基本统计信息
        mean_brightness = np.mean(gray)
        std_brightness = np.std(gray)
        
        # 计算直方图
        hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
        
        # 检测边缘
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
        
        # 检测运动区域（如果有前一帧）
        motion_score = 0
        if hasattr(self, 'prev_frame'):
            diff = cv2.absdiff(gray, self.prev_frame)
            motion_score = np.mean(diff)
            
        self.prev_frame = gray.copy()
        
        return {
            'frame_index': int(frame_idx),
            'mean_brightness': float(mean_brightness),
            'std_brightness': float(std_brightness),
            'edge_density': float(edge_density),
            'motion_score': float(motion_score),
            'histogram': [float(x) for x in hist.flatten()]
        }
    
    def detect_potential_smoke_areas(self, frame):
        """检测潜在的烟雾区域"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 烟雾通常具有以下特征：
        # 1. 中等亮度值
        # 2. 模糊边缘
        # 3. 不规则形状
        # 4. 运动特征
        
        # 亮度过滤
        brightness_mask = cv2.inRange(gray, 80, 200)
        
        # 高斯模糊检测
        blurred = cv2.GaussianBlur(gray, (15, 15), 0)
        blur_diff = cv2.absdiff(gray, blurred)
        blur_mask = blur_diff < 30  # 烟雾区域模糊程度高
        
        # 结合掩码
        potential_smoke = cv2.bitwise_and(brightness_mask, brightness_mask, mask=blur_mask.astype(np.uint8) * 255)
        
        # 形态学操作
        kernel = np.ones((5, 5), np.uint8)
        potential_smoke = cv2.morphologyEx(potential_smoke, cv2.MORPH_CLOSE, kernel)
        potential_smoke = cv2.morphologyEx(potential_smoke, cv2.MORPH_OPEN, kernel)
        
        return potential_smoke
    
    def generate_report(self):
        """生成分析报告"""
        if not self.video_info:
            return "无视频信息"
            
        report = f"""
=== 视频分析报告 ===
文件: {self.video_info['file_path']}
文件大小: {self.video_info['file_size_mb']:.2f} MB
分辨率: {self.video_info['width']} x {self.video_info['height']}
帧率: {self.video_info['fps']:.2f} FPS
总帧数: {self.video_info['frame_count']}
时长: {self.video_info['duration_seconds']:.2f} 秒

=== 帧分析统计 ===
采样帧数: {len(self.frame_samples)}
"""
        
        if self.frame_samples:
            brightnesses = [f['mean_brightness'] for f in self.frame_samples]
            edge_densities = [f['edge_density'] for f in self.frame_samples]
            motion_scores = [f['motion_score'] for f in self.frame_samples]
            
            report += f"""
平均亮度: {np.mean(brightnesses):.2f} ± {np.std(brightnesses):.2f}
边缘密度: {np.mean(edge_densities):.4f} ± {np.std(edge_densities):.4f}
运动评分: {np.mean(motion_scores):.2f} ± {np.std(motion_scores):.2f}

=== 烟雾检测建议 ===
"""
            
            # 基于分析结果给出建议
            avg_brightness = np.mean(brightnesses)
            avg_motion = np.mean(motion_scores)
            
            if 80 <= avg_brightness <= 180:
                report += "✓ 亮度范围适合烟雾检测\n"
            else:
                report += "⚠ 亮度可能影响烟雾检测效果\n"
                
            if avg_motion > 5:
                report += "✓ 检测到运动，适合光流分析\n"
            else:
                report += "⚠ 运动较少，建议使用静态检测方法\n"
                
        return report
    
    def save_analysis(self, output_file):
        """保存分析结果到JSON文件"""
        analysis_data = {
            'video_info': self.video_info,
            'frame_samples': self.frame_samples,
            'analysis_time': datetime.now().isoformat(),
            'report': self.generate_report()
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, ensure_ascii=False, indent=2)
    
    def __del__(self):
        """释放资源"""
        if hasattr(self, 'cap') and self.cap.isOpened():
            self.cap.release()


def analyze_videos():
    """分析1.mp4和2.mp4文件"""
    video_files = ['1.mp4', '2.mp4']
    
    for video_file in video_files:
        if os.path.exists(video_file):
            print(f"\n正在分析 {video_file}...")
            
            analyzer = VideoAnalyzer(video_file)
            
            # 获取视频信息
            video_info = analyzer.get_video_info()
            if video_info:
                print(f"视频信息获取成功: {video_info['width']}x{video_info['height']}, {video_info['fps']:.1f}fps")
                
                # 采样帧分析
                frame_samples = analyzer.sample_frames(10)
                print(f"采样了 {len(frame_samples)} 帧进行分析")
                
                # 生成报告
                report = analyzer.generate_report()
                print(report)
                
                # 保存分析结果
                output_file = f"analysis_{video_file.replace('.mp4', '.json')}"
                analyzer.save_analysis(output_file)
                print(f"分析结果已保存到: {output_file}")
                
            else:
                print(f"无法分析视频文件: {video_file}")
        else:
            print(f"视频文件不存在: {video_file}")


if __name__ == "__main__":
    analyze_videos()

# 烟雾检测系统使用说明

## 系统概述

本系统是一个基于计算机视觉的16路实时烟雾检测系统，具有以下特点：

- **智能检测算法**：基于后3帧烟雾浓度比较的智能报警机制
- **多通道监控**：支持最多16路视频同时监控
- **实时报警**：检测到烟雾时立即触发5秒报警音
- **防重复报警**：智能防止多通道同时播放报警音
- **图像保存**：自动保存报警时的图像到detection_results文件夹
- **详细日志**：记录所有检测和报警信息

## 新功能特性

### 1. 后3帧比较逻辑
- 系统会保存最近4帧的烟雾浓度数据
- 只有当后3帧的烟雾浓度均比当前帧大时，才会触发报警
- 这种机制可以有效减少误报，提高检测准确性

### 2. 5秒报警持续时间
- 每次报警音持续播放5秒后自动停止
- 避免长时间噪音干扰

### 3. 防重复报警机制
- 当某个通道正在播放报警音时，其他通道不会重复播放
- 确保同一时间只有一个报警音在播放

### 4. 界面状态提示
- 在界面左下角实时显示报警状态
- 报警时显示具体的通道信息
- 报警结束后自动恢复正常状态显示

### 5. 增强的图像保存
- 报警图像自动保存到detection_results文件夹
- 图像文件名包含通道号、时间戳和检测面积信息
- 在保存的图像上添加报警信息标注

## 使用步骤

### 1. 准备工作
1. 确保已安装所需的Python库：
   - OpenCV (cv2)
   - PyQt5
   - pygame
   - numpy

2. 准备视频源配置文件 `video_paths.txt`：
   ```
   rtsp://camera1_address
   摄像头1
   rtsp://camera2_address
   摄像头2
   ...
   ```

3. 准备报警音文件 `1.mp3`（放在程序同目录下）

### 2. 启动系统
```bash
python multi_channel_area_alarm_detector_fixed.py
```

### 3. 操作界面

#### 主要按钮功能：
- **开始检测**：启动所有通道的烟雾检测
- **停止报警**：停止所有通道的报警音
- **查看报警图片**：打开detection_results文件夹查看保存的报警图像
- **管理信号源**：编辑video_paths.txt文件
- **重新初始化**：重新加载配置并重启系统
- **查看日志**：查看系统运行日志

#### 参数调整：
- **检测间隔(帧)**：设置多少帧检测一次（默认25帧）
- **轮廓阈值**：设置检测敏感度（默认150）

#### 单通道控制：
- 使用下拉菜单选择特定通道
- 点击"暂停报警"可以暂停选中通道的报警

### 4. 状态监控

#### 界面状态指示：
- **绿色**：正常运行
- **红色**：检测到烟雾报警
- **橙色**：报警已暂停
- **灰色**：未启动或错误

#### 左下角状态提示：
- 正常状态：显示"系统状态: 正常监控中"
- 报警状态：显示"🚨 警报: 通道X(名称) 检测到烟雾警报！"

## 报警机制详解

### 触发条件
报警需要同时满足以下条件：
1. **后3帧比较**：后3帧的烟雾浓度均大于当前帧
2. **面积阈值**：检测到的烟雾面积超过设定阈值
3. **冷却时间**：距离上次报警间隔超过3秒
4. **无重复报警**：当前没有其他通道正在播放报警音

### 报警流程
1. 检测到符合条件的烟雾
2. 播放5秒报警音
3. 在界面左下角显示报警信息
4. 保存报警图像到detection_results文件夹
5. 记录详细日志信息
6. 5秒后自动停止报警音并恢复正常状态

### 烟雾浓度计算
烟雾浓度 = (面积密度 × 0.7 + 轮廓密度 × 0.3) × 100

其中：
- 面积密度 = 检测面积 / 视频总面积
- 轮廓密度 = 轮廓数量 / 100

## 文件结构

```
项目目录/
├── multi_channel_area_alarm_detector_fixed.py  # 主程序
├── video_paths.txt                             # 视频源配置
├── 1.mp3                                       # 报警音文件
├── detection_results/                          # 报警图像保存目录
│   ├── ALARM_CH1_20231201_143022_area_15.32cm2.jpg
│   └── ...
├── 16路烟雾检测日志.txt                        # 系统日志
└── 漏袋检测日志.txt                            # 检测日志
```

## 故障排除

### 常见问题

1. **无法播放报警音**
   - 检查1.mp3文件是否存在
   - 确认pygame库已正确安装

2. **视频源连接失败**
   - 检查video_paths.txt文件格式
   - 确认RTSP地址是否正确
   - 检查网络连接

3. **检测不准确**
   - 调整检测间隔和轮廓阈值参数
   - 检查摄像头安装位置和角度

4. **界面显示异常**
   - 检查PyQt5库是否正确安装
   - 确认系统支持GUI显示

### 日志查看
- 点击"查看日志"按钮查看系统运行日志
- 日志文件包含详细的检测和报警信息
- 可以通过日志分析系统运行状态和问题

## 技术支持

如遇到问题，请：
1. 查看系统日志文件
2. 检查控制台输出信息
3. 确认所有依赖库已正确安装
4. 验证视频源配置是否正确

## 更新记录

### v1.0 (当前版本)
- ✅ 实现后3帧烟雾浓度比较逻辑
- ✅ 添加5秒报警持续时间控制
- ✅ 实现防重复报警机制
- ✅ 添加界面左下角状态提示
- ✅ 增强图像保存功能
- ✅ 优化日志记录系统

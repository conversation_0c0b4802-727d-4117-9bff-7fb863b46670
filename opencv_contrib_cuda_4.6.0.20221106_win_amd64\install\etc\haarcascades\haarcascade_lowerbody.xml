<?xml version="1.0"?>
<!--
   19x23 lowerbody detector (see the detailed description below). 

//////////////////////////////////////////////////////////////////////////
| Contributors License Agreement
| IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
|   By downloading, copying, installing or using the software you agree 
|   to this license.
|   If you do not agree to this license, do not download, install,
|   copy or use the software.
|
| Copyright (c) 2004, <PERSON><PERSON> and <PERSON><PERSON> (ETH Zurich, Switzerland).
|  All rights reserved.
|
| Redistribution and use in source and binary forms, with or without
| modification, are permitted provided that the following conditions are
| met:
|
|    * Redistributions of source code must retain the above copyright
|       notice, this list of conditions and the following disclaimer.
|    * Redistributions in binary form must reproduce the above
|      copyright notice, this list of conditions and the following
|      disclaimer in the documentation and/or other materials provided
|      with the distribution.  
|    * The name of Contributor may not used to endorse or promote products 
|      derived from this software without specific prior written permission.
|
| THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
| "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
| LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
| A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
| CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
| EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
| PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
| PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
| LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
| NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
| SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.  Back to
| Top
//////////////////////////////////////////////////////////////////////////

"Haar"-based Detectors For Pedestrian Detection
===============================================
by Hannes Kruppa and Bernt Schiele, ETH Zurich, Switzerland

This archive provides the following three detectors:
- upper body detector (most fun, useful in many scenarios!)
- lower body detector
- full body detector

These detectors have been successfully applied to pedestrian detection
in still images. They can be directly passed as parameters to the
program HaarFaceDetect.
NOTE: These detectors deal with frontal and backside views but not
with side views (also see "Known limitations" below).

RESEARCHERS:
If you are using any of the detectors or involved ideas please cite
this paper (available at www.vision.ethz.ch/publications/):

@InProceedings{Kruppa03-bmvc,
  author =       "Hannes Kruppa, Modesto Castrillon-Santana and Bernt Schiele",
  title =        "Fast and Robust Face Finding via Local Context."
  booktitle =    "Joint IEEE International Workshop on Visual Surveillance and Performance Evaluation of Tracking and Surveillance"
  year =         "2003",
  month =        "October"
}

COMMERCIAL:
If you have any commercial interest in this work please contact 
<EMAIL>


ADDITIONAL INFORMATION 
====================== 
Check out the demo movie, e.g. using mplayer or any (Windows/Linux-) player
that can play back .mpg movies.
Under Linux that's:
> ffplay demo.mpg
or:
> mplayer demo.mpg

The movie shows a person walking towards the camera in a realistic
indoor setting. Using ffplay or mplayer you can pause and continue the
movie by pressing the space bar.

Detections coming from the different detectors are visualized using
different line styles: 
upper body : dotted line
lower body : dashed line
full body  : solid line

You will notice that successful detections containing the target do
not sit tightly on the body but also include some of the background
left and right.  This is not a bug but accurately reflects the
employed training data which also includes portions of the background
to ensure proper silhouette representation. If you want to get a
feeling for the training data check out the CBCL data set:
http://www.ai.mit.edu/projects/cbcl/software-datasets/PedestrianData.html

There is also a small number of false alarms in this sequence.  
NOTE: This is per frame detection, not tracking (which is also one of
the reasons why it is not mislead by the person's shadow on the back
wall). 

On an Intel Xeon 1.7GHz machine the detectors operate at something
between 6Hz to 14 Hz (on 352 x 288 frames per second) depending on the
detector. The detectors work as well on much lower image resolutions
which is always an interesting possibility for speed-ups or
"coarse-to-fine" search strategies.

Additional information e.g. on training parameters, detector
combination, detecting other types of objects (e.g. cars) etc. is
available in my PhD thesis report (available end of June). Check out
www.vision.ethz.ch/kruppa/


KNOWN LIMITATIONS
==================
1) The detectors only support frontal and back views but not sideviews.
   Sideviews are trickier and it makes a lot of sense to include additional
   modalities for their detection, e.g. motion information. I recommend
   Viola and Jones' ICCV 2003 paper if this further interests you.

2) Don't expect these detectors to be as accurate as a frontal face detector.
   A frontal face as a pattern is pretty distinct with respect to other
   patterns occurring in the world (i.e. image "background"). This is not so
   for upper, lower and especially full bodies, because they have to rely
   on fragile silhouette information rather than internal (facial) features.
   Still, we found especially the upper body detector to perform amazingly well.
   In contrast to a face detector these detectors will also work at very low
   image resolutions 

Acknowledgements
================
Thanks to Martin Spengler, ETH Zurich, for providing the demo movie.
-->
<opencv_storage>
<cascade type_id="opencv-cascade-classifier"><stageType>BOOST</stageType>
  <featureType>HAAR</featureType>
  <height>23</height>
  <width>19</width>
  <stageParams>
    <maxWeakCount>89</maxWeakCount></stageParams>
  <featureParams>
    <maxCatCount>0</maxCatCount></featureParams>
  <stageNum>27</stageNum>
  <stages>
    <_>
      <maxWeakCount>17</maxWeakCount>
      <stageThreshold>-1.4308550357818604e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 0 -1.6869869083166122e-02</internalNodes>
          <leafValues>
            5.4657417535781860e-01 -6.3678038120269775e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1 2.5349899660795927e-03</internalNodes>
          <leafValues>
            -3.7605491280555725e-01 3.2378101348876953e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 2 -2.4709459394216537e-02</internalNodes>
          <leafValues>
            -6.7979127168655396e-01 2.0501059293746948e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 3 8.2436859607696533e-02</internalNodes>
          <leafValues>
            2.0588639378547668e-01 -8.4938430786132812e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 4 -8.2128931535407901e-04</internalNodes>
          <leafValues>
            3.1891921162605286e-01 -4.6469458937644958e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 5 2.3016959428787231e-02</internalNodes>
          <leafValues>
            1.8670299649238586e-01 -7.0330899953842163e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 6 6.6386149264872074e-03</internalNodes>
          <leafValues>
            1.6370490193367004e-01 -8.4604722261428833e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 7 7.6682120561599731e-04</internalNodes>
          <leafValues>
            -3.9852690696716309e-01 2.3113329708576202e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 8 1.1731679737567902e-01</internalNodes>
          <leafValues>
            1.0445039719343185e-01 -8.8510942459106445e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 9 1.5421230345964432e-02</internalNodes>
          <leafValues>
            -2.7859508991241455e-01 2.8921920061111450e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 10 3.4018948674201965e-02</internalNodes>
          <leafValues>
            -1.4287669956684113e-01 7.7801531553268433e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 11 3.4638870507478714e-02</internalNodes>
          <leafValues>
            1.8644079566001892e-01 -6.0324841737747192e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 12 -3.7503659725189209e-01</internalNodes>
          <leafValues>
            9.2781841754913330e-01 -1.5421600639820099e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 13 -5.6011971086263657e-02</internalNodes>
          <leafValues>
            -5.8591067790985107e-01 1.9547510147094727e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 14 -1.4878909569233656e-03</internalNodes>
          <leafValues>
            2.8139349818229675e-01 -4.1853010654449463e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 15 -1.4495699666440487e-02</internalNodes>
          <leafValues>
            -7.2273969650268555e-01 9.4288460910320282e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 16 -5.6178281083703041e-03</internalNodes>
          <leafValues>
            -5.9551960229873657e-01 1.5202650427818298e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>13</maxWeakCount>
      <stageThreshold>-1.1907930374145508e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 17 -3.1839120201766491e-03</internalNodes>
          <leafValues>
            4.0025138854980469e-01 -6.8473160266876221e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 18 3.5989920143038034e-03</internalNodes>
          <leafValues>
            -5.1895952224731445e-01 3.0101141333580017e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 19 1.8804630264639854e-02</internalNodes>
          <leafValues>
            1.5554919838905334e-01 -8.0477172136306763e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 20 5.2497140131890774e-03</internalNodes>
          <leafValues>
            1.3780809938907623e-01 -6.0767507553100586e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 21 -1.4204799663275480e-03</internalNodes>
          <leafValues>
            3.2319429516792297e-01 -4.3407461047172546e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 22 -2.5174349546432495e-02</internalNodes>
          <leafValues>
            -7.0780879259109497e-01 9.3106329441070557e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 23 3.2285219058394432e-03</internalNodes>
          <leafValues>
            -3.2510471343994141e-01 3.3571699261665344e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 24 9.4993412494659424e-02</internalNodes>
          <leafValues>
            8.2439087331295013e-02 -8.7549537420272827e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 25 -6.5919090993702412e-03</internalNodes>
          <leafValues>
            -7.3804199695587158e-01 1.3853749632835388e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 26 -1.1146620381623507e-03</internalNodes>
          <leafValues>
            1.7917269468307495e-01 -2.7955859899520874e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 27 1.3349019922316074e-02</internalNodes>
          <leafValues>
            1.3057829439640045e-01 -6.9802671670913696e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 28 -3.5181451588869095e-02</internalNodes>
          <leafValues>
            4.6535360813140869e-01 -1.0698779672384262e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 29 3.1874589622020721e-02</internalNodes>
          <leafValues>
            -1.3565389811992645e-01 7.9047888517379761e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>19</maxWeakCount>
      <stageThreshold>-1.3129220008850098e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 30 -1.0647430084645748e-02</internalNodes>
          <leafValues>
            3.8079029321670532e-01 -5.8672338724136353e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 31 -7.3214493691921234e-02</internalNodes>
          <leafValues>
            -7.9550951719284058e-01 1.7223259806632996e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 32 6.0464427806437016e-03</internalNodes>
          <leafValues>
            1.6532160341739655e-01 -6.9376647472381592e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 33 7.3225022060796618e-04</internalNodes>
          <leafValues>
            -3.3247160911560059e-01 2.3669970035552979e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 34 -1.0990080423653126e-02</internalNodes>
          <leafValues>
            -6.9136887788772583e-01 2.1058270335197449e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 35 -1.5282750246115029e-04</internalNodes>
          <leafValues>
            2.0305849611759186e-01 -4.6551659703254700e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 36 2.4822261184453964e-04</internalNodes>
          <leafValues>
            -4.2122921347618103e-01 2.7335309982299805e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 37 -8.4205856546759605e-03</internalNodes>
          <leafValues>
            -4.3744468688964844e-01 5.8831848204135895e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 38 -3.6992791295051575e-01</internalNodes>
          <leafValues>
            9.1070818901062012e-01 -8.7207540869712830e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 39 6.1259930953383446e-03</internalNodes>
          <leafValues>
            1.1886730045080185e-01 -1.8520170450210571e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 40 -6.0144090093672276e-03</internalNodes>
          <leafValues>
            -6.3057059049606323e-01 1.4577180147171021e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 41 8.5623031482100487e-03</internalNodes>
          <leafValues>
            -2.9369381070137024e-01 3.2411348819732666e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 42 -1.3966850005090237e-02</internalNodes>
          <leafValues>
            -8.0650371313095093e-01 1.1267790198326111e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 43 -4.1734468191862106e-02</internalNodes>
          <leafValues>
            7.7495330572128296e-01 -7.8866302967071533e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 44 -2.7996799326501787e-04</internalNodes>
          <leafValues>
            2.7783310413360596e-01 -3.5196089744567871e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 45 1.9588569179177284e-02</internalNodes>
          <leafValues>
            -6.5759636461734772e-02 5.2414137125015259e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 46 9.2163113877177238e-03</internalNodes>
          <leafValues>
            -1.5525479614734650e-01 5.4835391044616699e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 47 -2.1458569914102554e-02</internalNodes>
          <leafValues>
            -5.2255308628082275e-01 8.2208268344402313e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 48 3.6805770359933376e-03</internalNodes>
          <leafValues>
            -2.4434129893779755e-01 3.6122488975524902e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>23</maxWeakCount>
      <stageThreshold>-1.3777279853820801e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 49 -8.3544738590717316e-03</internalNodes>
          <leafValues>
            2.8173181414604187e-01 -4.9728131294250488e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 50 -5.5724289268255234e-03</internalNodes>
          <leafValues>
            -6.5505301952362061e-01 1.9406059384346008e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 51 -5.7714767754077911e-03</internalNodes>
          <leafValues>
            -6.2230938673019409e-01 2.7622398734092712e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 52 2.2995889186859131e-02</internalNodes>
          <leafValues>
            1.9798569381237030e-02 -7.8324538469314575e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 53 -1.1443760013207793e-03</internalNodes>
          <leafValues>
            2.8108718991279602e-01 -4.8214849829673767e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 54 -2.5917509198188782e-01</internalNodes>
          <leafValues>
            -6.8214958906173706e-01 -3.3729869755916297e-04</leafValues></_>
        <_>
          <internalNodes>
            0 -1 55 -3.0133039690554142e-03</internalNodes>
          <leafValues>
            -6.5704411268234253e-01 1.3693599402904510e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 56 5.4540671408176422e-03</internalNodes>
          <leafValues>
            8.6931817233562469e-02 -7.0567971467971802e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 57 6.6230311058461666e-03</internalNodes>
          <leafValues>
            1.6634289920330048e-01 -5.1772958040237427e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 58 -1.2561669573187828e-02</internalNodes>
          <leafValues>
            9.0290471911430359e-02 -1.6850970685482025e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 59 4.2890738695859909e-02</internalNodes>
          <leafValues>
            1.2977810204029083e-01 -5.8218061923980713e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 60 -1.3341030571609735e-03</internalNodes>
          <leafValues>
            1.3694329559803009e-01 -1.9437809288501740e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 61 -4.1247460991144180e-02</internalNodes>
          <leafValues>
            6.8543851375579834e-01 -1.3039450347423553e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 62 -9.1503392904996872e-03</internalNodes>
          <leafValues>
            -1.1895430088043213e-01 6.7576698958873749e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 63 -1.7151240026578307e-03</internalNodes>
          <leafValues>
            2.6475539803504944e-01 -3.0487450957298279e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 64 2.0843200385570526e-01</internalNodes>
          <leafValues>
            1.2401489913463593e-01 -4.7014111280441284e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 65 7.2393968701362610e-02</internalNodes>
          <leafValues>
            9.6924379467964172e-02 -7.7347749471664429e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 66 -1.5335980569943786e-03</internalNodes>
          <leafValues>
            1.7991219460964203e-01 -2.5788331031799316e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 67 4.8640500754117966e-03</internalNodes>
          <leafValues>
            1.1392980068922043e-01 -5.5173867940902710e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 68 -1.6523050144314766e-03</internalNodes>
          <leafValues>
            1.5154689550399780e-01 -2.2901679575443268e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 69 7.5348757207393646e-02</internalNodes>
          <leafValues>
            -1.4630889892578125e-01 6.8105882406234741e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 70 -8.2630068063735962e-03</internalNodes>
          <leafValues>
            -7.2783601284027100e-01 1.0281019657850266e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 71 -5.5124741047620773e-03</internalNodes>
          <leafValues>
            -6.3059347867965698e-01 9.3257799744606018e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>15</maxWeakCount>
      <stageThreshold>-1.0618749856948853e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 72 -9.3849105760455132e-03</internalNodes>
          <leafValues>
            5.2500581741333008e-01 -4.3231061100959778e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 73 -1.3772470410913229e-03</internalNodes>
          <leafValues>
            2.0698480308055878e-01 -4.2718759179115295e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 74 2.6320109143853188e-02</internalNodes>
          <leafValues>
            1.5825170278549194e-01 -6.5509521961212158e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 75 -4.5488759875297546e-02</internalNodes>
          <leafValues>
            -4.9510109424591064e-01 1.7998820543289185e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 76 -4.7006201930344105e-03</internalNodes>
          <leafValues>
            3.3971160650253296e-01 -3.6917701363563538e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 77 -1.3270860072225332e-03</internalNodes>
          <leafValues>
            3.0907860398292542e-01 -1.9771750271320343e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 78 9.3802614137530327e-03</internalNodes>
          <leafValues>
            9.4488449394702911e-02 -7.3198097944259644e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 79 4.3565612286329269e-03</internalNodes>
          <leafValues>
            1.1520200222730637e-01 -5.4008102416992188e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 80 8.1178937107324600e-03</internalNodes>
          <leafValues>
            -1.5956309437751770e-01 5.3777867555618286e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 81 -8.7829083204269409e-03</internalNodes>
          <leafValues>
            5.6634718179702759e-01 -1.3279379904270172e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 82 2.1944850683212280e-02</internalNodes>
          <leafValues>
            1.5901289880275726e-01 -5.1751822233200073e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 83 4.9510098993778229e-02</internalNodes>
          <leafValues>
            1.1067640036344528e-02 -4.9972468614578247e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 84 -2.1175360307097435e-03</internalNodes>
          <leafValues>
            2.6490759849548340e-01 -2.4565629661083221e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 85 1.0379469953477383e-02</internalNodes>
          <leafValues>
            1.2624099850654602e-01 -4.0877240896224976e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 86 2.4977258872240782e-03</internalNodes>
          <leafValues>
            -1.9723020493984222e-01 3.8866749405860901e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>18</maxWeakCount>
      <stageThreshold>-9.5461457967758179e-01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 87 -6.1489548534154892e-03</internalNodes>
          <leafValues>
            4.0187481045722961e-01 -5.2397370338439941e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 88 5.0464540719985962e-02</internalNodes>
          <leafValues>
            1.3049679994583130e-01 -5.8651441335678101e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 89 -5.5906269699335098e-02</internalNodes>
          <leafValues>
            -5.1229542493820190e-01 2.4392889440059662e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 90 1.4281509816646576e-01</internalNodes>
          <leafValues>
            -1.5180160291492939e-02 -6.9593918323516846e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 91 4.1162770241498947e-02</internalNodes>
          <leafValues>
            1.3673730194568634e-01 -6.4158838987350464e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 92 -1.6468750312924385e-02</internalNodes>
          <leafValues>
            2.6339039206504822e-01 -2.2083680331707001e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 93 2.4763140827417374e-02</internalNodes>
          <leafValues>
            1.0897739976644516e-01 -6.5213900804519653e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 94 4.3008858337998390e-03</internalNodes>
          <leafValues>
            -1.8299630284309387e-01 4.3614229559898376e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 95 3.4035290591418743e-03</internalNodes>
          <leafValues>
            -2.4363580346107483e-01 2.8224369883537292e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 96 -2.2210620343685150e-02</internalNodes>
          <leafValues>
            -5.4645758867263794e-01 1.3542969524860382e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 97 -2.6968019083142281e-02</internalNodes>
          <leafValues>
            6.5300947427749634e-01 -1.4297309517860413e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 98 -3.4927908331155777e-02</internalNodes>
          <leafValues>
            -5.2346628904342651e-01 1.0084570199251175e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 99 3.6263581365346909e-02</internalNodes>
          <leafValues>
            1.5110149979591370e-01 -5.4185849428176880e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 100 -3.8526788353919983e-02</internalNodes>
          <leafValues>
            -8.6942279338836670e-01 3.7176769226789474e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 101 2.5399168953299522e-03</internalNodes>
          <leafValues>
            -2.6125881075859070e-01 2.7278441190719604e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 102 -1.2931150384247303e-02</internalNodes>
          <leafValues>
            -4.9501579999923706e-01 9.1383516788482666e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 103 1.1981350369751453e-02</internalNodes>
          <leafValues>
            -1.2059610337018967e-01 6.3848638534545898e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 104 -7.4320413172245026e-02</internalNodes>
          <leafValues>
            4.6591779589653015e-01 -4.0265668183565140e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>14</maxWeakCount>
      <stageThreshold>-1.1777880191802979e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 105 -6.9070039317011833e-03</internalNodes>
          <leafValues>
            4.3197679519653320e-01 -5.1717847585678101e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 106 -8.1628039479255676e-03</internalNodes>
          <leafValues>
            2.7116540074348450e-01 -3.2803410291671753e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 107 1.8852509558200836e-02</internalNodes>
          <leafValues>
            1.5548799932003021e-01 -5.5243927240371704e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 108 3.4079391509294510e-02</internalNodes>
          <leafValues>
            1.5272259712219238e-01 -6.5318012237548828e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 109 -3.2038250938057899e-03</internalNodes>
          <leafValues>
            3.4725460410118103e-01 -2.7734228968620300e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 110 2.1410689223557711e-03</internalNodes>
          <leafValues>
            -6.8888276815414429e-02 2.4079489707946777e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 111 1.4620450139045715e-01</internalNodes>
          <leafValues>
            1.5766879916191101e-01 -5.4515862464904785e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 112 -6.2386798672378063e-03</internalNodes>
          <leafValues>
            3.2899579405784607e-01 -1.6970640420913696e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 113 7.7623138204216957e-03</internalNodes>
          <leafValues>
            1.6352510452270508e-01 -5.1879328489303589e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 114 3.7800080608576536e-03</internalNodes>
          <leafValues>
            -1.8464370071887970e-01 4.8660078644752502e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 115 2.2303969599306583e-03</internalNodes>
          <leafValues>
            -1.7057199776172638e-01 4.7744798660278320e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 116 2.4544890038669109e-03</internalNodes>
          <leafValues>
            -3.3550649881362915e-01 2.5369268655776978e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 117 -2.1707419306039810e-02</internalNodes>
          <leafValues>
            -4.8321890830993652e-01 1.6075029969215393e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 118 1.7421970143914223e-02</internalNodes>
          <leafValues>
            7.9877912998199463e-02 -7.5137257575988770e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>34</maxWeakCount>
      <stageThreshold>-1.2834340333938599e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 119 8.8802073150873184e-03</internalNodes>
          <leafValues>
            -4.4682410359382629e-01 2.6062530279159546e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 120 -3.0198058811947703e-04</internalNodes>
          <leafValues>
            1.5258400142192841e-01 -3.5206508636474609e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 121 6.7998501472175121e-03</internalNodes>
          <leafValues>
            1.2259320169687271e-01 -6.8427437543869019e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 122 2.7802670374512672e-03</internalNodes>
          <leafValues>
            -3.3681631088256836e-01 1.8518559634685516e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 123 -1.1553820222616196e-02</internalNodes>
          <leafValues>
            -6.9871348142623901e-01 1.3079600036144257e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 124 -2.6563290506601334e-02</internalNodes>
          <leafValues>
            -7.0277881622314453e-01 1.7791330814361572e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 125 -2.5158381322398782e-04</internalNodes>
          <leafValues>
            2.4779480695724487e-01 -3.9787930250167847e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 126 3.5748310387134552e-02</internalNodes>
          <leafValues>
            -3.8043439388275146e-02 4.7976261377334595e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 127 -1.9973930902779102e-03</internalNodes>
          <leafValues>
            2.5774869322776794e-01 -3.1990098953247070e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 128 -1.1007110029459000e-01</internalNodes>
          <leafValues>
            -4.9102869629859924e-01 2.3104630410671234e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 129 -2.2225650027394295e-03</internalNodes>
          <leafValues>
            2.3825299739837646e-01 -2.8415530920028687e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 130 -7.7874241396784782e-03</internalNodes>
          <leafValues>
            -3.8951370120048523e-01 5.5762890726327896e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 131 5.6415859609842300e-02</internalNodes>
          <leafValues>
            -9.3521721661090851e-02 7.2561162710189819e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 132 -3.5978010855615139e-03</internalNodes>
          <leafValues>
            1.9452190399169922e-01 -1.9651280343532562e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 133 -7.2716898284852505e-03</internalNodes>
          <leafValues>
            3.4169870615005493e-01 -2.2851559519767761e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 134 7.1941758506000042e-03</internalNodes>
          <leafValues>
            7.2148866951465607e-02 -4.5313501358032227e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 135 -4.1034761816263199e-03</internalNodes>
          <leafValues>
            -5.1336747407913208e-01 1.3323569297790527e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 136 -3.4210970625281334e-03</internalNodes>
          <leafValues>
            -4.2383781075477600e-01 8.4852807223796844e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 137 4.1890922002494335e-03</internalNodes>
          <leafValues>
            -1.3398550450801849e-01 4.3749558925628662e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 138 1.1827970156446099e-03</internalNodes>
          <leafValues>
            -2.9739010334014893e-01 2.2126840054988861e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 139 -4.1196551173925400e-02</internalNodes>
          <leafValues>
            -5.0735759735107422e-01 1.3243959844112396e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 140 2.9593890067189932e-03</internalNodes>
          <leafValues>
            -1.4052620530128479e-01 6.1360880732536316e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 141 -5.0226859748363495e-03</internalNodes>
          <leafValues>
            -4.7495970129966736e-01 1.2069150060415268e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 142 -1.5097860246896744e-02</internalNodes>
          <leafValues>
            2.7555391192436218e-01 -5.3780451416969299e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 143 -2.7190970256924629e-02</internalNodes>
          <leafValues>
            7.5995457172393799e-01 -7.4793189764022827e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 144 1.9893879070878029e-02</internalNodes>
          <leafValues>
            -6.7238640040159225e-03 7.3972767591476440e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 145 7.7208830043673515e-03</internalNodes>
          <leafValues>
            9.3071162700653076e-02 -6.5780252218246460e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 146 -1.1565990280359983e-03</internalNodes>
          <leafValues>
            9.4645917415618896e-02 -1.6407909989356995e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 147 2.6069190353155136e-03</internalNodes>
          <leafValues>
            -1.3877980411052704e-01 4.7349870204925537e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 148 -5.3586110472679138e-02</internalNodes>
          <leafValues>
            -3.7349641323089600e-01 2.5728559121489525e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 149 1.5184599906206131e-03</internalNodes>
          <leafValues>
            -2.2478710114955902e-01 2.3574599623680115e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 150 -3.7061560899019241e-02</internalNodes>
          <leafValues>
            -6.1827117204666138e-01 8.2348063588142395e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 151 -2.6311799883842468e-02</internalNodes>
          <leafValues>
            -6.0057657957077026e-01 7.7768869698047638e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 152 -8.7947428226470947e-02</internalNodes>
          <leafValues>
            3.8841038942337036e-01 -8.1545598804950714e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>20</maxWeakCount>
      <stageThreshold>-1.2891789674758911e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 153 -2.9038030654191971e-02</internalNodes>
          <leafValues>
            5.0635957717895508e-01 -4.3462699651718140e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 154 3.9044669829308987e-03</internalNodes>
          <leafValues>
            -1.9009789824485779e-01 5.1840317249298096e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 155 2.9162769205868244e-03</internalNodes>
          <leafValues>
            -3.4351310133934021e-01 2.4016310274600983e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 156 -8.9670084416866302e-03</internalNodes>
          <leafValues>
            -4.2667150497436523e-01 1.2316550314426422e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 157 -2.4935540277510881e-03</internalNodes>
          <leafValues>
            3.6086550354957581e-01 -1.8381460011005402e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 158 -4.8912568017840385e-03</internalNodes>
          <leafValues>
            -6.4749848842620850e-01 1.0856709629297256e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 159 -4.0970719419419765e-03</internalNodes>
          <leafValues>
            2.2143830358982086e-01 -3.1505578756332397e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 160 4.3956499546766281e-02</internalNodes>
          <leafValues>
            -1.0780169814825058e-01 7.1893501281738281e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 161 1.9277370302006602e-03</internalNodes>
          <leafValues>
            2.0247739553451538e-01 -4.0381088852882385e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 162 9.4976946711540222e-03</internalNodes>
          <leafValues>
            4.3494019657373428e-02 -2.9908061027526855e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 163 3.5389279946684837e-03</internalNodes>
          <leafValues>
            -1.5109489858150482e-01 5.1864242553710938e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 164 -2.2064079530537128e-03</internalNodes>
          <leafValues>
            2.3006440699100494e-01 -3.3191001415252686e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 165 3.9085410535335541e-03</internalNodes>
          <leafValues>
            -3.4253311157226562e-01 2.2951880097389221e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 166 2.6973709464073181e-03</internalNodes>
          <leafValues>
            1.1976680159568787e-01 -3.5321989655494690e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 167 -2.1321459207683802e-03</internalNodes>
          <leafValues>
            1.8206289410591125e-01 -2.8434100747108459e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 168 2.6955150533467531e-03</internalNodes>
          <leafValues>
            7.4593842029571533e-02 -3.0896648764610291e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 169 -6.0222679749131203e-03</internalNodes>
          <leafValues>
            1.8041500449180603e-01 -2.7531668543815613e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 170 -8.9143458753824234e-03</internalNodes>
          <leafValues>
            2.4166099727153778e-01 -1.4506129920482635e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 171 2.3474939167499542e-02</internalNodes>
          <leafValues>
            -1.2354619801044464e-01 6.5625041723251343e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 172 -5.6602950207889080e-03</internalNodes>
          <leafValues>
            -3.3785250782966614e-01 1.1194559931755066e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>20</maxWeakCount>
      <stageThreshold>-1.0202569961547852e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 173 -6.9699093699455261e-02</internalNodes>
          <leafValues>
            5.0786459445953369e-01 -4.7562688589096069e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 174 2.1672779694199562e-02</internalNodes>
          <leafValues>
            -2.9134199023246765e-01 3.4561529755592346e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 175 -4.7600260004401207e-03</internalNodes>
          <leafValues>
            3.6477440595626831e-01 -1.9551509618759155e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 176 -4.6418169513344765e-03</internalNodes>
          <leafValues>
            -5.6445592641830444e-01 9.8486669361591339e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 177 -6.0006938874721527e-03</internalNodes>
          <leafValues>
            -6.3645982742309570e-01 1.4379170536994934e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 178 1.9073469564318657e-02</internalNodes>
          <leafValues>
            -3.4218288958072662e-02 5.5043292045593262e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 179 4.7993380576372147e-02</internalNodes>
          <leafValues>
            -8.5889510810375214e-02 7.6790231466293335e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 180 -3.6511209327727556e-03</internalNodes>
          <leafValues>
            2.0186069607734680e-01 -2.9832679033279419e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 181 -1.4485770370811224e-03</internalNodes>
          <leafValues>
            -5.1293247938156128e-01 1.3695690035820007e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 182 -3.3748829737305641e-03</internalNodes>
          <leafValues>
            -4.0975129604339600e-01 1.1581440269947052e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 183 2.3586750030517578e-03</internalNodes>
          <leafValues>
            1.7582429945468903e-01 -4.5439630746841431e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 184 -2.2074829787015915e-02</internalNodes>
          <leafValues>
            4.6775639057159424e-01 -4.6358831226825714e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 185 7.0953248068690300e-03</internalNodes>
          <leafValues>
            -3.2100531458854675e-01 2.2119350731372833e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 186 -2.0119780674576759e-03</internalNodes>
          <leafValues>
            5.4601740092039108e-02 -9.7853101789951324e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 187 4.9847508780658245e-03</internalNodes>
          <leafValues>
            -1.3063269853591919e-01 5.2815079689025879e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 188 -5.3485459648072720e-03</internalNodes>
          <leafValues>
            -4.2115539312362671e-01 1.1927159875631332e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 189 2.5243330746889114e-03</internalNodes>
          <leafValues>
            1.2105660140514374e-01 -4.5177119970321655e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 190 -2.4893151130527258e-03</internalNodes>
          <leafValues>
            1.2249600142240524e-01 -1.1200980097055435e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 191 4.3740491382777691e-03</internalNodes>
          <leafValues>
            -1.0549320280551910e-01 6.0806149244308472e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 192 -7.3214988224208355e-03</internalNodes>
          <leafValues>
            4.7615110874176025e-01 -6.8390920758247375e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>24</maxWeakCount>
      <stageThreshold>-1.0336159467697144e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 193 -4.2286239564418793e-02</internalNodes>
          <leafValues>
            3.6749860644340515e-01 -4.3680980801582336e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 194 3.8884699344635010e-02</internalNodes>
          <leafValues>
            -3.5438889265060425e-01 2.7009218931198120e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 195 1.5983959892764688e-03</internalNodes>
          <leafValues>
            -3.2200628519058228e-01 2.5404900312423706e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 196 3.9249849505722523e-03</internalNodes>
          <leafValues>
            1.6477300226688385e-01 -4.2043879628181458e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 197 1.5850430354475975e-03</internalNodes>
          <leafValues>
            -2.5503370165824890e-01 3.1559389829635620e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 198 -3.4282119013369083e-03</internalNodes>
          <leafValues>
            -4.0074288845062256e-01 1.1993350088596344e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 199 -3.3538821153342724e-03</internalNodes>
          <leafValues>
            3.0459630489349365e-01 -2.2311030328273773e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 200 -6.7664748057723045e-03</internalNodes>
          <leafValues>
            3.2396519184112549e-01 -9.2932380735874176e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 201 -6.7180307814851403e-04</internalNodes>
          <leafValues>
            -3.2457518577575684e-01 2.1808999776840210e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 202 2.8931829147040844e-03</internalNodes>
          <leafValues>
            1.2530609965324402e-01 -4.8582470417022705e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 203 -3.3115309197455645e-03</internalNodes>
          <leafValues>
            4.0534108877182007e-01 -2.2432869672775269e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 204 8.8509041815996170e-03</internalNodes>
          <leafValues>
            1.2155570089817047e-01 -6.0243481397628784e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 205 5.4662628099322319e-03</internalNodes>
          <leafValues>
            -1.6978119313716888e-01 4.0752619504928589e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 206 4.7559391707181931e-02</internalNodes>
          <leafValues>
            -8.1737041473388672e-02 6.9865119457244873e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 207 3.1745019368827343e-03</internalNodes>
          <leafValues>
            1.7419810593128204e-01 -3.7237030267715454e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 208 -5.1520839333534241e-03</internalNodes>
          <leafValues>
            2.7799358963966370e-01 -2.5311779975891113e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 209 -4.8141111619770527e-03</internalNodes>
          <leafValues>
            -5.8466029167175293e-01 1.5894299745559692e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 210 2.1967150270938873e-02</internalNodes>
          <leafValues>
            -1.0052759945392609e-01 4.7374871373176575e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 211 -6.0128211043775082e-03</internalNodes>
          <leafValues>
            1.9820199906826019e-01 -4.2172819375991821e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 212 4.5052049681544304e-03</internalNodes>
          <leafValues>
            1.7064809799194336e-02 -4.8947790265083313e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 213 -1.3302109437063336e-03</internalNodes>
          <leafValues>
            1.8670339882373810e-01 -2.9437661170959473e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 214 -7.3667510878294706e-04</internalNodes>
          <leafValues>
            -1.4788800477981567e-01 1.0121300071477890e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 215 -1.4602739829570055e-03</internalNodes>
          <leafValues>
            -4.3107959628105164e-01 1.2479860335588455e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 216 3.4185629338026047e-02</internalNodes>
          <leafValues>
            -5.7933650910854340e-02 5.4917758703231812e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>33</maxWeakCount>
      <stageThreshold>-1.0450899600982666e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 217 3.0665110796689987e-02</internalNodes>
          <leafValues>
            -3.9953279495239258e-01 3.3617529273033142e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 218 2.8893710114061832e-03</internalNodes>
          <leafValues>
            -3.8745269179344177e-01 3.0567520856857300e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 219 -1.1876110220327973e-03</internalNodes>
          <leafValues>
            2.2150239348411560e-01 -2.9632321000099182e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 220 4.0173018351197243e-03</internalNodes>
          <leafValues>
            1.3102529942989349e-01 -4.8803418874740601e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 221 4.4870697893202305e-03</internalNodes>
          <leafValues>
            -3.3282509446144104e-01 1.6376070678234100e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 222 3.2539520412683487e-02</internalNodes>
          <leafValues>
            -5.9164509177207947e-02 6.9953370094299316e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 223 -8.9682880789041519e-03</internalNodes>
          <leafValues>
            -5.6289541721343994e-01 1.1756320297718048e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 224 -6.1743397964164615e-04</internalNodes>
          <leafValues>
            1.5408250689506531e-01 -2.7350011467933655e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 225 -3.1031211256049573e-04</internalNodes>
          <leafValues>
            1.8013550341129303e-01 -3.7572589516639709e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 226 2.8775030747056007e-02</internalNodes>
          <leafValues>
            -3.4200929105281830e-02 2.7645361423492432e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 227 -6.1647972324863076e-04</internalNodes>
          <leafValues>
            1.7953120172023773e-01 -3.5178318619728088e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 228 2.1818219684064388e-03</internalNodes>
          <leafValues>
            -1.4532999694347382e-01 1.4900140464305878e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 229 -2.4263889063149691e-03</internalNodes>
          <leafValues>
            -4.6981298923492432e-01 9.5262229442596436e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 230 2.5438209995627403e-02</internalNodes>
          <leafValues>
            -2.1531460806727409e-02 3.3266928791999817e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 231 7.9593079863116145e-04</internalNodes>
          <leafValues>
            1.2254969775676727e-01 -3.5679769515991211e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 232 5.6763447355479002e-04</internalNodes>
          <leafValues>
            -1.3694189488887787e-01 1.0818839818239212e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 233 8.7481308728456497e-03</internalNodes>
          <leafValues>
            -9.0849868953227997e-02 5.0112378597259521e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 234 -4.7468831762671471e-03</internalNodes>
          <leafValues>
            1.1629249900579453e-01 -1.4651729725301266e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 235 3.0644210055470467e-03</internalNodes>
          <leafValues>
            -2.2739639878273010e-01 2.7780678868293762e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 236 3.1514191068708897e-03</internalNodes>
          <leafValues>
            3.5710681229829788e-02 -3.2296779751777649e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 237 -3.8335900753736496e-03</internalNodes>
          <leafValues>
            -4.8395419120788574e-01 9.2689603567123413e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 238 -3.6972409579902887e-03</internalNodes>
          <leafValues>
            1.6351610422134399e-01 -1.4657320082187653e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 239 6.7644561640918255e-03</internalNodes>
          <leafValues>
            8.0342940986156464e-02 -5.0272989273071289e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 240 5.7455507339909673e-04</internalNodes>
          <leafValues>
            -1.9531010091304779e-01 1.2394949793815613e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 241 1.0008309967815876e-02</internalNodes>
          <leafValues>
            -1.5030139684677124e-01 2.7990019321441650e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 242 -7.2150952182710171e-03</internalNodes>
          <leafValues>
            1.6882060468196869e-01 -1.2279219925403595e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 243 1.1310850270092487e-02</internalNodes>
          <leafValues>
            -9.6786908805370331e-02 6.4601618051528931e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 244 1.0049899667501450e-01</internalNodes>
          <leafValues>
            2.0610159263014793e-02 -9.9988579750061035e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 245 1.3250860385596752e-02</internalNodes>
          <leafValues>
            9.3147717416286469e-02 -4.8156800866127014e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 246 -3.9085310697555542e-01</internalNodes>
          <leafValues>
            7.1057820320129395e-01 -1.6548840329051018e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 247 2.4332199245691299e-02</internalNodes>
          <leafValues>
            1.4528210461139679e-01 -2.8366720676422119e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 248 1.0354409459978342e-03</internalNodes>
          <leafValues>
            -2.0017370581626892e-01 1.8794250488281250e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 249 -7.1747899055480957e-01</internalNodes>
          <leafValues>
            6.6637128591537476e-01 -5.2656259387731552e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>42</maxWeakCount>
      <stageThreshold>-1.0599969625473022e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 250 1.9620559178292751e-03</internalNodes>
          <leafValues>
            -4.1077700257301331e-01 1.8896859884262085e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 251 2.1331369876861572e-02</internalNodes>
          <leafValues>
            9.2599019408226013e-02 -3.9660450816154480e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 252 -2.3037450388073921e-02</internalNodes>
          <leafValues>
            -7.2293937206268311e-01 9.6411719918251038e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 253 -5.0521228462457657e-02</internalNodes>
          <leafValues>
            1.8302009999752045e-01 -1.9482779502868652e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 254 2.5330919772386551e-02</internalNodes>
          <leafValues>
            1.0334759950637817e-01 -5.8018290996551514e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 255 -4.3120220652781427e-04</internalNodes>
          <leafValues>
            1.3374519348144531e-01 -2.1300980448722839e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 256 -1.4295669643615838e-05</internalNodes>
          <leafValues>
            1.8420490622520447e-01 -3.0300238728523254e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 257 -2.8645719867199659e-03</internalNodes>
          <leafValues>
            1.7371790111064911e-01 -2.1612820029258728e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 258 1.0322510264813900e-02</internalNodes>
          <leafValues>
            1.1071330308914185e-01 -4.2402949929237366e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 259 1.3879509642720222e-02</internalNodes>
          <leafValues>
            -1.0993299633264542e-01 5.5458897352218628e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 260 -1.7010340234264731e-03</internalNodes>
          <leafValues>
            -3.1409528851509094e-01 1.5474779903888702e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 261 -2.7375848731026053e-04</internalNodes>
          <leafValues>
            1.4674690365791321e-01 -1.2817619740962982e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 262 3.9977379143238068e-02</internalNodes>
          <leafValues>
            -6.3540339469909668e-02 6.0685801506042480e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 263 -1.2663399800658226e-02</internalNodes>
          <leafValues>
            1.0982260107994080e-01 -1.2707209587097168e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 264 1.0186760127544403e-01</internalNodes>
          <leafValues>
            8.8505871593952179e-02 -5.7165622711181641e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 265 -1.0695089586079121e-03</internalNodes>
          <leafValues>
            3.4594889730215073e-02 -9.9618308246135712e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 266 -3.4467370714992285e-03</internalNodes>
          <leafValues>
            2.2871519625186920e-01 -1.9664469361305237e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 267 -1.2329400330781937e-01</internalNodes>
          <leafValues>
            -1.0825649648904800e-01 2.4728389456868172e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 268 -5.8832589536905289e-02</internalNodes>
          <leafValues>
            5.5791580677032471e-01 -7.7630676329135895e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 269 9.7795920446515083e-03</internalNodes>
          <leafValues>
            9.4951488077640533e-02 -5.3767371177673340e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 270 1.1116569861769676e-02</internalNodes>
          <leafValues>
            -8.9288607239723206e-02 4.6695429086685181e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 271 -1.5398260205984116e-02</internalNodes>
          <leafValues>
            9.0432487428188324e-02 -1.2233799695968628e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 272 5.8570769615471363e-03</internalNodes>
          <leafValues>
            1.0859709978103638e-01 -4.0961760282516479e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 273 6.6174753010272980e-02</internalNodes>
          <leafValues>
            -4.4282642193138599e-03 -8.8055539131164551e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 274 -1.0636489838361740e-02</internalNodes>
          <leafValues>
            -4.4541570544242859e-01 1.0953740030527115e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 275 -3.1363599002361298e-02</internalNodes>
          <leafValues>
            8.0546891689300537e-01 -4.9883890897035599e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 276 9.8021561279892921e-04</internalNodes>
          <leafValues>
            -2.3428329825401306e-01 1.6934409737586975e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 277 5.3463829681277275e-03</internalNodes>
          <leafValues>
            -1.0729180276393890e-01 2.5447541475296021e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 278 -5.1919990219175816e-03</internalNodes>
          <leafValues>
            -5.1496618986129761e-01 8.5118137300014496e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 279 1.8721649423241615e-02</internalNodes>
          <leafValues>
            -8.4052212536334991e-02 4.7836899757385254e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 280 3.7875440903007984e-03</internalNodes>
          <leafValues>
            -2.3145659267902374e-01 1.6052989661693573e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 281 6.8765478208661079e-03</internalNodes>
          <leafValues>
            9.6559382975101471e-02 -2.3832960426807404e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 282 -5.4661519825458527e-03</internalNodes>
          <leafValues>
            -3.7871730327606201e-01 8.7851487100124359e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 283 -1.5829449519515038e-02</internalNodes>
          <leafValues>
            5.2159512042999268e-01 -7.3916867375373840e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 284 1.2771990150213242e-02</internalNodes>
          <leafValues>
            1.0658729821443558e-01 -3.2850459218025208e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 285 4.7000780701637268e-02</internalNodes>
          <leafValues>
            -2.9548000544309616e-02 4.8469349741935730e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 286 1.1224800255149603e-03</internalNodes>
          <leafValues>
            -2.1395659446716309e-01 1.5407760441303253e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 287 -1.0136750061064959e-03</internalNodes>
          <leafValues>
            2.3574739694595337e-01 -1.4536799490451813e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 288 5.2841319702565670e-03</internalNodes>
          <leafValues>
            8.0536216497421265e-02 -3.6417248845100403e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 289 -1.7608689144253731e-02</internalNodes>
          <leafValues>
            5.3858822584152222e-01 -3.5741850733757019e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 290 3.4710608422756195e-02</internalNodes>
          <leafValues>
            -4.3261460959911346e-02 7.7817600965499878e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 291 1.6450349241495132e-02</internalNodes>
          <leafValues>
            4.1815090924501419e-02 -3.4912678599357605e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>45</maxWeakCount>
      <stageThreshold>-1.0216469764709473e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 292 -1.7846419941633940e-03</internalNodes>
          <leafValues>
            2.2014810144901276e-01 -3.6912658810615540e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 293 -6.1350408941507339e-04</internalNodes>
          <leafValues>
            -3.0695998668670654e-01 9.7717791795730591e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 294 -2.5726810563355684e-03</internalNodes>
          <leafValues>
            -3.7789058685302734e-01 1.7042149603366852e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 295 8.8661757763475180e-04</internalNodes>
          <leafValues>
            -3.7929078936576843e-01 9.3289971351623535e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 296 3.5716239362955093e-02</internalNodes>
          <leafValues>
            7.3169313371181488e-02 -6.1792898178100586e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 297 3.5162840038537979e-02</internalNodes>
          <leafValues>
            -1.2328250333666801e-02 4.4894638657569885e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 298 -5.8216741308569908e-03</internalNodes>
          <leafValues>
            -4.9501991271972656e-01 8.8005952537059784e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 299 -7.7909301035106182e-04</internalNodes>
          <leafValues>
            1.1154119670391083e-01 -2.8316551446914673e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 300 -6.8164491094648838e-03</internalNodes>
          <leafValues>
            1.8434180319309235e-01 -2.3727069795131683e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 301 9.0218139812350273e-03</internalNodes>
          <leafValues>
            -5.3773559629917145e-02 2.6174989342689514e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 302 -6.7481878213584423e-03</internalNodes>
          <leafValues>
            -5.0475108623504639e-01 7.6614417135715485e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 303 7.5771231204271317e-03</internalNodes>
          <leafValues>
            -1.1926110088825226e-01 3.4210419654846191e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 304 -4.6335519291460514e-03</internalNodes>
          <leafValues>
            -4.9088281393051147e-01 6.9542020559310913e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 305 4.1346959769725800e-03</internalNodes>
          <leafValues>
            -8.1591427326202393e-02 4.7879660129547119e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 306 -9.8444558680057526e-03</internalNodes>
          <leafValues>
            2.0124210417270660e-01 -2.3769280314445496e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 307 -3.4897070378065109e-02</internalNodes>
          <leafValues>
            -9.1024678945541382e-01 1.8579540774226189e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 308 -3.5042490344494581e-04</internalNodes>
          <leafValues>
            1.2479469925165176e-01 -3.0717149376869202e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 309 -9.4668623059988022e-03</internalNodes>
          <leafValues>
            1.1332949995994568e-01 -1.6115890443325043e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 310 2.2053409367799759e-02</internalNodes>
          <leafValues>
            -7.9784400761127472e-02 6.0739010572433472e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 311 -7.2947797889355570e-05</internalNodes>
          <leafValues>
            1.4449119567871094e-01 -1.3706150650978088e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 312 -7.5134839862585068e-03</internalNodes>
          <leafValues>
            -3.0744421482086182e-01 1.0279080271720886e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 313 1.0311939753592014e-02</internalNodes>
          <leafValues>
            -7.0246197283267975e-02 4.8307010531425476e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 314 9.4670448452234268e-03</internalNodes>
          <leafValues>
            7.0281803607940674e-02 -4.7069519758224487e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 315 -3.0116239562630653e-02</internalNodes>
          <leafValues>
            5.2378559112548828e-01 -3.7109669297933578e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 316 -1.2667849659919739e-02</internalNodes>
          <leafValues>
            -6.0825890302658081e-01 5.0444670021533966e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 317 2.2987429983913898e-03</internalNodes>
          <leafValues>
            -1.1808679997920990e-01 1.7393890023231506e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 318 2.5533209554851055e-03</internalNodes>
          <leafValues>
            -1.6625979542732239e-01 1.9768959283828735e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 319 -3.3218199014663696e-01</internalNodes>
          <leafValues>
            -9.5407789945602417e-01 4.1291080415248871e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 320 5.4485369473695755e-03</internalNodes>
          <leafValues>
            -9.1220542788505554e-02 3.9834749698638916e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 321 4.7633191570639610e-03</internalNodes>
          <leafValues>
            -1.2069889903068542e-01 1.6169339418411255e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 322 4.4371229596436024e-03</internalNodes>
          <leafValues>
            8.5928186774253845e-02 -4.4427189230918884e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 323 2.7019889093935490e-03</internalNodes>
          <leafValues>
            -1.9511219859123230e-01 7.1141660213470459e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 324 -1.4219670556485653e-03</internalNodes>
          <leafValues>
            1.9089500606060028e-01 -1.8880489468574524e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 325 -6.9531630724668503e-03</internalNodes>
          <leafValues>
            -2.6191520690917969e-01 7.7488146722316742e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 326 -2.6554360985755920e-01</internalNodes>
          <leafValues>
            4.7893580794334412e-01 -7.8830257058143616e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 327 5.4960828274488449e-03</internalNodes>
          <leafValues>
            6.4748808741569519e-02 -4.0898790955543518e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 328 1.6060929745435715e-02</internalNodes>
          <leafValues>
            9.4868503510951996e-02 -3.5040768980979919e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 329 -3.5279421135783195e-03</internalNodes>
          <leafValues>
            2.2704540193080902e-01 -1.5011039376258850e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 330 1.5189720317721367e-02</internalNodes>
          <leafValues>
            -8.6033642292022705e-02 5.0375241041183472e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 331 9.8117031157016754e-03</internalNodes>
          <leafValues>
            9.1945856809616089e-02 -2.7134710550308228e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 332 -8.9835934340953827e-03</internalNodes>
          <leafValues>
            -3.5721930861473083e-01 1.1564330011606216e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 333 2.5472430512309074e-02</internalNodes>
          <leafValues>
            -3.8861878216266632e-02 5.0707322359085083e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 334 1.3594819465652108e-03</internalNodes>
          <leafValues>
            -1.5127420425415039e-01 2.3332439363002777e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 335 1.4673129655420780e-02</internalNodes>
          <leafValues>
            7.6386481523513794e-02 -4.3126261234283447e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 336 -2.1757239475846291e-02</internalNodes>
          <leafValues>
            6.0306608676910400e-01 -5.7926669716835022e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>49</maxWeakCount>
      <stageThreshold>-1.0149190425872803e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 337 -1.9122850149869919e-02</internalNodes>
          <leafValues>
            2.1423059701919556e-01 -4.0178310871124268e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 338 -4.0749661275185645e-04</internalNodes>
          <leafValues>
            1.0837800055742264e-01 -9.7847007215023041e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 339 1.8419560045003891e-02</internalNodes>
          <leafValues>
            9.4817012548446655e-02 -4.4825899600982666e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 340 -3.0946850893087685e-04</internalNodes>
          <leafValues>
            1.1567220091819763e-01 -6.9291338324546814e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 341 2.4416830390691757e-02</internalNodes>
          <leafValues>
            -2.6403778791427612e-01 1.4588509500026703e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 342 3.9483308792114258e-03</internalNodes>
          <leafValues>
            7.8703567385673523e-02 -3.9770650863647461e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 343 1.5498059801757336e-02</internalNodes>
          <leafValues>
            -6.8623371422290802e-02 6.3598757982254028e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 344 1.0397369973361492e-02</internalNodes>
          <leafValues>
            5.3116258233785629e-02 -2.4757599830627441e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 345 1.0350650409236550e-03</internalNodes>
          <leafValues>
            -2.2953610122203827e-01 2.1623679995536804e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 346 -6.9717521546408534e-04</internalNodes>
          <leafValues>
            1.6330949962139130e-01 -2.7930000424385071e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 347 1.1055100476369262e-03</internalNodes>
          <leafValues>
            -2.6721170544624329e-01 1.3809490203857422e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 348 1.8128760159015656e-02</internalNodes>
          <leafValues>
            7.8602522611618042e-02 -3.3748328685760498e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 349 -1.4303029747679830e-03</internalNodes>
          <leafValues>
            1.5668049454689026e-01 -2.5422498583793640e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 350 1.0650220327079296e-02</internalNodes>
          <leafValues>
            -4.1638601571321487e-02 3.2634070515632629e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 351 -1.0680139530450106e-03</internalNodes>
          <leafValues>
            1.7996980249881744e-01 -2.0673060417175293e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 352 -8.0095082521438599e-03</internalNodes>
          <leafValues>
            -2.8778979182243347e-01 7.5492449104785919e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 353 -1.1857559904456139e-02</internalNodes>
          <leafValues>
            -5.5485212802886963e-01 4.7465000301599503e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 354 -1.9440150260925293e-01</internalNodes>
          <leafValues>
            4.9564599990844727e-01 -6.8522267043590546e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 355 1.2786169536411762e-02</internalNodes>
          <leafValues>
            -5.8201011270284653e-02 5.1194858551025391e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 356 1.1360739590600133e-03</internalNodes>
          <leafValues>
            -2.1216529607772827e-01 1.4639540016651154e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 357 -3.7541511119343340e-04</internalNodes>
          <leafValues>
            1.1406060308218002e-01 -2.7936661243438721e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 358 6.2142009846866131e-03</internalNodes>
          <leafValues>
            2.8568789362907410e-02 -3.2485058903694153e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 359 4.5166439376771450e-03</internalNodes>
          <leafValues>
            -9.5556378364562988e-02 3.6032339930534363e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 360 -1.7354219453409314e-03</internalNodes>
          <leafValues>
            -8.0804876983165741e-02 5.3851570934057236e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 361 -6.9608418270945549e-03</internalNodes>
          <leafValues>
            -6.0131508111953735e-01 4.5509491115808487e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 362 8.7833311408758163e-03</internalNodes>
          <leafValues>
            -9.4497971236705780e-02 3.1924161314964294e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 363 -2.0243569742888212e-03</internalNodes>
          <leafValues>
            2.6737558841705322e-01 -1.1679279804229736e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 364 5.6362948380410671e-03</internalNodes>
          <leafValues>
            4.6491090208292007e-02 -2.3982259631156921e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 365 -2.1751220338046551e-03</internalNodes>
          <leafValues>
            -3.1831741333007812e-01 1.1634550243616104e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 366 2.5424890220165253e-02</internalNodes>
          <leafValues>
            7.5600057840347290e-02 -3.7359631061553955e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 367 3.9950129576027393e-04</internalNodes>
          <leafValues>
            -2.6206868886947632e-01 1.4345559477806091e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 368 -3.9724060334265232e-03</internalNodes>
          <leafValues>
            2.0395089685916901e-01 -1.1896310001611710e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 369 2.4637179449200630e-03</internalNodes>
          <leafValues>
            -1.3687339425086975e-01 3.4098258614540100e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 370 1.4397709630429745e-02</internalNodes>
          <leafValues>
            2.4846889078617096e-02 -6.5415948629379272e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 371 -1.4848919818177819e-05</internalNodes>
          <leafValues>
            1.3884930312633514e-01 -2.1077479422092438e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 372 -3.8339510560035706e-02</internalNodes>
          <leafValues>
            5.8668392896652222e-01 -3.6245860159397125e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 373 -5.4605712648481131e-04</internalNodes>
          <leafValues>
            2.1259330213069916e-01 -1.3791069388389587e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 374 1.3036499731242657e-02</internalNodes>
          <leafValues>
            5.0619971007108688e-02 -2.3150099813938141e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 375 -2.4273560848087072e-03</internalNodes>
          <leafValues>
            2.4302999675273895e-01 -1.1315950006246567e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 376 -6.3351681455969810e-03</internalNodes>
          <leafValues>
            -3.5549488663673401e-01 9.4948403537273407e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 377 -5.7510860264301300e-02</internalNodes>
          <leafValues>
            4.9378138780593872e-01 -6.0664121061563492e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 378 6.8376341369003057e-04</internalNodes>
          <leafValues>
            -1.9417250156402588e-01 1.4234590530395508e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 379 8.8113872334361076e-03</internalNodes>
          <leafValues>
            4.7562059015035629e-02 -5.8416491746902466e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 380 1.0788169689476490e-02</internalNodes>
          <leafValues>
            -4.6855889260768890e-02 1.6548010706901550e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 381 -1.3571690069511533e-03</internalNodes>
          <leafValues>
            -3.2510679960250854e-01 9.4090476632118225e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 382 -1.0195979848504066e-02</internalNodes>
          <leafValues>
            -1.4696849882602692e-01 2.6246059685945511e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 383 -1.2560819741338491e-03</internalNodes>
          <leafValues>
            2.2853380441665649e-01 -1.6265660524368286e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 384 6.6750420955941081e-04</internalNodes>
          <leafValues>
            -1.3430669903755188e-01 1.3987569510936737e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 385 2.0975170191377401e-03</internalNodes>
          <leafValues>
            -1.2987610697746277e-01 1.9978469610214233e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>53</maxWeakCount>
      <stageThreshold>-9.3152678012847900e-01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 386 -3.6917610559612513e-03</internalNodes>
          <leafValues>
            2.2682790458202362e-01 -4.1167381405830383e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 387 -9.4609148800373077e-03</internalNodes>
          <leafValues>
            1.6305020451545715e-01 -2.2949010133743286e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 388 3.3874800428748131e-03</internalNodes>
          <leafValues>
            7.7644690871238708e-02 -4.7465118765830994e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 389 3.3596849534660578e-03</internalNodes>
          <leafValues>
            -1.4722810685634613e-01 1.3755659759044647e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 390 -2.2649099119007587e-03</internalNodes>
          <leafValues>
            -2.9027861356735229e-01 1.2261869758367538e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 391 -5.5420072749257088e-04</internalNodes>
          <leafValues>
            1.1591990292072296e-01 -2.3066529631614685e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 392 1.9706019666045904e-03</internalNodes>
          <leafValues>
            1.1808300018310547e-01 -3.7879431247711182e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 393 1.7503080889582634e-02</internalNodes>
          <leafValues>
            -9.4161599874496460e-02 4.7933238744735718e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 394 -2.9575270600616932e-03</internalNodes>
          <leafValues>
            1.7336699366569519e-01 -3.1673321127891541e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 395 -2.6238700747489929e-01</internalNodes>
          <leafValues>
            -7.4405288696289062e-01 8.9512793347239494e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 396 5.5493800900876522e-03</internalNodes>
          <leafValues>
            -2.4088740348815918e-01 1.4212040603160858e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 397 -1.4842569828033447e-02</internalNodes>
          <leafValues>
            5.5166311562061310e-02 -8.5363000631332397e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 398 -1.8193490803241730e-02</internalNodes>
          <leafValues>
            -7.5389099121093750e-01 4.4062498956918716e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 399 -1.9381130114197731e-03</internalNodes>
          <leafValues>
            1.4762139320373535e-01 -1.4214770495891571e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 400 -6.1375028453767300e-03</internalNodes>
          <leafValues>
            -5.4175209999084473e-01 5.2872691303491592e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 401 1.6630079597234726e-02</internalNodes>
          <leafValues>
            -6.0005810111761093e-02 5.2294141054153442e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 402 -9.7470665350556374e-03</internalNodes>
          <leafValues>
            -3.1776770949363708e-01 9.4077728688716888e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 403 -3.9159679412841797e-01</internalNodes>
          <leafValues>
            5.1550501585006714e-01 -8.6178213357925415e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 404 1.0457860305905342e-02</internalNodes>
          <leafValues>
            -5.4442230612039566e-02 5.5086338520050049e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 405 9.2479586601257324e-02</internalNodes>
          <leafValues>
            9.5865959301590919e-03 -7.5205242633819580e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 406 -1.3383329845964909e-02</internalNodes>
          <leafValues>
            -2.5909280776977539e-01 1.2255199998617172e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 407 -1.9297929480671883e-02</internalNodes>
          <leafValues>
            -1.8686549365520477e-01 4.2670380324125290e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 408 -1.1118740076199174e-03</internalNodes>
          <leafValues>
            1.4586099982261658e-01 -2.2742809355258942e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 409 2.3209059610962868e-02</internalNodes>
          <leafValues>
            2.1769199520349503e-02 -2.4001930654048920e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 410 6.9435071200132370e-03</internalNodes>
          <leafValues>
            -8.4814570844173431e-02 3.8388100266456604e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 411 -1.0249669849872589e-01</internalNodes>
          <leafValues>
            -7.0618611574172974e-01 1.2580949813127518e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 412 -1.4036430045962334e-02</internalNodes>
          <leafValues>
            -3.8427880406379700e-01 8.7678723037242889e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 413 6.8071340210735798e-03</internalNodes>
          <leafValues>
            -7.5941346585750580e-02 7.6014332473278046e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 414 4.8163239844143391e-03</internalNodes>
          <leafValues>
            -1.6402910649776459e-01 2.0124110579490662e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 415 -3.0274710152298212e-03</internalNodes>
          <leafValues>
            -2.8118729591369629e-01 6.8671241402626038e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 416 -1.6530510038137436e-03</internalNodes>
          <leafValues>
            2.1427379548549652e-01 -1.3038359582424164e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 417 -3.9757499471306801e-03</internalNodes>
          <leafValues>
            -2.3737999796867371e-01 5.1290549337863922e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 418 6.9589749909937382e-03</internalNodes>
          <leafValues>
            -1.3246279954910278e-01 2.3703409731388092e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 419 7.2270620148628950e-04</internalNodes>
          <leafValues>
            5.0478070974349976e-02 -1.3544809818267822e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 420 1.5057729557156563e-02</internalNodes>
          <leafValues>
            -6.6954463720321655e-02 4.5368999242782593e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 421 6.5838429145514965e-03</internalNodes>
          <leafValues>
            3.9054669439792633e-02 -1.9516509771347046e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 422 -2.9128929600119591e-03</internalNodes>
          <leafValues>
            1.7604969441890717e-01 -1.5639689564704895e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 423 6.4386397600173950e-01</internalNodes>
          <leafValues>
            -1.1777699925005436e-02 1.0000569820404053e+00</leafValues></_>
        <_>
          <internalNodes>
            0 -1 424 5.1160277798771858e-03</internalNodes>
          <leafValues>
            9.5464669167995453e-02 -3.7832370400428772e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 425 6.8325497210025787e-02</internalNodes>
          <leafValues>
            -3.9297499461099505e-04 -9.9986249208450317e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 426 4.4071719050407410e-02</internalNodes>
          <leafValues>
            2.8716549277305603e-02 -9.0306490659713745e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 427 -1.5712520107626915e-02</internalNodes>
          <leafValues>
            2.4888029694557190e-01 -5.3066261112689972e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 428 -3.9486829191446304e-03</internalNodes>
          <leafValues>
            -5.0214129686355591e-01 5.2089609205722809e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 429 1.1841469677165151e-03</internalNodes>
          <leafValues>
            6.2122888863086700e-02 -1.6479890048503876e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 430 -1.1385709792375565e-01</internalNodes>
          <leafValues>
            5.6728571653366089e-01 -3.8864318281412125e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 431 6.2493737787008286e-03</internalNodes>
          <leafValues>
            8.7858140468597412e-02 -2.8675949573516846e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 432 -2.3781529162079096e-03</internalNodes>
          <leafValues>
            2.6684141159057617e-01 -9.3291386961936951e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 433 -6.3620522618293762e-02</internalNodes>
          <leafValues>
            1.5153369307518005e-01 -1.5354029834270477e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 434 7.9275481402873993e-03</internalNodes>
          <leafValues>
            8.8268518447875977e-02 -3.1872791051864624e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 435 1.0556660126894712e-03</internalNodes>
          <leafValues>
            -1.0226110368967056e-01 6.0546699911355972e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 436 9.1879200190305710e-03</internalNodes>
          <leafValues>
            8.0963402986526489e-02 -3.5031539201736450e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 437 3.9727380499243736e-03</internalNodes>
          <leafValues>
            -1.0334850102663040e-01 2.7450188994407654e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 438 1.7149309860542417e-03</internalNodes>
          <leafValues>
            -1.2329679727554321e-01 2.1561819314956665e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>55</maxWeakCount>
      <stageThreshold>-9.3984860181808472e-01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 439 -1.4547890052199364e-02</internalNodes>
          <leafValues>
            -5.7042872905731201e-01 1.0164090245962143e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 440 -1.2570459512062371e-04</internalNodes>
          <leafValues>
            7.7566891908645630e-02 -2.9524150490760803e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 441 9.4022490084171295e-03</internalNodes>
          <leafValues>
            -3.2618519663810730e-01 1.3688039779663086e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 442 -5.1469001919031143e-03</internalNodes>
          <leafValues>
            -2.2486360371112823e-01 1.4886389672756195e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 443 -3.1212199246510863e-04</internalNodes>
          <leafValues>
            1.1287149786949158e-01 -3.2888731360435486e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 444 1.8742609769105911e-02</internalNodes>
          <leafValues>
            -1.8080070614814758e-02 3.0115321278572083e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 445 2.9675778932869434e-03</internalNodes>
          <leafValues>
            -2.5948849320411682e-01 1.3308060169219971e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 446 -3.0295079573988914e-02</internalNodes>
          <leafValues>
            -6.0041320323944092e-01 3.3516548573970795e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 447 6.4835487864911556e-03</internalNodes>
          <leafValues>
            -7.7768087387084961e-02 4.6268320083618164e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 448 2.2889559622853994e-03</internalNodes>
          <leafValues>
            6.0411829501390457e-02 -1.7498730123043060e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 449 -1.6078320331871510e-03</internalNodes>
          <leafValues>
            -2.9557180404663086e-01 1.5449790656566620e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 450 -2.3348669707775116e-01</internalNodes>
          <leafValues>
            -6.3751947879791260e-01 1.3748309575021267e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 451 5.8999718166887760e-03</internalNodes>
          <leafValues>
            1.2713789939880371e-01 -3.2689490914344788e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 452 1.2073719874024391e-02</internalNodes>
          <leafValues>
            1.6614260151982307e-02 -2.2707170248031616e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 453 -5.6356011191383004e-04</internalNodes>
          <leafValues>
            1.6879190504550934e-01 -1.9605310261249542e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 454 1.7435080371797085e-03</internalNodes>
          <leafValues>
            -1.3831000030040741e-01 2.2103509306907654e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 455 6.6066621802747250e-03</internalNodes>
          <leafValues>
            4.4354528188705444e-02 -6.7365241050720215e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 456 -5.9419698081910610e-03</internalNodes>
          <leafValues>
            1.7569009959697723e-01 -1.3697220385074615e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 457 4.9261527601629496e-04</internalNodes>
          <leafValues>
            -2.1035130321979523e-01 1.3241830468177795e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 458 -3.6582869943231344e-03</internalNodes>
          <leafValues>
            1.5420369803905487e-01 -1.0563220083713531e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 459 -1.4477679505944252e-03</internalNodes>
          <leafValues>
            -2.8920960426330566e-01 1.4950390160083771e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 460 -1.0310580255463719e-03</internalNodes>
          <leafValues>
            8.8572971522808075e-02 -9.0375833213329315e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 461 3.2927519641816616e-03</internalNodes>
          <leafValues>
            -1.1087729781866074e-01 3.0003741383552551e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 462 -1.6668019816279411e-03</internalNodes>
          <leafValues>
            -6.2054108828306198e-02 2.2652259469032288e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 463 1.3452100101858377e-03</internalNodes>
          <leafValues>
            9.2012971639633179e-02 -3.5944160819053650e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 464 -1.4981569722294807e-02</internalNodes>
          <leafValues>
            3.6636090278625488e-01 -6.4556807279586792e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 465 6.2536462210118771e-03</internalNodes>
          <leafValues>
            6.9381363689899445e-02 -4.1023838520050049e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 466 5.0937399268150330e-02</internalNodes>
          <leafValues>
            1.7869930714368820e-02 -6.0524070262908936e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 467 1.0756580159068108e-03</internalNodes>
          <leafValues>
            -2.3777949810028076e-01 1.4223319292068481e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 468 -4.1086040437221527e-03</internalNodes>
          <leafValues>
            1.4915379881858826e-01 -1.9213069975376129e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 469 -1.3338520191609859e-02</internalNodes>
          <leafValues>
            -4.9711030721664429e-01 6.5755158662796021e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 470 3.1997971236705780e-02</internalNodes>
          <leafValues>
            -6.4927592873573303e-02 6.6577041149139404e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 471 -4.9686059355735779e-02</internalNodes>
          <leafValues>
            5.0676888227462769e-01 -6.4676910638809204e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 472 6.0286428779363632e-03</internalNodes>
          <leafValues>
            8.8214896619319916e-02 -2.7923619747161865e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 473 -6.9053061306476593e-03</internalNodes>
          <leafValues>
            -6.1452347040176392e-01 3.5631489008665085e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 474 5.8130919933319092e-03</internalNodes>
          <leafValues>
            -9.3653626739978790e-02 9.9817357957363129e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 475 -1.1030419729650021e-02</internalNodes>
          <leafValues>
            4.5798170566558838e-01 -6.5124973654747009e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 476 -1.5703570097684860e-03</internalNodes>
          <leafValues>
            4.7113660722970963e-02 -1.3347460329532623e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 477 4.6482901088893414e-03</internalNodes>
          <leafValues>
            7.3932677507400513e-02 -4.2145860195159912e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 478 5.0479872152209282e-04</internalNodes>
          <leafValues>
            -2.0517270267009735e-01 9.5128253102302551e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 479 2.6125760748982430e-02</internalNodes>
          <leafValues>
            -6.8816967308521271e-02 4.2644789814949036e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 480 6.4811189658939838e-03</internalNodes>
          <leafValues>
            1.1302389949560165e-01 -4.7021061182022095e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 481 -4.5484181493520737e-02</internalNodes>
          <leafValues>
            5.4101467132568359e-01 -5.6804839521646500e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 482 6.8956136703491211e-02</internalNodes>
          <leafValues>
            3.4444119781255722e-02 -1.7411549389362335e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 483 -2.0358948968350887e-03</internalNodes>
          <leafValues>
            1.3366940617561340e-01 -2.0985920727252960e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 484 1.4390050200745463e-03</internalNodes>
          <leafValues>
            -1.6449619829654694e-01 9.8886348307132721e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 485 3.0180480331182480e-02</internalNodes>
          <leafValues>
            8.7635383009910583e-02 -3.9464119076728821e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 486 -3.8663588929921389e-03</internalNodes>
          <leafValues>
            1.5964619815349579e-01 -1.1840829998254776e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 487 1.0753490030765533e-02</internalNodes>
          <leafValues>
            -5.7142060250043869e-02 5.0125277042388916e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 488 1.0978150181472301e-02</internalNodes>
          <leafValues>
            3.5985160619020462e-02 -3.8646480441093445e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 489 -7.8152219066396356e-04</internalNodes>
          <leafValues>
            1.8248090147972107e-01 -1.6435949504375458e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 490 -6.9936108775436878e-03</internalNodes>
          <leafValues>
            -2.6556238532066345e-01 9.4436101615428925e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 491 2.3125730454921722e-02</internalNodes>
          <leafValues>
            -5.9101939201354980e-02 5.7359057664871216e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 492 -1.7055520787835121e-02</internalNodes>
          <leafValues>
            -5.4567247629165649e-01 2.7153130620718002e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 493 1.5192289836704731e-02</internalNodes>
          <leafValues>
            9.2580981552600861e-02 -2.9735139012336731e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>53</maxWeakCount>
      <stageThreshold>-8.2538652420043945e-01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 494 -2.1589139476418495e-02</internalNodes>
          <leafValues>
            3.3779260516166687e-01 -2.6725459098815918e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 495 6.3885431736707687e-03</internalNodes>
          <leafValues>
            -2.6759129762649536e-01 2.1438689529895782e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 496 -2.4394609499722719e-03</internalNodes>
          <leafValues>
            1.8841089308261871e-01 -2.3495130240917206e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 497 3.9824391715228558e-03</internalNodes>
          <leafValues>
            4.6689908951520920e-02 -1.7984829843044281e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 498 -3.1252959161065519e-04</internalNodes>
          <leafValues>
            1.7267709970474243e-01 -1.8782779574394226e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 499 3.3181109465658665e-03</internalNodes>
          <leafValues>
            1.2081120163202286e-01 -3.2373869419097900e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 500 -7.0711369626224041e-03</internalNodes>
          <leafValues>
            -2.7498379349708557e-01 1.3868269324302673e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 501 4.4392608106136322e-03</internalNodes>
          <leafValues>
            -2.2279019653797150e-01 1.7155140638351440e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 502 2.1352670155465603e-03</internalNodes>
          <leafValues>
            -1.1322859674692154e-01 2.8428959846496582e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 503 -4.0205409750342369e-03</internalNodes>
          <leafValues>
            -2.4542550742626190e-01 9.4957500696182251e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 504 -6.5228617750108242e-03</internalNodes>
          <leafValues>
            3.2106789946556091e-01 -9.7372367978096008e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 505 4.4146090658614412e-05</internalNodes>
          <leafValues>
            -1.5269330143928528e-01 8.5128836333751678e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 506 4.7606039792299271e-02</internalNodes>
          <leafValues>
            7.9339757561683655e-02 -2.9599419236183167e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 507 4.0928661823272705e-02</internalNodes>
          <leafValues>
            -3.5142261534929276e-02 3.7593579292297363e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 508 -1.1161889880895615e-02</internalNodes>
          <leafValues>
            -2.6747810840606689e-01 8.9181788265705109e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 509 -2.9888451099395752e-01</internalNodes>
          <leafValues>
            4.8014399409294128e-01 -7.2485052049160004e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 510 1.1514360085129738e-02</internalNodes>
          <leafValues>
            -5.9218250215053558e-02 4.0962639451026917e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 511 -2.6182739529758692e-03</internalNodes>
          <leafValues>
            -1.8478739261627197e-01 3.9801560342311859e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 512 -1.2829460320062935e-04</internalNodes>
          <leafValues>
            1.0710919648408890e-01 -2.4155279994010925e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 513 -6.9328160025179386e-03</internalNodes>
          <leafValues>
            -2.9845720529556274e-01 4.5657958835363388e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 514 -6.3937888480722904e-03</internalNodes>
          <leafValues>
            1.8363510072231293e-01 -1.4049419760704041e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 515 4.1702711023390293e-03</internalNodes>
          <leafValues>
            -5.1890019327402115e-02 1.0211580246686935e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 516 1.0390999726951122e-02</internalNodes>
          <leafValues>
            -1.3426989316940308e-01 1.9137309491634369e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 517 1.3004739768803120e-02</internalNodes>
          <leafValues>
            -4.5922718942165375e-02 3.0526930093765259e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 518 -4.0645021945238113e-03</internalNodes>
          <leafValues>
            -4.8477160930633545e-01 6.9338463246822357e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 519 -3.7050418904982507e-04</internalNodes>
          <leafValues>
            1.0090719908475876e-01 -6.8911276757717133e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 520 8.8882551062852144e-04</internalNodes>
          <leafValues>
            -1.6742789745330811e-01 1.8965889513492584e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 521 -4.8583559691905975e-03</internalNodes>
          <leafValues>
            -4.0789389610290527e-01 5.1483351737260818e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 522 4.4327960349619389e-03</internalNodes>
          <leafValues>
            -1.4262509346008301e-01 1.8987190723419189e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 523 2.0999709144234657e-02</internalNodes>
          <leafValues>
            9.2153772711753845e-02 -3.0773550271987915e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 524 -2.2740170825272799e-03</internalNodes>
          <leafValues>
            1.5176279842853546e-01 -1.6528700292110443e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 525 -1.5075540170073509e-02</internalNodes>
          <leafValues>
            -3.1039240956306458e-01 6.5696939826011658e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 526 9.5290662720799446e-03</internalNodes>
          <leafValues>
            -6.7693017423152924e-02 4.0692031383514404e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 527 1.2057139538228512e-03</internalNodes>
          <leafValues>
            4.3188188225030899e-02 -1.8454369902610779e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 528 -2.4757070466876030e-02</internalNodes>
          <leafValues>
            6.6890978813171387e-01 -3.4418709576129913e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 529 3.0408669263124466e-03</internalNodes>
          <leafValues>
            -1.3256159424781799e-01 9.5131039619445801e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 530 -1.5181970084086061e-03</internalNodes>
          <leafValues>
            1.2939499318599701e-01 -1.8558539450168610e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 531 -2.4845359846949577e-02</internalNodes>
          <leafValues>
            -7.3013377189636230e-01 9.4545418396592140e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 532 -8.1413304433226585e-03</internalNodes>
          <leafValues>
            1.1521799862384796e-01 -1.9038149714469910e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 533 -4.2350329458713531e-03</internalNodes>
          <leafValues>
            7.2733633220195770e-02 -1.0841889679431915e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 534 9.9135711789131165e-03</internalNodes>
          <leafValues>
            -8.4218956530094147e-02 4.7613239288330078e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 535 -2.7879870031028986e-03</internalNodes>
          <leafValues>
            -1.2846939265727997e-01 6.5720662474632263e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 536 2.6451589073985815e-03</internalNodes>
          <leafValues>
            8.9269757270812988e-02 -2.6216679811477661e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 537 -2.6683490723371506e-02</internalNodes>
          <leafValues>
            8.9870773255825043e-02 -9.6914090216159821e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 538 3.1197380740195513e-03</internalNodes>
          <leafValues>
            -1.1731740087270737e-01 2.2004860639572144e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 539 -2.3388290405273438e-01</internalNodes>
          <leafValues>
            -9.0905857086181641e-01 5.6871720589697361e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 540 1.0922820307314396e-02</internalNodes>
          <leafValues>
            8.5061840713024139e-02 -3.0725648999214172e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 541 9.4858808442950249e-03</internalNodes>
          <leafValues>
            -2.2317569702863693e-02 3.3745709061622620e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 542 -5.1413412438705564e-04</internalNodes>
          <leafValues>
            1.4860659837722778e-01 -1.5598359704017639e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 543 6.5561588853597641e-03</internalNodes>
          <leafValues>
            6.6693432629108429e-02 -2.9945740103721619e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 544 9.8293996416032314e-04</internalNodes>
          <leafValues>
            -1.9923539459705353e-01 1.4816479384899139e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 545 -1.8866109894588590e-03</internalNodes>
          <leafValues>
            8.6462371051311493e-02 -1.6101740300655365e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 546 2.7264489326626062e-03</internalNodes>
          <leafValues>
            -8.2049086689949036e-02 3.8679501414299011e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>60</maxWeakCount>
      <stageThreshold>-8.3464938402175903e-01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 547 -1.2602520175278187e-02</internalNodes>
          <leafValues>
            2.2423070669174194e-01 -3.3462178707122803e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 548 2.5659699458628893e-03</internalNodes>
          <leafValues>
            8.5756540298461914e-02 -3.2376360893249512e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 549 -1.2003120500594378e-03</internalNodes>
          <leafValues>
            1.4650370180606842e-01 -3.0306750535964966e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 550 4.7978968359529972e-03</internalNodes>
          <leafValues>
            -2.4725909531116486e-01 5.2705809473991394e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 551 -5.9380318270996213e-04</internalNodes>
          <leafValues>
            -1.8883049488067627e-01 1.5490350127220154e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 552 8.1017091870307922e-03</internalNodes>
          <leafValues>
            1.0764879733324051e-01 -2.4738930165767670e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 553 -6.8427261430770159e-04</internalNodes>
          <leafValues>
            1.8282850086688995e-01 -1.6550099849700928e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 554 4.5279348269104958e-03</internalNodes>
          <leafValues>
            -5.5668760091066360e-02 4.1382691264152527e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 555 3.8289420772343874e-03</internalNodes>
          <leafValues>
            -2.2222219407558441e-01 1.5282329916954041e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 556 -6.2229200266301632e-03</internalNodes>
          <leafValues>
            -3.2351690530776978e-01 6.8372547626495361e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 557 -6.1763478443026543e-03</internalNodes>
          <leafValues>
            -3.9912268519401550e-01 7.7707469463348389e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 558 -8.7820261716842651e-02</internalNodes>
          <leafValues>
            5.8577078580856323e-01 -5.3584650158882141e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 559 -6.8017458543181419e-03</internalNodes>
          <leafValues>
            -4.3307110667228699e-01 6.2693849205970764e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 560 1.0741569567471743e-03</internalNodes>
          <leafValues>
            -1.1966490000486374e-01 5.5397849529981613e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 561 -3.0490919947624207e-02</internalNodes>
          <leafValues>
            -2.3663240671157837e-01 1.0002999752759933e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 562 5.1879119127988815e-02</internalNodes>
          <leafValues>
            -3.6418840289115906e-02 7.3392897844314575e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 563 8.6805049795657396e-04</internalNodes>
          <leafValues>
            -1.7705479264259338e-01 1.4985239505767822e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 564 4.8424140550196171e-03</internalNodes>
          <leafValues>
            -4.6208251267671585e-02 1.3162529468536377e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 565 9.1674225404858589e-03</internalNodes>
          <leafValues>
            9.9181063473224640e-02 -2.0292450487613678e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 566 -5.6356228888034821e-03</internalNodes>
          <leafValues>
            8.7860167026519775e-02 -3.7438090890645981e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 567 -3.8375150412321091e-02</internalNodes>
          <leafValues>
            4.9721479415893555e-01 -4.3815169483423233e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 568 8.9894384145736694e-03</internalNodes>
          <leafValues>
            9.4126552343368530e-02 -3.0227750539779663e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 569 -1.1650560190901160e-04</internalNodes>
          <leafValues>
            1.3361050188541412e-01 -1.8932069838047028e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 570 -6.6462112590670586e-04</internalNodes>
          <leafValues>
            7.7972702682018280e-02 -1.3508260250091553e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 571 -1.2656490318477154e-02</internalNodes>
          <leafValues>
            -3.6913019418716431e-01 6.4613893628120422e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 572 -4.3929531238973141e-03</internalNodes>
          <leafValues>
            2.6696819067001343e-01 -8.8650099933147430e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 573 -1.2583639472723007e-03</internalNodes>
          <leafValues>
            2.0614829659461975e-01 -1.0952439904212952e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 574 -1.1131940409541130e-02</internalNodes>
          <leafValues>
            -4.1352048516273499e-01 6.2840126454830170e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 575 3.0703889206051826e-03</internalNodes>
          <leafValues>
            -1.5591779351234436e-01 1.5018209815025330e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 576 3.5361549817025661e-03</internalNodes>
          <leafValues>
            6.2573492527008057e-02 -2.1869969367980957e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 577 2.8864629566669464e-02</internalNodes>
          <leafValues>
            -6.9561749696731567e-02 4.4892778992652893e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 578 -7.1035906672477722e-02</internalNodes>
          <leafValues>
            2.0991979539394379e-01 -3.6562878638505936e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 579 -1.1107679456472397e-03</internalNodes>
          <leafValues>
            -3.3020168542861938e-01 7.9758942127227783e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 580 7.9184047877788544e-02</internalNodes>
          <leafValues>
            -1.3226009905338287e-02 3.8603660464286804e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 581 1.3353509828448296e-02</internalNodes>
          <leafValues>
            5.8410558849573135e-02 -3.9250770211219788e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 582 5.0049051642417908e-02</internalNodes>
          <leafValues>
            -2.3318229243159294e-02 7.4593770503997803e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 583 -2.1859000623226166e-01</internalNodes>
          <leafValues>
            -8.4585267305374146e-01 2.5940530002117157e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 584 1.0064110159873962e-02</internalNodes>
          <leafValues>
            -1.0959850251674652e-01 2.1068529784679413e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 585 7.5430879369378090e-03</internalNodes>
          <leafValues>
            5.3567539900541306e-02 -3.3617278933525085e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 586 1.5817210078239441e-02</internalNodes>
          <leafValues>
            -1.9042259082198143e-02 2.2196899354457855e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 587 -1.7135319649241865e-04</internalNodes>
          <leafValues>
            1.7667369544506073e-01 -1.2068530172109604e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 588 6.6670849919319153e-03</internalNodes>
          <leafValues>
            7.0071838796138763e-02 -2.2137600183486938e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 589 2.7946738991886377e-03</internalNodes>
          <leafValues>
            -1.0509230196475983e-01 1.9277399778366089e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 590 -1.5057970304042101e-03</internalNodes>
          <leafValues>
            6.0012888163328171e-02 -1.2378510087728500e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 591 8.5329543799161911e-03</internalNodes>
          <leafValues>
            -4.7611240297555923e-02 3.9985141158103943e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 592 4.2939469218254089e-02</internalNodes>
          <leafValues>
            3.1611390411853790e-02 -1.9731660187244415e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 593 2.0308220759034157e-02</internalNodes>
          <leafValues>
            3.5055190324783325e-02 -5.1969397068023682e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 594 -7.7673741616308689e-03</internalNodes>
          <leafValues>
            -1.8817919492721558e-01 5.6889228522777557e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 595 2.1762759424746037e-03</internalNodes>
          <leafValues>
            -9.0948157012462616e-02 2.4575869739055634e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 596 -1.9813690334558487e-02</internalNodes>
          <leafValues>
            5.2904421091079712e-01 -3.8754951208829880e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 597 1.3035159558057785e-02</internalNodes>
          <leafValues>
            6.7918822169303894e-02 -3.0413469672203064e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 598 -1.9664920400828123e-03</internalNodes>
          <leafValues>
            -2.0626169443130493e-01 9.6140593290328979e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 599 -2.6359891053289175e-03</internalNodes>
          <leafValues>
            2.5085249543190002e-01 -8.3200961351394653e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 600 -2.2968810517340899e-03</internalNodes>
          <leafValues>
            2.9634681344032288e-01 -5.8743689209222794e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 601 -3.8644939195364714e-03</internalNodes>
          <leafValues>
            1.9411550462245941e-01 -1.0827559977769852e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 602 4.4517841160995886e-05</internalNodes>
          <leafValues>
            -2.4451869726181030e-01 1.0293029993772507e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 603 1.9567341078072786e-03</internalNodes>
          <leafValues>
            -1.0519249737262726e-01 2.2499999403953552e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 604 1.4188109897077084e-02</internalNodes>
          <leafValues>
            3.2100718468427658e-02 -5.9142422676086426e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 605 -1.3274629600346088e-04</internalNodes>
          <leafValues>
            7.4577853083610535e-02 -2.7654591202735901e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 606 2.0996380597352982e-02</internalNodes>
          <leafValues>
            -4.5735489577054977e-02 3.2947731018066406e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>68</maxWeakCount>
      <stageThreshold>-7.0352667570114136e-01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 607 -3.9841078221797943e-02</internalNodes>
          <leafValues>
            1.5186519920825958e-01 -2.9055249691009521e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 608 1.1327869724482298e-03</internalNodes>
          <leafValues>
            -1.1921630054712296e-01 1.2098889797925949e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 609 1.0022070491686463e-03</internalNodes>
          <leafValues>
            1.2088630348443985e-01 -2.5621330738067627e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 610 6.3866227865219116e-02</internalNodes>
          <leafValues>
            4.7628100961446762e-02 -8.6150348186492920e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 611 -3.0986019410192966e-03</internalNodes>
          <leafValues>
            -3.1975808739662170e-01 9.1434687376022339e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 612 6.5784230828285217e-03</internalNodes>
          <leafValues>
            -8.0473050475120544e-02 3.6123031377792358e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 613 4.5082601718604565e-03</internalNodes>
          <leafValues>
            -1.8215750157833099e-01 1.4672499895095825e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 614 -1.6526240855455399e-02</internalNodes>
          <leafValues>
            -1.2954659759998322e-01 6.6522419452667236e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 615 -4.1868099942803383e-03</internalNodes>
          <leafValues>
            -2.6552608609199524e-01 1.1237680166959763e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 616 5.6613027118146420e-04</internalNodes>
          <leafValues>
            1.1822649836540222e-01 -1.6119679808616638e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 617 2.0279800519347191e-03</internalNodes>
          <leafValues>
            -2.2618439793586731e-01 1.1263699829578400e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 618 -1.1969150044023991e-02</internalNodes>
          <leafValues>
            -2.7523440122604370e-01 8.3603866398334503e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 619 -2.8411731123924255e-01</internalNodes>
          <leafValues>
            4.0216109156608582e-01 -7.7971749007701874e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 620 -3.6587871145457029e-03</internalNodes>
          <leafValues>
            -2.9723858833312988e-01 6.3484713435173035e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 621 9.2046172358095646e-04</internalNodes>
          <leafValues>
            7.7872820198535919e-02 -2.9539081454277039e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 622 1.3571759685873985e-02</internalNodes>
          <leafValues>
            -7.2430767118930817e-02 3.4849750995635986e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 623 -3.1399999279528856e-03</internalNodes>
          <leafValues>
            -2.2088779509067535e-01 1.0072159767150879e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 624 6.9894008338451385e-03</internalNodes>
          <leafValues>
            5.9188209474086761e-02 -1.4137220382690430e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 625 -5.9609091840684414e-04</internalNodes>
          <leafValues>
            1.3563929498195648e-01 -1.5081329643726349e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 626 1.6805849736556411e-03</internalNodes>
          <leafValues>
            -7.8348256647586823e-02 7.7357366681098938e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 627 -5.7250040117651224e-04</internalNodes>
          <leafValues>
            2.3572799563407898e-01 -1.1594360321760178e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 628 4.3474160134792328e-02</internalNodes>
          <leafValues>
            8.2836961373686790e-03 -3.7428310513496399e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 629 6.0316640883684158e-04</internalNodes>
          <leafValues>
            -1.7846900224685669e-01 1.6185760498046875e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 630 2.6881720870733261e-02</internalNodes>
          <leafValues>
            7.2419442236423492e-02 -1.7971959710121155e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 631 -4.9273878335952759e-02</internalNodes>
          <leafValues>
            4.6386399865150452e-01 -5.0276938825845718e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 632 -6.7225202918052673e-02</internalNodes>
          <leafValues>
            -1. 1.3532400131225586e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 633 2.0203770697116852e-01</internalNodes>
          <leafValues>
            -3.8748100399971008e-02 5.7211977243423462e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 634 3.1489748507738113e-02</internalNodes>
          <leafValues>
            4.5488908886909485e-02 -1.2539370357990265e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 635 -5.7097017997875810e-04</internalNodes>
          <leafValues>
            1.9619710743427277e-01 -1.0944739729166031e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 636 -7.8234989196062088e-03</internalNodes>
          <leafValues>
            6.7954361438751221e-02 -7.2075963020324707e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 637 -2.1555390208959579e-02</internalNodes>
          <leafValues>
            -2.8890660405158997e-01 9.9806018173694611e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 638 -8.3767198026180267e-02</internalNodes>
          <leafValues>
            -4.3685078620910645e-01 1.0792650282382965e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 639 -3.5752300173044205e-03</internalNodes>
          <leafValues>
            1.1191669851541519e-01 -1.9461460411548615e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 640 1.2265419587492943e-02</internalNodes>
          <leafValues>
            -6.5728217363357544e-02 3.2739359140396118e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 641 2.8762801084667444e-03</internalNodes>
          <leafValues>
            -1.8723809719085693e-01 1.1246989667415619e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 642 7.4190571904182434e-03</internalNodes>
          <leafValues>
            5.1525920629501343e-02 -2.6615419983863831e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 643 -4.9716630019247532e-03</internalNodes>
          <leafValues>
            1.5384270250797272e-01 -1.5141449868679047e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 644 2.0294899120926857e-02</internalNodes>
          <leafValues>
            -1.9532799720764160e-02 3.0571049451828003e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 645 1.3469019904732704e-02</internalNodes>
          <leafValues>
            6.2345318496227264e-02 -3.6343741416931152e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 646 6.8610929884016514e-03</internalNodes>
          <leafValues>
            -6.2487348914146423e-02 2.8820911049842834e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 647 -5.9594889171421528e-04</internalNodes>
          <leafValues>
            8.5537739098072052e-02 -2.4081380665302277e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 648 -4.0149871259927750e-02</internalNodes>
          <leafValues>
            -1. 1.5480610309168696e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 649 -2.7885669842362404e-03</internalNodes>
          <leafValues>
            -2.2338689863681793e-01 1.1001159995794296e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 650 -7.9318676143884659e-03</internalNodes>
          <leafValues>
            1.3043269515037537e-01 -2.8859179466962814e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 651 -2.9607459509861656e-05</internalNodes>
          <leafValues>
            1.1876039952039719e-01 -1.7018820345401764e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 652 2.6092668995261192e-03</internalNodes>
          <leafValues>
            -6.9877780973911285e-02 1.5036509931087494e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 653 -4.5970208942890167e-02</internalNodes>
          <leafValues>
            5.6322151422500610e-01 -3.6318130791187286e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 654 9.0047682169824839e-04</internalNodes>
          <leafValues>
            3.2461058348417282e-02 -1.8973889946937561e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 655 -5.1712408661842346e-02</internalNodes>
          <leafValues>
            -8.5045510530471802e-01 2.0679740235209465e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 656 -1.4172409474849701e-01</internalNodes>
          <leafValues>
            -9.1004508733749390e-01 3.8531969767063856e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 657 -6.9771192967891693e-02</internalNodes>
          <leafValues>
            4.2144781351089478e-01 -5.5162269622087479e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 658 -7.5836889445781708e-03</internalNodes>
          <leafValues>
            -4.2189291119575500e-01 6.1964530497789383e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 659 -1.2404819717630744e-03</internalNodes>
          <leafValues>
            1.7558629810810089e-01 -1.3540640473365784e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 660 1.0614699684083462e-02</internalNodes>
          <leafValues>
            4.5083239674568176e-02 -2.5765570998191833e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 661 1.7647630302235484e-03</internalNodes>
          <leafValues>
            -1.1009249836206436e-01 2.4041210114955902e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 662 3.7170480936765671e-03</internalNodes>
          <leafValues>
            -7.6920822262763977e-02 2.0119519531726837e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 663 1.5280679799616337e-02</internalNodes>
          <leafValues>
            5.8605119585990906e-02 -3.6220121383666992e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 664 -8.1635616719722748e-02</internalNodes>
          <leafValues>
            5.2819788455963135e-01 -4.3608970940113068e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 665 -2.4431939236819744e-03</internalNodes>
          <leafValues>
            -2.4369360506534576e-01 8.4384277462959290e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 666 -1.2289900332689285e-03</internalNodes>
          <leafValues>
            1.0332729667425156e-01 -9.7442328929901123e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 667 6.9271848769858479e-04</internalNodes>
          <leafValues>
            -1.1367750167846680e-01 1.6121849417686462e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 668 9.9380649626255035e-03</internalNodes>
          <leafValues>
            5.2774678915739059e-02 -1.5222820639610291e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 669 -1.8377749249339104e-02</internalNodes>
          <leafValues>
            4.6800789237022400e-01 -4.2411230504512787e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 670 -3.0569550581276417e-03</internalNodes>
          <leafValues>
            1.2866629660129547e-01 -9.8308563232421875e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 671 -1.8440110143274069e-03</internalNodes>
          <leafValues>
            -2.7592489123344421e-01 1.0050299763679504e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 672 5.6205368600785732e-03</internalNodes>
          <leafValues>
            -7.0716217160224915e-02 1.6734069585800171e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 673 3.4157470799982548e-03</internalNodes>
          <leafValues>
            5.2378088235855103e-02 -5.0982749462127686e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 674 -3.0376210343092680e-03</internalNodes>
          <leafValues>
            1.4243629574775696e-01 -6.3037060201168060e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>67</maxWeakCount>
      <stageThreshold>-7.4644768238067627e-01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 675 1.0126640088856220e-02</internalNodes>
          <leafValues>
            -2.1863789856433868e-01 1.7513489723205566e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 676 -2.6893198955804110e-03</internalNodes>
          <leafValues>
            -3.2822969555854797e-01 9.9838256835937500e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 677 -1.5573530457913876e-02</internalNodes>
          <leafValues>
            1.9594019651412964e-01 -2.2535979747772217e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 678 4.9326270818710327e-03</internalNodes>
          <leafValues>
            4.9988470971584320e-02 -5.3175377845764160e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 679 -7.6638202881440520e-04</internalNodes>
          <leafValues>
            -2.6926669478416443e-01 1.1751429736614227e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 680 -1.2552300177048892e-04</internalNodes>
          <leafValues>
            6.9110788404941559e-02 -8.1727392971515656e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 681 -1.4519299838866573e-05</internalNodes>
          <leafValues>
            1.1483950167894363e-01 -2.3017129302024841e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 682 -1.6113840043544769e-02</internalNodes>
          <leafValues>
            5.0956588983535767e-01 -3.7494029849767685e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 683 5.5138790048658848e-03</internalNodes>
          <leafValues>
            -7.8787550330162048e-02 2.3771439492702484e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 684 8.7763823568820953e-02</internalNodes>
          <leafValues>
            1.3863979838788509e-02 -8.9777380228042603e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 685 -1.2825570069253445e-02</internalNodes>
          <leafValues>
            -3.9504998922348022e-01 5.5546328425407410e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 686 8.2099979044869542e-04</internalNodes>
          <leafValues>
            -1.2663979828357697e-01 1.9081629812717438e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 687 -1.2775770155712962e-03</internalNodes>
          <leafValues>
            1.1065080016851425e-01 -1.9801099598407745e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 688 -2.5229719281196594e-01</internalNodes>
          <leafValues>
            -8.1039828062057495e-01 8.3870543166995049e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 689 7.0347747532650828e-04</internalNodes>
          <leafValues>
            -2.1380549669265747e-01 9.8673596978187561e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 690 1.0717480443418026e-02</internalNodes>
          <leafValues>
            8.4470443427562714e-02 -2.6063749194145203e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 691 5.1081487908959389e-03</internalNodes>
          <leafValues>
            -5.5732220411300659e-02 4.1447860002517700e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 692 -1.9006159156560898e-02</internalNodes>
          <leafValues>
            -3.7475249171257019e-01 7.9524833709001541e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 693 1.1136929970234632e-03</internalNodes>
          <leafValues>
            -2.2650149464607239e-01 1.0789389908313751e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 694 1.1141769587993622e-02</internalNodes>
          <leafValues>
            -4.2054798454046249e-02 1.3697710633277893e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 695 1.2054879916831851e-03</internalNodes>
          <leafValues>
            9.2105977237224579e-02 -2.3083679378032684e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 696 -2.0797130127903074e-04</internalNodes>
          <leafValues>
            8.4210596978664398e-02 -6.6967681050300598e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 697 -1.6412649303674698e-02</internalNodes>
          <leafValues>
            4.2269191145896912e-01 -4.9638699740171432e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 698 7.0363390259444714e-03</internalNodes>
          <leafValues>
            9.0550661087036133e-02 -2.7322870492935181e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 699 -8.4774550050497055e-03</internalNodes>
          <leafValues>
            -1.9004869461059570e-01 1.0416539758443832e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 700 -8.7799631059169769e-02</internalNodes>
          <leafValues>
            -1. 4.5551471412181854e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 701 -4.6731110662221909e-02</internalNodes>
          <leafValues>
            4.1607761383056641e-01 -6.7924611270427704e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 702 7.4915830045938492e-03</internalNodes>
          <leafValues>
            4.7516189515590668e-02 -4.4306200742721558e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 703 8.6966790258884430e-03</internalNodes>
          <leafValues>
            -3.9423149079084396e-02 5.2188277244567871e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 704 -6.4137862063944340e-03</internalNodes>
          <leafValues>
            -2.4749429523944855e-01 1.1350250244140625e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 705 6.4909840002655983e-03</internalNodes>
          <leafValues>
            -2.0237590372562408e-01 1.1887309700250626e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 706 1.1677639558911324e-03</internalNodes>
          <leafValues>
            -9.8187439143657684e-02 1.4470459520816803e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 707 8.0650653690099716e-03</internalNodes>
          <leafValues>
            3.0806429684162140e-02 -5.7410538196563721e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 708 -6.1450549401342869e-03</internalNodes>
          <leafValues>
            1.4213280379772186e-01 -1.2155479937791824e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 709 3.3926900941878557e-03</internalNodes>
          <leafValues>
            -6.9425463676452637e-02 3.7945500016212463e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 710 2.5861251354217529e-01</internalNodes>
          <leafValues>
            -8.0964984372258186e-03 5.7324391603469849e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 711 4.6327650547027588e-02</internalNodes>
          <leafValues>
            9.3428269028663635e-02 -2.9274320602416992e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 712 -1.4053919585421681e-05</internalNodes>
          <leafValues>
            5.9584300965070724e-02 -1.2193849682807922e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 713 -5.5521689355373383e-03</internalNodes>
          <leafValues>
            -3.0268138647079468e-01 7.9481996595859528e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 714 -7.1974180638790131e-02</internalNodes>
          <leafValues>
            5.9862488508224487e-01 -3.2414238899946213e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 715 -1.1097419774159789e-03</internalNodes>
          <leafValues>
            -2.2289000451564789e-01 9.4809576869010925e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 716 1.1012280359864235e-02</internalNodes>
          <leafValues>
            -5.0954710692167282e-02 2.1996709704399109e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 717 -1.0663530230522156e-01</internalNodes>
          <leafValues>
            -7.8257107734680176e-01 2.3075709119439125e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 718 2.6826610788702965e-02</internalNodes>
          <leafValues>
            -3.3334378153085709e-02 3.2825571298599243e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 719 1.6480779275298119e-02</internalNodes>
          <leafValues>
            2.4793079122900963e-02 -7.9102367162704468e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 720 1.4533529756590724e-03</internalNodes>
          <leafValues>
            -4.7377821058034897e-02 1.8299889564514160e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 721 4.6536721289157867e-02</internalNodes>
          <leafValues>
            -4.2217779904603958e-02 4.7201961278915405e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 722 1.3604049570858479e-02</internalNodes>
          <leafValues>
            7.1543172001838684e-02 -2.8175559639930725e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 723 2.9868748970329762e-03</internalNodes>
          <leafValues>
            -1.2019319832324982e-01 1.5165250003337860e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 724 7.5455583631992340e-02</internalNodes>
          <leafValues>
            7.6729329302906990e-03 -3.7560600042343140e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 725 -2.1207109093666077e-03</internalNodes>
          <leafValues>
            1.1624389886856079e-01 -1.5187309682369232e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 726 4.6092201955616474e-03</internalNodes>
          <leafValues>
            5.2315160632133484e-02 -2.3050600290298462e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 727 1.0207670275121927e-03</internalNodes>
          <leafValues>
            -1.1380010098218918e-01 1.7626440525054932e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 728 6.2532532028853893e-03</internalNodes>
          <leafValues>
            6.1674360185861588e-02 -3.4915238618850708e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 729 2.8322400525212288e-02</internalNodes>
          <leafValues>
            -3.9958149194717407e-02 5.2392977476119995e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 730 -1.6342360526323318e-02</internalNodes>
          <leafValues>
            -1.2563559412956238e-01 4.0041740983724594e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 731 -1.8282469827681780e-03</internalNodes>
          <leafValues>
            9.1135032474994659e-02 -1.9224719703197479e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 732 4.4616919010877609e-02</internalNodes>
          <leafValues>
            -1.7582910135388374e-02 3.0281931161880493e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 733 3.5677649429999292e-04</internalNodes>
          <leafValues>
            -8.7897412478923798e-02 2.2339150309562683e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 734 -4.5413200859911740e-04</internalNodes>
          <leafValues>
            6.5522827208042145e-02 -9.9679380655288696e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 735 1.5353029593825340e-03</internalNodes>
          <leafValues>
            6.8590000271797180e-02 -2.9728370904922485e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 736 2.1600390318781137e-03</internalNodes>
          <leafValues>
            -8.9736528694629669e-02 8.0284543335437775e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 737 -5.9745612088590860e-04</internalNodes>
          <leafValues>
            2.1873860061168671e-01 -1.1398520320653915e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 738 -1.2356050312519073e-02</internalNodes>
          <leafValues>
            -2.9350760579109192e-01 6.4420320093631744e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 739 -3.2670930027961731e-01</internalNodes>
          <leafValues>
            3.8920149207115173e-01 -4.9165409058332443e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 740 8.7828626856207848e-03</internalNodes>
          <leafValues>
            8.6186192929744720e-02 -2.2631849348545074e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 741 3.3569689840078354e-03</internalNodes>
          <leafValues>
            -9.1194286942481995e-02 2.1264100074768066e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>75</maxWeakCount>
      <stageThreshold>-7.8030252456665039e-01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 742 -1.5290499664843082e-02</internalNodes>
          <leafValues>
            1.6011320054531097e-01 -2.1511940658092499e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 743 -5.9956451877951622e-03</internalNodes>
          <leafValues>
            -1.8299789726734161e-01 3.7886500358581543e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 744 6.2301359139382839e-04</internalNodes>
          <leafValues>
            -1.2199199944734573e-01 2.1163250505924225e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 745 5.8087380602955818e-04</internalNodes>
          <leafValues>
            -2.2747389972209930e-01 7.6958037912845612e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 746 -2.8277048841118813e-03</internalNodes>
          <leafValues>
            2.7597460150718689e-01 -7.8942306339740753e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 747 2.1096320822834969e-02</internalNodes>
          <leafValues>
            4.1295919567346573e-02 -3.2933080196380615e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 748 -2.2117430344223976e-03</internalNodes>
          <leafValues>
            2.4672569334506989e-01 -7.3121666908264160e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 749 -2.3275949060916901e-03</internalNodes>
          <leafValues>
            -2.2825109958648682e-01 7.9285196959972382e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 750 -4.4754869304597378e-03</internalNodes>
          <leafValues>
            1.1744049936532974e-01 -1.9801409542560577e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 751 -2.5716619566082954e-03</internalNodes>
          <leafValues>
            3.7658710032701492e-02 -1.2148059904575348e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 752 1.5387970488518476e-03</internalNodes>
          <leafValues>
            -5.5973250418901443e-02 3.6923429369926453e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 753 -3.3066518604755402e-02</internalNodes>
          <leafValues>
            3.9160001277923584e-01 -7.7862940728664398e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 754 -8.5727721452713013e-02</internalNodes>
          <leafValues>
            -2.5174748897552490e-01 1.3543550670146942e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 755 -7.0333289913833141e-03</internalNodes>
          <leafValues>
            1.3328710198402405e-01 -1.5664640069007874e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 756 -6.8310517235659063e-05</internalNodes>
          <leafValues>
            9.9454201757907867e-02 -2.3412980139255524e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 757 -6.0546118766069412e-04</internalNodes>
          <leafValues>
            -1.7742669582366943e-01 1.0017810016870499e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 758 -2.2480569314211607e-03</internalNodes>
          <leafValues>
            -3.6424639821052551e-01 5.3501259535551071e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 759 -1.5090550296008587e-03</internalNodes>
          <leafValues>
            7.7575050294399261e-02 -9.4920717179775238e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 760 -5.8666180848376825e-05</internalNodes>
          <leafValues>
            1.2585939466953278e-01 -1.4529819786548615e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 761 3.5532109905034304e-03</internalNodes>
          <leafValues>
            -9.8626613616943359e-02 7.4326246976852417e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 762 -1.4601859729737043e-03</internalNodes>
          <leafValues>
            -3.3026841282844543e-01 6.3813462853431702e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 763 -2.3586049792356789e-04</internalNodes>
          <leafValues>
            1.0846760123968124e-01 -1.0571049898862839e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 764 1.4756060205399990e-02</internalNodes>
          <leafValues>
            -5.9472840279340744e-02 3.7792891263961792e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 765 -1.6795310378074646e-01</internalNodes>
          <leafValues>
            -6.6773468255996704e-01 1.7404930666089058e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 766 3.2017670571804047e-02</internalNodes>
          <leafValues>
            -2.3720450699329376e-01 9.6205927431583405e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 767 -6.1111792456358671e-04</internalNodes>
          <leafValues>
            1.3566890358924866e-01 -6.8121932446956635e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 768 -1.1586040258407593e-02</internalNodes>
          <leafValues>
            -2.9761460423469543e-01 6.4853250980377197e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 769 -1.1290679685771465e-03</internalNodes>
          <leafValues>
            1.3520470261573792e-01 -9.0693503618240356e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 770 1.8352170009166002e-03</internalNodes>
          <leafValues>
            -9.6694603562355042e-02 1.8725989758968353e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 771 -2.7584248781204224e-01</internalNodes>
          <leafValues>
            2.7460220456123352e-01 -1.6176709905266762e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 772 -5.2487280219793320e-02</internalNodes>
          <leafValues>
            -2.6295030117034912e-01 8.4279276430606842e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 773 -2.8409080579876900e-02</internalNodes>
          <leafValues>
            4.4033178687095642e-01 -4.6736340969800949e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 774 1.2234229594469070e-02</internalNodes>
          <leafValues>
            7.1391902863979340e-02 -2.9463478922843933e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 775 3.7752088159322739e-02</internalNodes>
          <leafValues>
            -3.2507140189409256e-02 6.2293910980224609e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 776 -1.3006339780986309e-02</internalNodes>
          <leafValues>
            -3.5619509220123291e-01 5.7085920125246048e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 777 -3.7061918992549181e-03</internalNodes>
          <leafValues>
            1.7485049366950989e-01 -1.0506869852542877e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 778 -4.8177209682762623e-03</internalNodes>
          <leafValues>
            1.4761090278625488e-01 -1.3700130581855774e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 779 -3.0726719647645950e-02</internalNodes>
          <leafValues>
            -2.1432609856128693e-01 3.4535329788923264e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 780 1.0044399648904800e-02</internalNodes>
          <leafValues>
            8.2472868263721466e-02 -2.1329440176486969e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 781 3.3808979787863791e-04</internalNodes>
          <leafValues>
            -5.6368399411439896e-02 8.4050692617893219e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 782 -3.4935539588332176e-04</internalNodes>
          <leafValues>
            1.5510140359401703e-01 -1.5465189516544342e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 783 8.5416442016139627e-04</internalNodes>
          <leafValues>
            7.4811212718486786e-02 -2.0761939883232117e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 784 -7.4278831016272306e-04</internalNodes>
          <leafValues>
            2.0695370435714722e-01 -1.1315040290355682e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 785 -4.1803911328315735e-02</internalNodes>
          <leafValues>
            7.7375417947769165e-01 -2.7391599491238594e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 786 -8.9303712593391538e-04</internalNodes>
          <leafValues>
            -2.8926849365234375e-01 8.3425313234329224e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 787 2.0034189801663160e-03</internalNodes>
          <leafValues>
            5.7899519801139832e-02 -2.1817860007286072e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 788 7.4933562427759171e-04</internalNodes>
          <leafValues>
            -1.3606220483779907e-01 1.6150030493736267e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 789 -8.9645422995090485e-02</internalNodes>
          <leafValues>
            -9.5717740058898926e-01 5.8882208541035652e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 790 -6.5244808793067932e-03</internalNodes>
          <leafValues>
            1.4521969854831696e-01 -1.6119849681854248e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 791 -2.8723690193146467e-03</internalNodes>
          <leafValues>
            1.0670810192823410e-01 -3.0505739152431488e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 792 2.2762219887226820e-03</internalNodes>
          <leafValues>
            -1.4573380351066589e-01 1.5590649843215942e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 793 4.3706637807190418e-03</internalNodes>
          <leafValues>
            -2.4369299411773682e-02 2.0724129676818848e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 794 1.1989739723503590e-03</internalNodes>
          <leafValues>
            8.8461942970752716e-02 -2.2536410391330719e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 795 -6.1923090834170580e-04</internalNodes>
          <leafValues>
            1.5108090639114380e-01 -9.9106341600418091e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 796 -1.0555429616943002e-03</internalNodes>
          <leafValues>
            1.5399299561977386e-01 -1.4410500228404999e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 797 2.3101890459656715e-02</internalNodes>
          <leafValues>
            -2.6107529178261757e-02 2.5875169038772583e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 798 6.7337458021938801e-03</internalNodes>
          <leafValues>
            6.4629636704921722e-02 -3.2299819588661194e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 799 1.4084229478612542e-03</internalNodes>
          <leafValues>
            8.5755072534084320e-02 -1.4947549998760223e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 800 -2.3923629487399012e-04</internalNodes>
          <leafValues>
            1.8700890243053436e-01 -1.0941530019044876e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 801 2.2198690567165613e-04</internalNodes>
          <leafValues>
            -1.9517560303211212e-01 5.9587858617305756e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 802 2.8156230691820383e-03</internalNodes>
          <leafValues>
            -8.9527882635593414e-02 2.2894319891929626e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 803 7.8730508685112000e-03</internalNodes>
          <leafValues>
            6.4139701426029205e-02 -1.7174859344959259e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 804 1.0448540560901165e-03</internalNodes>
          <leafValues>
            -2.0927239954471588e-01 1.1022809892892838e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 805 -1.8041099607944489e-01</internalNodes>
          <leafValues>
            2.5460541248321533e-01 -3.1580239534378052e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 806 -1.8916819989681244e-01</internalNodes>
          <leafValues>
            -8.1439048051834106e-01 3.0212750658392906e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 807 -4.8934340476989746e-02</internalNodes>
          <leafValues>
            4.8329269886016846e-01 -3.1813390552997589e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 808 -6.2278551049530506e-03</internalNodes>
          <leafValues>
            -2.2463080286979675e-01 9.3202292919158936e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 809 -3.6263489164412022e-03</internalNodes>
          <leafValues>
            9.7239963710308075e-02 -2.2094939649105072e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 810 2.0688530057668686e-02</internalNodes>
          <leafValues>
            -3.9044689387083054e-02 6.9668918848037720e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 811 -6.5703191794455051e-03</internalNodes>
          <leafValues>
            -1.5919350087642670e-01 3.7697389721870422e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 812 -2.7691440191119909e-03</internalNodes>
          <leafValues>
            -2.1777799725532532e-01 1.1075550317764282e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 813 -2.5391899980604649e-03</internalNodes>
          <leafValues>
            7.6753303408622742e-02 -1.2121020257472992e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 814 1.4522899873554707e-02</internalNodes>
          <leafValues>
            -4.6935468912124634e-02 4.4322049617767334e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 815 -4.8549640923738480e-03</internalNodes>
          <leafValues>
            -4.1040301322937012e-01 4.7296289354562759e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 816 -3.6202149931341410e-03</internalNodes>
          <leafValues>
            3.6707898974418640e-01 -5.0583109259605408e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>79</maxWeakCount>
      <stageThreshold>-8.1366151571273804e-01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 817 9.7794737666845322e-03</internalNodes>
          <leafValues>
            -1.9873769581317902e-01 1.8754990398883820e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 818 2.5764610618352890e-03</internalNodes>
          <leafValues>
            -1.6544049978256226e-01 1.1968299746513367e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 819 6.6844018874689937e-04</internalNodes>
          <leafValues>
            8.1187427043914795e-02 -2.6954218745231628e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 820 1.8919180147349834e-03</internalNodes>
          <leafValues>
            8.2398690283298492e-02 -1.9564670324325562e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 821 -8.2977651618421078e-04</internalNodes>
          <leafValues>
            -2.1381169557571411e-01 1.0152959823608398e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 822 -2.5124829262495041e-03</internalNodes>
          <leafValues>
            2.6497021317481995e-01 -8.1728130578994751e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 823 4.9220919609069824e-03</internalNodes>
          <leafValues>
            -1.3837899267673492e-01 1.7047420144081116e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 824 1.5432259533554316e-03</internalNodes>
          <leafValues>
            -2.3483499884605408e-01 1.2624679505825043e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 825 -7.5272549875080585e-03</internalNodes>
          <leafValues>
            -2.1902580559253693e-01 7.8214943408966064e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 826 -3.2087319414131343e-04</internalNodes>
          <leafValues>
            9.9803313612937927e-02 -1.0052630305290222e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 827 -5.6291592773050070e-04</internalNodes>
          <leafValues>
            1.4587800204753876e-01 -1.3194470107555389e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 828 -3.4248359501361847e-02</internalNodes>
          <leafValues>
            7.3179531097412109e-01 -2.5754369795322418e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 829 5.5207060649991035e-03</internalNodes>
          <leafValues>
            7.3829427361488342e-02 -2.4615940451622009e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 830 3.3663161098957062e-02</internalNodes>
          <leafValues>
            -5.0750829279422760e-02 5.1054477691650391e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 831 1.0605139657855034e-02</internalNodes>
          <leafValues>
            -1.9593380391597748e-01 9.6162728965282440e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 832 3.6454470828175545e-03</internalNodes>
          <leafValues>
            -1.0274770110845566e-01 1.8021290004253387e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 833 3.1658720225095749e-02</internalNodes>
          <leafValues>
            7.7415347099304199e-02 -2.3498320579528809e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 834 6.0496449470520020e-02</internalNodes>
          <leafValues>
            7.9810861498117447e-03 -5.8126330375671387e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 835 -2.1451190696097910e-04</internalNodes>
          <leafValues>
            -2.7141410112380981e-01 7.2448231279850006e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 836 -8.9069753885269165e-03</internalNodes>
          <leafValues>
            1.0864660143852234e-01 -3.7890978157520294e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 837 -3.1367139890789986e-03</internalNodes>
          <leafValues>
            2.3194080591201782e-01 -8.3242997527122498e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 838 -8.2477089017629623e-04</internalNodes>
          <leafValues>
            1.3757370412349701e-01 -4.0709521621465683e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 839 -3.8041090010665357e-04</internalNodes>
          <leafValues>
            9.9655948579311371e-02 -2.0115250349044800e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 840 3.0412159394472837e-03</internalNodes>
          <leafValues>
            4.8606388270854950e-02 -2.9261159896850586e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 841 -2.7135149575769901e-03</internalNodes>
          <leafValues>
            -2.0402909815311432e-01 8.7270192801952362e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 842 -1.1454220116138458e-01</internalNodes>
          <leafValues>
            2.6342248916625977e-01 -2.8976829722523689e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 843 -7.9219061881303787e-03</internalNodes>
          <leafValues>
            -2.3954220116138458e-01 7.8425459563732147e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 844 -6.4272403717041016e-02</internalNodes>
          <leafValues>
            3.8651049137115479e-01 -3.4981280565261841e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 845 2.0820159465074539e-02</internalNodes>
          <leafValues>
            3.6676738411188126e-02 -5.0909721851348877e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 846 4.7503421083092690e-03</internalNodes>
          <leafValues>
            -4.9171518534421921e-02 1.8542270362377167e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 847 -9.3589037656784058e-02</internalNodes>
          <leafValues>
            6.2822377681732178e-01 -2.5140469893813133e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 848 -6.8223377456888556e-04</internalNodes>
          <leafValues>
            4.0090799331665039e-02 -1.0250650346279144e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 849 -8.3058718591928482e-03</internalNodes>
          <leafValues>
            -2.1625949442386627e-01 8.5505023598670959e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 850 5.5919620208442211e-03</internalNodes>
          <leafValues>
            -6.5724261105060577e-02 6.1939451843500137e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 851 1.8336649518460035e-03</internalNodes>
          <leafValues>
            -1.0324809700250626e-01 2.5134149193763733e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 852 -4.4351099058985710e-03</internalNodes>
          <leafValues>
            -1.5100279450416565e-01 3.7323009222745895e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 853 -4.7271270304918289e-03</internalNodes>
          <leafValues>
            1.3500709831714630e-01 -1.5250219404697418e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 854 5.3573452169075608e-04</internalNodes>
          <leafValues>
            -6.0964770615100861e-02 7.1996733546257019e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 855 -1.3135100016370416e-04</internalNodes>
          <leafValues>
            1.2902179360389709e-01 -1.3107609748840332e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 856 4.0799290873110294e-03</internalNodes>
          <leafValues>
            4.9433309584856033e-02 -1.9467090070247650e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 857 -3.1066180672496557e-03</internalNodes>
          <leafValues>
            2.3984549939632416e-01 -7.1281567215919495e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 858 1.0999400168657303e-02</internalNodes>
          <leafValues>
            2.9017930850386620e-02 -3.8504680991172791e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 859 1.5001590363681316e-03</internalNodes>
          <leafValues>
            -8.3652436733245850e-02 1.8141129612922668e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 860 1.3700149953365326e-02</internalNodes>
          <leafValues>
            3.6753259599208832e-02 -4.5086589455604553e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 861 3.9507630281150341e-03</internalNodes>
          <leafValues>
            -6.9417111575603485e-02 2.1540710330009460e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 862 -8.5161393508315086e-03</internalNodes>
          <leafValues>
            1.0704089701175690e-01 -1.4857380092144012e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 863 1.7032850300893188e-03</internalNodes>
          <leafValues>
            -8.1896521151065826e-02 3.2398068904876709e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 864 -1.0852930136024952e-02</internalNodes>
          <leafValues>
            -1.3142329454421997e-01 9.9990189075469971e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 865 -3.7832378875464201e-03</internalNodes>
          <leafValues>
            9.7596637904644012e-02 -1.6081459820270538e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 866 1.3263260014355183e-02</internalNodes>
          <leafValues>
            6.8189077079296112e-02 -1.4820660650730133e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 867 -4.4276300817728043e-02</internalNodes>
          <leafValues>
            5.3883999586105347e-01 -3.4769881516695023e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 868 -1.6476439312100410e-02</internalNodes>
          <leafValues>
            -6.9341838359832764e-01 3.0285930261015892e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 869 1.5063960105180740e-02</internalNodes>
          <leafValues>
            5.0365351140499115e-02 -3.2215261459350586e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 870 5.3230069577693939e-02</internalNodes>
          <leafValues>
            4.0058908052742481e-03 -1.0000929832458496e+00</leafValues></_>
        <_>
          <internalNodes>
            0 -1 871 -1.2282089889049530e-01</internalNodes>
          <leafValues>
            4.0438568592071533e-01 -5.4661169648170471e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 872 -8.0205321311950684e-02</internalNodes>
          <leafValues>
            -1.8915909528732300e-01 3.5704288631677628e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 873 -1.1679669842123985e-03</internalNodes>
          <leafValues>
            -2.7641400694847107e-01 5.9974398463964462e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 874 -3.1197320204228163e-03</internalNodes>
          <leafValues>
            1.1307190358638763e-01 -7.2880730032920837e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 875 3.6612390540540218e-03</internalNodes>
          <leafValues>
            -4.7828570008277893e-02 3.9067369699478149e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 876 4.6034730039536953e-03</internalNodes>
          <leafValues>
            -4.7448419034481049e-02 3.6146968603134155e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 877 -1.0733479866757989e-03</internalNodes>
          <leafValues>
            1.1264870315790176e-01 -2.9074960947036743e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 878 -1.8310690298676491e-02</internalNodes>
          <leafValues>
            9.6729353070259094e-02 -1.0150820016860962e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 879 -6.8194739520549774e-02</internalNodes>
          <leafValues>
            -2.2048689424991608e-01 1.0977990180253983e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 880 8.9977607131004333e-03</internalNodes>
          <leafValues>
            -2.9652440920472145e-02 1.5059219300746918e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 881 2.6954131317324936e-04</internalNodes>
          <leafValues>
            -1.9917850196361542e-01 9.4677992165088654e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 882 5.9090729337185621e-04</internalNodes>
          <leafValues>
            -1.3240300118923187e-01 6.3088178634643555e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 883 5.5691739544272423e-03</internalNodes>
          <leafValues>
            1.0318289697170258e-01 -1.9276739656925201e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 884 -9.9434129893779755e-02</internalNodes>
          <leafValues>
            2.5911080837249756e-01 -4.3947871774435043e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 885 -9.6295922994613647e-03</internalNodes>
          <leafValues>
            -3.6871969699859619e-01 4.6506170183420181e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 886 -1.7397940391674638e-03</internalNodes>
          <leafValues>
            1.3736039400100708e-01 -6.9822482764720917e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 887 -1.3269430026412010e-02</internalNodes>
          <leafValues>
            4.5216149091720581e-01 -3.8461238145828247e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 888 2.5604839902371168e-03</internalNodes>
          <leafValues>
            5.4858781397342682e-02 -2.4963529407978058e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 889 -1.9173050532117486e-03</internalNodes>
          <leafValues>
            -2.5733208656311035e-01 6.7481383681297302e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 890 -3.7461649626493454e-02</internalNodes>
          <leafValues>
            5.9668248891830444e-01 -1.8121080473065376e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 891 -1.9658938981592655e-03</internalNodes>
          <leafValues>
            1.9501520693302155e-01 -9.0026341378688812e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 892 -3.2596408855170012e-03</internalNodes>
          <leafValues>
            -3.5647168755531311e-01 4.6495281159877777e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 893 -1.2043650262057781e-02</internalNodes>
          <leafValues>
            3.7508749961853027e-01 -5.3072199225425720e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 894 4.1690650396049023e-03</internalNodes>
          <leafValues>
            -4.1845761239528656e-02 1.1177790164947510e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 895 1.4214499853551388e-02</internalNodes>
          <leafValues>
            7.1965761482715607e-02 -2.6777520775794983e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>81</maxWeakCount>
      <stageThreshold>-3.0813199996948242e+01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 896 -1.2230969965457916e-02</internalNodes>
          <leafValues>
            1.4567610621452332e-01 -2.4045179784297943e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 897 -5.5717672221362591e-03</internalNodes>
          <leafValues>
            -1.8789610266685486e-01 4.0596708655357361e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 898 -5.5606552632525563e-04</internalNodes>
          <leafValues>
            1.6649569571018219e-01 -1.1817839741706848e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 899 8.3173572784289718e-04</internalNodes>
          <leafValues>
            -1.4224030077457428e-01 4.1616160422563553e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 900 -8.7869318667799234e-04</internalNodes>
          <leafValues>
            -1.6430449485778809e-01 1.5523290634155273e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 901 -1.3641480356454849e-02</internalNodes>
          <leafValues>
            3.0867528915405273e-01 -2.7172269299626350e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 902 1.4917860426066909e-05</internalNodes>
          <leafValues>
            -1.5592050552368164e-01 1.0176579654216766e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 903 8.7703643366694450e-03</internalNodes>
          <leafValues>
            6.1582878232002258e-02 -3.0546051263809204e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 904 7.5755198486149311e-03</internalNodes>
          <leafValues>
            -6.8759873509407043e-02 2.9675748944282532e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 905 4.9841161817312241e-02</internalNodes>
          <leafValues>
            1.0127910412847996e-02 -7.9213422536849976e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 906 -1.1090819723904133e-02</internalNodes>
          <leafValues>
            1.8339020013809204e-01 -1.0113699734210968e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 907 -8.5937082767486572e-02</internalNodes>
          <leafValues>
            -4.1994568705558777e-01 1.5568479895591736e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 908 -1.0151329915970564e-03</internalNodes>
          <leafValues>
            1.1474460363388062e-01 -1.6091680526733398e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 909 -1.3470250181853771e-02</internalNodes>
          <leafValues>
            -3.0626448988914490e-01 5.3186140954494476e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 910 1.6635110601782799e-02</internalNodes>
          <leafValues>
            -4.3458938598632812e-02 4.4043311476707458e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 911 -2.2650870960205793e-03</internalNodes>
          <leafValues>
            1.5985119342803955e-01 -1.2725980579853058e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 912 7.0288166403770447e-02</internalNodes>
          <leafValues>
            6.4891628921031952e-02 -2.3496179282665253e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 913 2.9186379164457321e-02</internalNodes>
          <leafValues>
            -2.0920279622077942e-01 8.9257873594760895e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 914 -5.0624469295144081e-03</internalNodes>
          <leafValues>
            3.4374091029167175e-01 -6.2093049287796021e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 915 2.9356318991631269e-03</internalNodes>
          <leafValues>
            -1.4249369502067566e-01 4.5412261039018631e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 916 -6.7740739323198795e-03</internalNodes>
          <leafValues>
            3.1641799211502075e-01 -4.9601629376411438e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 917 -1.4607170305680484e-04</internalNodes>
          <leafValues>
            1.0752049833536148e-01 -1.1540039628744125e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 918 -3.5684450995177031e-03</internalNodes>
          <leafValues>
            -4.1672629117965698e-01 4.2202819138765335e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 919 -2.0149808842688799e-03</internalNodes>
          <leafValues>
            1.0860130190849304e-01 -1.6349700093269348e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 920 -8.7240645661950111e-03</internalNodes>
          <leafValues>
            -2.2000640630722046e-01 9.0927027165889740e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 921 7.3565947823226452e-03</internalNodes>
          <leafValues>
            -1.0335700213909149e-01 1.6051970422267914e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 922 3.4252731129527092e-03</internalNodes>
          <leafValues>
            -6.9635637104511261e-02 3.1490880250930786e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 923 -5.7803248055279255e-03</internalNodes>
          <leafValues>
            -4.3639171123504639e-01 3.6127548664808273e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 924 -2.9641189612448215e-03</internalNodes>
          <leafValues>
            2.1797280013561249e-01 -7.7875941991806030e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 925 2.4028679355978966e-02</internalNodes>
          <leafValues>
            2.5940960273146629e-02 -5.7640588283538818e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 926 8.1514477729797363e-02</internalNodes>
          <leafValues>
            -3.4380380064249039e-02 5.7957500219345093e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 927 6.7858170950785279e-04</internalNodes>
          <leafValues>
            1.0398740321397781e-01 -2.3831090331077576e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 928 4.2639520019292831e-02</internalNodes>
          <leafValues>
            -4.1167970746755600e-02 4.0556749701499939e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 929 -4.0414459072053432e-03</internalNodes>
          <leafValues>
            -3.8652890920639038e-01 5.3053580224514008e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 930 4.2280308902263641e-02</internalNodes>
          <leafValues>
            1.5058529563248158e-02 -9.6623957157135010e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 931 -7.3401766712777317e-05</internalNodes>
          <leafValues>
            8.4438636898994446e-02 -1.0468550026416779e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 932 4.7503020614385605e-03</internalNodes>
          <leafValues>
            -3.8135491311550140e-02 4.3066629767417908e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 933 1.7291309777647257e-03</internalNodes>
          <leafValues>
            7.5733587145805359e-02 -1.5384200215339661e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 934 -4.8985757166519761e-04</internalNodes>
          <leafValues>
            1.3722479343414307e-01 -1.2631259858608246e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 935 -2.2209450253285468e-04</internalNodes>
          <leafValues>
            5.1139138638973236e-02 -6.6661313176155090e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 936 1.1202819878235459e-03</internalNodes>
          <leafValues>
            -1.0968499630689621e-01 1.5611450374126434e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 937 -2.0596029236912727e-02</internalNodes>
          <leafValues>
            -4.5425260066986084e-01 5.6112911552190781e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 938 -5.1287859678268433e-03</internalNodes>
          <leafValues>
            -3.9422529935836792e-01 4.4144820421934128e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 939 -4.3597300536930561e-03</internalNodes>
          <leafValues>
            1.9391660392284393e-01 -6.5949328243732452e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 940 4.7703061136417091e-04</internalNodes>
          <leafValues>
            -1.1900710314512253e-01 1.6375440359115601e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 941 -1.0993770323693752e-02</internalNodes>
          <leafValues>
            -2.9915741086006165e-01 2.8793500736355782e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 942 8.1108389422297478e-03</internalNodes>
          <leafValues>
            -4.8145949840545654e-02 3.8399958610534668e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 943 -3.6698309704661369e-03</internalNodes>
          <leafValues>
            8.8712036609649658e-02 -3.0650860071182251e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 944 1.3895990559831262e-03</internalNodes>
          <leafValues>
            -5.5156201124191284e-02 3.5109901428222656e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 945 1.2493750546127558e-03</internalNodes>
          <leafValues>
            -1.8023060262203217e-01 1.3490100204944611e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 946 5.5981278419494629e-03</internalNodes>
          <leafValues>
            7.9764246940612793e-02 -2.7847459912300110e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 947 -3.8133479654788971e-02</internalNodes>
          <leafValues>
            3.5153418779373169e-01 -1.7089430242776871e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 948 -4.6064890921115875e-03</internalNodes>
          <leafValues>
            -2.2194199264049530e-01 1.0675799846649170e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 949 -2.3793010413646698e-01</internalNodes>
          <leafValues>
            4.0079510211944580e-01 -6.2151808291673660e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 950 1.2010410428047180e-02</internalNodes>
          <leafValues>
            5.8646921068429947e-02 -3.5234829783439636e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 951 8.4618777036666870e-03</internalNodes>
          <leafValues>
            -4.1455499827861786e-02 3.9362218976020813e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 952 -1.4482599683105946e-02</internalNodes>
          <leafValues>
            -2.7049958705902100e-01 6.9400496780872345e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 953 2.5672810152173042e-03</internalNodes>
          <leafValues>
            -8.2357987761497498e-02 2.2959560155868530e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 954 6.8167857825756073e-03</internalNodes>
          <leafValues>
            8.5212066769599915e-02 -2.2813120484352112e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 955 -6.4145028591156006e-04</internalNodes>
          <leafValues>
            1.3260249793529510e-01 -8.1091962754726410e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 956 3.8798429886810482e-04</internalNodes>
          <leafValues>
            -2.1800529956817627e-01 8.2977667450904846e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 957 2.6308000087738037e-02</internalNodes>
          <leafValues>
            -2.5558909401297569e-02 5.8989650011062622e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 958 2.0907879807054996e-03</internalNodes>
          <leafValues>
            5.7611741125583649e-02 -3.0286490917205811e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 959 -1.1132369749248028e-02</internalNodes>
          <leafValues>
            -1.3822869956493378e-01 4.2258080095052719e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 960 -1.5296150231733918e-03</internalNodes>
          <leafValues>
            9.1749697923660278e-02 -2.2181099653244019e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 961 6.7247601691633463e-04</internalNodes>
          <leafValues>
            -6.7084349691867828e-02 7.9762071371078491e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 962 1.0386659763753414e-02</internalNodes>
          <leafValues>
            -7.4621170759201050e-02 2.2916689515113831e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 963 6.2723900191485882e-04</internalNodes>
          <leafValues>
            -8.6500599980354309e-02 9.7814910113811493e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 964 1.5324779786169529e-02</internalNodes>
          <leafValues>
            8.0094330012798309e-02 -2.2011950612068176e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 965 -8.7603963911533356e-03</internalNodes>
          <leafValues>
            3.1290820240974426e-01 -5.9373341500759125e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 966 -2.3745700309518725e-04</internalNodes>
          <leafValues>
            1.1855959892272949e-01 -1.4514200389385223e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 967 -1.0718279518187046e-03</internalNodes>
          <leafValues>
            1.2567649781703949e-01 -5.3101938217878342e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 968 5.3873867727816105e-04</internalNodes>
          <leafValues>
            -1.0715659707784653e-01 1.6037760674953461e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 969 -6.9268636405467987e-02</internalNodes>
          <leafValues>
            -7.9294067621231079e-01 8.2057341933250427e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 970 1.0430130176246166e-02</internalNodes>
          <leafValues>
            5.1620200276374817e-02 -3.3472689986228943e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 971 7.1888908743858337e-02</internalNodes>
          <leafValues>
            1.5941270394250751e-03 -8.5840928554534912e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 972 2.0217420533299446e-02</internalNodes>
          <leafValues>
            -3.9817400276660919e-02 4.6351060271263123e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 973 5.8006029576063156e-03</internalNodes>
          <leafValues>
            -2.1701389923691750e-02 9.9040143191814423e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 974 3.5261210054159164e-02</internalNodes>
          <leafValues>
            1.7082870006561279e-02 -1.0000469684600830e+00</leafValues></_>
        <_>
          <internalNodes>
            0 -1 975 -4.5255878567695618e-01</internalNodes>
          <leafValues>
            -9.1292119026184082e-01 5.2670161239802837e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 976 -7.5286221690475941e-03</internalNodes>
          <leafValues>
            -5.2581560611724854e-01 2.2044740617275238e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>89</maxWeakCount>
      <stageThreshold>-3.0780099868774414e+01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 977 2.9085609130561352e-03</internalNodes>
          <leafValues>
            -2.0195980370044708e-01 1.6118539869785309e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 978 -6.4552230760455132e-03</internalNodes>
          <leafValues>
            -1.8676100671291351e-01 3.5359650850296021e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 979 2.7815890498459339e-03</internalNodes>
          <leafValues>
            -1.2228749692440033e-01 2.0362569391727448e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 980 -7.6125850901007652e-03</internalNodes>
          <leafValues>
            -3.6965709924697876e-01 3.9566628634929657e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 981 -2.5900858640670776e-01</internalNodes>
          <leafValues>
            6.4312630891799927e-01 3.1312569626607001e-04</leafValues></_>
        <_>
          <internalNodes>
            0 -1 982 4.6097189188003540e-03</internalNodes>
          <leafValues>
            -2.7262160554528236e-02 2.1891650557518005e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 983 -1.4135500416159630e-02</internalNodes>
          <leafValues>
            7.6006792485713959e-02 -2.6031088829040527e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 984 -5.9708990156650543e-03</internalNodes>
          <leafValues>
            -1.9146460294723511e-01 1.1078900098800659e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 985 -1.0699110571295023e-03</internalNodes>
          <leafValues>
            9.0127058327198029e-02 -1.9876359403133392e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 986 1.5315730124711990e-02</internalNodes>
          <leafValues>
            5.1883369684219360e-02 -3.1069299578666687e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 987 -7.3937349952757359e-05</internalNodes>
          <leafValues>
            1.0555309802293777e-01 -1.6768750548362732e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 988 -8.1876888871192932e-02</internalNodes>
          <leafValues>
            4.6053099632263184e-01 -3.8276348263025284e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 989 -8.8246334344148636e-03</internalNodes>
          <leafValues>
            -3.3107680082321167e-01 6.9674566388130188e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 990 -3.7569031119346619e-03</internalNodes>
          <leafValues>
            -2.7566310763359070e-01 6.9375626742839813e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 991 -3.6343189422041178e-03</internalNodes>
          <leafValues>
            1.6658850014209747e-01 -1.2031579762697220e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 992 2.1979490295052528e-02</internalNodes>
          <leafValues>
            -2.2316349670290947e-02 3.4402579069137573e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 993 6.1386551707983017e-02</internalNodes>
          <leafValues>
            1.7906000837683678e-02 -8.8129872083663940e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 994 2.7061739936470985e-02</internalNodes>
          <leafValues>
            -3.2444350421428680e-02 2.8866448998451233e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 995 -9.5964036881923676e-03</internalNodes>
          <leafValues>
            -3.0743318796157837e-01 5.2499480545520782e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 996 -1.7550870543345809e-03</internalNodes>
          <leafValues>
            1.0434249788522720e-01 -1.1126209795475006e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 997 1.6808100044727325e-03</internalNodes>
          <leafValues>
            -1.1712419986724854e-01 1.5606869757175446e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 998 -1.3623350532725453e-03</internalNodes>
          <leafValues>
            2.2637459635734558e-01 -8.6454801261425018e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 999 -3.6580429878085852e-03</internalNodes>
          <leafValues>
            -3.9829111099243164e-01 4.7143589705228806e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1000 5.2668720483779907e-02</internalNodes>
          <leafValues>
            -1.9696790724992752e-02 4.2998239398002625e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1001 -3.4802549635060132e-04</internalNodes>
          <leafValues>
            9.1115236282348633e-02 -2.0480670034885406e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1002 1.2204200029373169e-03</internalNodes>
          <leafValues>
            3.3061511814594269e-02 -1.7324869334697723e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1003 -9.4577670097351074e-03</internalNodes>
          <leafValues>
            2.9774200916290283e-01 -5.8979131281375885e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1004 -1.7641530139371753e-03</internalNodes>
          <leafValues>
            -9.6304766833782196e-02 6.5304636955261230e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1005 8.1057827919721603e-03</internalNodes>
          <leafValues>
            5.7158369570970535e-02 -3.1123921275138855e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1006 1.3963400386273861e-02</internalNodes>
          <leafValues>
            -3.5234641283750534e-02 3.5719850659370422e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1007 -3.1854680273681879e-03</internalNodes>
          <leafValues>
            -2.1528400480747223e-01 7.6040878891944885e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1008 -4.3546650558710098e-03</internalNodes>
          <leafValues>
            -8.3892293274402618e-02 2.8290690854191780e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1009 -1.6740639694035053e-03</internalNodes>
          <leafValues>
            1.5145839750766754e-01 -1.1756320297718048e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1010 -2.7018489781767130e-03</internalNodes>
          <leafValues>
            1.3833570480346680e-01 -5.0832830369472504e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1011 2.2117499611340463e-04</internalNodes>
          <leafValues>
            -2.3960849642753601e-01 7.5004346668720245e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1012 2.2773200646042824e-02</internalNodes>
          <leafValues>
            -2.2433629259467125e-02 3.7049260735511780e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1013 9.5928199589252472e-03</internalNodes>
          <leafValues>
            9.7205437719821930e-02 -1.7737109959125519e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1014 3.3168029040098190e-03</internalNodes>
          <leafValues>
            -5.6414358317852020e-02 9.1938421130180359e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1015 -2.3929888848215342e-03</internalNodes>
          <leafValues>
            2.1076680719852448e-01 -9.2880353331565857e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1016 -1.0766570456326008e-02</internalNodes>
          <leafValues>
            -1.2974379956722260e-01 5.9958908706903458e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1017 9.9714798852801323e-04</internalNodes>
          <leafValues>
            -1.4279229938983917e-01 1.4279709756374359e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1018 -6.6825798712670803e-03</internalNodes>
          <leafValues>
            -2.3819839954376221e-01 4.8119660466909409e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1019 -3.7201410159468651e-03</internalNodes>
          <leafValues>
            1.9953179359436035e-01 -9.0783573687076569e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1020 -1.8553409725427628e-02</internalNodes>
          <leafValues>
            -2.6621541380882263e-01 2.2872749716043472e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1021 3.0256200116127729e-03</internalNodes>
          <leafValues>
            -9.1106131672859192e-02 2.4559549987316132e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1022 -6.2146309763193130e-02</internalNodes>
          <leafValues>
            -1. 5.2797337993979454e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1023 1.7690609674900770e-03</internalNodes>
          <leafValues>
            -1.9379650056362152e-01 9.5696106553077698e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1024 -4.3277359509374946e-05</internalNodes>
          <leafValues>
            1.1374049633741379e-01 -1.3504849374294281e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1025 1.2779419776052237e-03</internalNodes>
          <leafValues>
            7.9606160521507263e-02 -2.3597019910812378e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1026 -4.4742479920387268e-02</internalNodes>
          <leafValues>
            1.8557150661945343e-01 -3.4167829900979996e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1027 2.7726130792871118e-04</internalNodes>
          <leafValues>
            -5.7937718927860260e-02 2.8903219103813171e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1028 5.6225471198558807e-02</internalNodes>
          <leafValues>
            1.3840789906680584e-02 -7.7199739217758179e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1029 8.6825769394636154e-03</internalNodes>
          <leafValues>
            -1.8263089656829834e-01 1.1423269659280777e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1030 -2.4038869887590408e-03</internalNodes>
          <leafValues>
            -1.9004139304161072e-01 6.5928563475608826e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1031 1.2840219773352146e-02</internalNodes>
          <leafValues>
            -3.6279100924730301e-02 4.5519340038299561e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1032 1.1061480036005378e-03</internalNodes>
          <leafValues>
            -6.3054688274860382e-02 8.1609472632408142e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1033 -4.6486179344356060e-03</internalNodes>
          <leafValues>
            -2.7108541131019592e-01 8.0167703330516815e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1034 6.4021991565823555e-03</internalNodes>
          <leafValues>
            -6.6946588456630707e-02 1.0634910315275192e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1035 -8.2370378077030182e-02</internalNodes>
          <leafValues>
            3.4517300128936768e-01 -4.8468429595232010e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1036 -3.7429828196763992e-02</internalNodes>
          <leafValues>
            -6.9630950689315796e-01 1.3054380193352699e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1037 1.0500400327146053e-02</internalNodes>
          <leafValues>
            9.6028283238410950e-02 -2.6362740993499756e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1038 6.8851239979267120e-02</internalNodes>
          <leafValues>
            3.7341150455176830e-03 -9.9989157915115356e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1039 1.0171310277655721e-03</internalNodes>
          <leafValues>
            -2.3500110208988190e-01 9.1097183525562286e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1040 -2.9057949781417847e-02</internalNodes>
          <leafValues>
            5.9977847337722778e-01 -3.6899000406265259e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1041 2.2022729739546776e-02</internalNodes>
          <leafValues>
            5.8034650981426239e-02 -3.2748758792877197e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1042 -4.3123541399836540e-03</internalNodes>
          <leafValues>
            2.2153949737548828e-01 -6.1332020908594131e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1043 1.0949710384011269e-02</internalNodes>
          <leafValues>
            2.1837379783391953e-02 -7.4662190675735474e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1044 4.3610740453004837e-02</internalNodes>
          <leafValues>
            -4.5098949223756790e-02 2.8109139204025269e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1045 7.7252179384231567e-02</internalNodes>
          <leafValues>
            2.0801780745387077e-02 -8.6648237705230713e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1046 -2.4023890495300293e-02</internalNodes>
          <leafValues>
            3.9884421229362488e-01 -3.5227119922637939e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1047 1.9559780135750771e-02</internalNodes>
          <leafValues>
            3.5944730043411255e-02 -5.1469117403030396e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1048 2.5917299091815948e-02</internalNodes>
          <leafValues>
            -1.2942669913172722e-02 4.1695970296859741e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1049 -4.6949301031418145e-04</internalNodes>
          <leafValues>
            1.6665999591350555e-01 -9.0680040419101715e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1050 -8.4590032696723938e-02</internalNodes>
          <leafValues>
            -5.9283781051635742e-01 7.2113061323761940e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1051 -8.9234940242022276e-04</internalNodes>
          <leafValues>
            1.7458200454711914e-01 -1.0072509944438934e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1052 -2.4009350687265396e-02</internalNodes>
          <leafValues>
            -3.9131438732147217e-01 2.2361040115356445e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1053 -4.7586968867108226e-04</internalNodes>
          <leafValues>
            1.8306100368499756e-01 -1.2541130185127258e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1054 2.9483099933713675e-03</internalNodes>
          <leafValues>
            6.5301053225994110e-02 -2.0387080311775208e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1055 3.6947780754417181e-03</internalNodes>
          <leafValues>
            -6.0878321528434753e-02 3.0403020977973938e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1056 -2.9413169249892235e-03</internalNodes>
          <leafValues>
            -3.0284491181373596e-01 4.7550499439239502e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1057 -7.1274640504270792e-04</internalNodes>
          <leafValues>
            1.6200789809226990e-01 -1.1822160333395004e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1058 2.4309750646352768e-02</internalNodes>
          <leafValues>
            -1.1442789807915688e-02 2.0453959703445435e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1059 -9.1473112115636468e-04</internalNodes>
          <leafValues>
            -2.0707829296588898e-01 7.5701341032981873e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1060 -3.6473390646278858e-03</internalNodes>
          <leafValues>
            2.4093860387802124e-01 -8.3565562963485718e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1061 1.2513220310211182e-02</internalNodes>
          <leafValues>
            4.1536040604114532e-02 -3.7487721443176270e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1062 6.2148571014404297e-03</internalNodes>
          <leafValues>
            2.0434129983186722e-02 -9.0057849884033203e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1063 -2.0954229403287172e-03</internalNodes>
          <leafValues>
            1.1625260114669800e-01 -1.8561770021915436e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1064 -2.1173250675201416e-01</internalNodes>
          <leafValues>
            -1. 2.4372090119868517e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1065 1.0188589803874493e-03</internalNodes>
          <leafValues>
            -7.5683966279029846e-02 2.9555431008338928e-01</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>77</maxWeakCount>
      <stageThreshold>-3.0694400787353516e+01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 1066 -2.4422600865364075e-02</internalNodes>
          <leafValues>
            2.0446979999542236e-01 -2.2299669682979584e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1067 1.0574000189080834e-03</internalNodes>
          <leafValues>
            -1.4355170726776123e-01 8.5603542625904083e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1068 2.5123930536210537e-03</internalNodes>
          <leafValues>
            1.0997679829597473e-01 -2.3044809699058533e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1069 1.2112739682197571e-01</internalNodes>
          <leafValues>
            3.3267501741647720e-02 -9.9910151958465576e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1070 2.9103590641170740e-03</internalNodes>
          <leafValues>
            -1.0391929745674133e-01 1.9292880594730377e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1071 -8.6717177182435989e-03</internalNodes>
          <leafValues>
            -2.7087220549583435e-01 9.9762901663780212e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1072 6.1140959151089191e-03</internalNodes>
          <leafValues>
            -1.1517100036144257e-01 2.0429219305515289e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1073 2.0590990781784058e-02</internalNodes>
          <leafValues>
            -3.3107578754425049e-02 4.6375459432601929e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1074 1.1507490416988730e-03</internalNodes>
          <leafValues>
            7.6014623045921326e-02 -2.7485209703445435e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1075 6.5449788235127926e-03</internalNodes>
          <leafValues>
            -1.1266589909791946e-01 5.0031568855047226e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1076 1.6102850204333663e-03</internalNodes>
          <leafValues>
            -1.8794959783554077e-01 1.1234410107135773e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1077 2.8527909889817238e-03</internalNodes>
          <leafValues>
            4.0457468479871750e-02 -8.4716461598873138e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1078 -4.0883300825953484e-03</internalNodes>
          <leafValues>
            1.2509189546108246e-01 -1.4850109815597534e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1079 1.6648479504510760e-03</internalNodes>
          <leafValues>
            -1.0346720367670059e-01 5.3585231304168701e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1080 -3.1635090708732605e-03</internalNodes>
          <leafValues>
            -3.3729389309883118e-01 6.1192918568849564e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1081 -1.0922599583864212e-02</internalNodes>
          <leafValues>
            4.5238488912582397e-01 -5.7903379201889038e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1082 -3.3356929197907448e-03</internalNodes>
          <leafValues>
            3.3880978822708130e-01 -6.4470112323760986e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1083 -3.0014500021934509e-02</internalNodes>
          <leafValues>
            -8.2835501432418823e-01 2.4696119129657745e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1084 -3.0110439658164978e-01</internalNodes>
          <leafValues>
            -8.3429050445556641e-01 1.4369309879839420e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1085 -4.2447918094694614e-03</internalNodes>
          <leafValues>
            -1.2281739711761475e-01 2.8134100139141083e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1086 7.7825621701776981e-03</internalNodes>
          <leafValues>
            -6.9222308695316315e-02 2.5814509391784668e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1087 -1.2726710177958012e-02</internalNodes>
          <leafValues>
            1.0745859891176224e-01 -7.6575823128223419e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1088 4.7346940264105797e-03</internalNodes>
          <leafValues>
            4.4127859175205231e-02 -3.8045680522918701e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1089 3.4512639977037907e-03</internalNodes>
          <leafValues>
            -4.2947210371494293e-02 4.6074831485748291e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1090 5.6996050989255309e-04</internalNodes>
          <leafValues>
            6.6926121711730957e-02 -2.9685848951339722e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1091 -5.3889099508523941e-02</internalNodes>
          <leafValues>
            -1. 3.9760880172252655e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1092 1.0263220174238086e-03</internalNodes>
          <leafValues>
            -1.1138930171728134e-01 1.7764210700988770e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1093 3.9374440908432007e-02</internalNodes>
          <leafValues>
            1.2977429665625095e-02 -6.3669937849044800e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1094 1.8777979537844658e-02</internalNodes>
          <leafValues>
            -3.9334569126367569e-02 4.5990169048309326e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1095 1.5851920470595360e-03</internalNodes>
          <leafValues>
            -1.0917869955301285e-01 5.6247789412736893e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1096 -1.0857740417122841e-02</internalNodes>
          <leafValues>
            -2.0176340639591217e-01 9.0685456991195679e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1097 4.4399261474609375e-02</internalNodes>
          <leafValues>
            1.9891490228474140e-03 -9.9981158971786499e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1098 -1.7311190022155643e-03</internalNodes>
          <leafValues>
            1.4699029922485352e-01 -1.4069539308547974e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1099 -1.6609770245850086e-03</internalNodes>
          <leafValues>
            1.6190530359745026e-01 -5.5535599589347839e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1100 -4.3332851491868496e-03</internalNodes>
          <leafValues>
            -3.3971568942070007e-01 4.3209198862314224e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1101 -4.4786658691009507e-05</internalNodes>
          <leafValues>
            1.0217490047216415e-01 -1.0289809852838516e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1102 -1.2255939655005932e-02</internalNodes>
          <leafValues>
            4.6331259608268738e-01 -3.8829129189252853e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1103 3.1728390604257584e-02</internalNodes>
          <leafValues>
            -1.0918959975242615e-02 1.9252130389213562e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1104 8.6054168641567230e-03</internalNodes>
          <leafValues>
            5.3962308913469315e-02 -3.3835870027542114e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1105 2.4249579291790724e-03</internalNodes>
          <leafValues>
            -4.3876059353351593e-02 2.4977789819240570e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1106 -1.9957860931754112e-03</internalNodes>
          <leafValues>
            1.1398400366306305e-01 -1.7925310134887695e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1107 4.6042509377002716e-02</internalNodes>
          <leafValues>
            2.0680939778685570e-03 -8.7673932313919067e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1108 2.4898271076381207e-03</internalNodes>
          <leafValues>
            -6.9595612585544586e-02 2.6142540574073792e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1109 1.0052820434793830e-03</internalNodes>
          <leafValues>
            4.5501660555601120e-02 -1.2399580329656601e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1110 9.0297553688287735e-03</internalNodes>
          <leafValues>
            -7.1272410452365875e-02 2.2919359803199768e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1111 1.2028490193188190e-02</internalNodes>
          <leafValues>
            2.0230330526828766e-02 -3.4052988886833191e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1112 2.3313730489462614e-03</internalNodes>
          <leafValues>
            8.7259337306022644e-02 -2.3195190727710724e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1113 9.5184362726286054e-04</internalNodes>
          <leafValues>
            -2.3168809711933136e-01 5.5022191256284714e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1114 9.6378661692142487e-03</internalNodes>
          <leafValues>
            -4.1655559092760086e-02 4.2928260564804077e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1115 1.3566980138421059e-02</internalNodes>
          <leafValues>
            4.5669659972190857e-02 -2.2501240670681000e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1116 3.3653501421213150e-02</internalNodes>
          <leafValues>
            -6.7861579358577728e-02 3.6967611312866211e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1117 -6.0395020991563797e-02</internalNodes>
          <leafValues>
            -9.0887361764907837e-01 3.8193699438124895e-03</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1118 1.3169209705665708e-03</internalNodes>
          <leafValues>
            -1.5941339731216431e-01 1.4766550064086914e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1119 -9.7704064100980759e-03</internalNodes>
          <leafValues>
            -1.2848410010337830e-01 4.7832399606704712e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1120 -4.5100511051714420e-03</internalNodes>
          <leafValues>
            1.2574909627437592e-01 -2.1964469552040100e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1121 -2.0346629898995161e-03</internalNodes>
          <leafValues>
            -1.8574400246143341e-01 4.9177091568708420e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1122 1.3294390402734280e-02</internalNodes>
          <leafValues>
            9.1497242450714111e-02 -2.1343930065631866e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1123 -4.0054250508546829e-02</internalNodes>
          <leafValues>
            3.1770059466362000e-01 -3.1080769374966621e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1124 2.5492990389466286e-02</internalNodes>
          <leafValues>
            3.8877040147781372e-02 -4.5658990740776062e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1125 -3.8089688867330551e-02</internalNodes>
          <leafValues>
            6.6615498065948486e-01 -1.9895339384675026e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1126 -2.1308319270610809e-01</internalNodes>
          <leafValues>
            -8.6534178256988525e-01 2.0898429676890373e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1127 -8.9727543294429779e-02</internalNodes>
          <leafValues>
            2.5725919008255005e-01 -4.6261668205261230e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1128 2.5075700134038925e-02</internalNodes>
          <leafValues>
            4.1259508579969406e-02 -3.7666648626327515e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1129 2.3366149514913559e-02</internalNodes>
          <leafValues>
            -7.2202831506729126e-02 2.4737030267715454e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1130 2.8038409072905779e-04</internalNodes>
          <leafValues>
            -7.9473547637462616e-02 2.2478230297565460e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1131 8.2364194095134735e-03</internalNodes>
          <leafValues>
            5.1211010664701462e-02 -1.3328659534454346e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1132 5.3922779858112335e-02</internalNodes>
          <leafValues>
            1.7108399420976639e-02 -8.9256042242050171e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1133 2.7015779633074999e-03</internalNodes>
          <leafValues>
            -1.8405599892139435e-01 1.2830390036106110e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1134 -1.6505690291523933e-02</internalNodes>
          <leafValues>
            6.2239181995391846e-01 -2.6413690298795700e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1135 -1.8418730469420552e-03</internalNodes>
          <leafValues>
            -1.2646800279617310e-01 4.8690851777791977e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1136 5.1953629590570927e-03</internalNodes>
          <leafValues>
            4.5653700828552246e-02 -3.2519981265068054e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1137 5.0785308703780174e-03</internalNodes>
          <leafValues>
            4.0703259408473969e-02 -2.0620769262313843e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1138 5.0687040202319622e-03</internalNodes>
          <leafValues>
            -7.6456248760223389e-02 2.5867408514022827e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1139 -1.1892319656908512e-02</internalNodes>
          <leafValues>
            -2.2366219758987427e-01 3.0855409801006317e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1140 2.4257500190287828e-03</internalNodes>
          <leafValues>
            -7.1597889065742493e-02 2.6108819246292114e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1141 -1.1990379542112350e-02</internalNodes>
          <leafValues>
            2.2678479552268982e-01 -1.0305509716272354e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1142 -2.2772200405597687e-02</internalNodes>
          <leafValues>
            -2.3770140111446381e-01 7.6630853116512299e-02</leafValues></_></weakClassifiers></_>
    <_>
      <maxWeakCount>78</maxWeakCount>
      <stageThreshold>-3.0664699554443359e+01</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 1143 3.3625920768827200e-03</internalNodes>
          <leafValues>
            -1.8268440663814545e-01 1.5935519337654114e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1144 4.4937757775187492e-03</internalNodes>
          <leafValues>
            -8.9438192546367645e-02 2.8422310948371887e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1145 -8.8971032528206706e-04</internalNodes>
          <leafValues>
            9.5665588974952698e-02 -1.9407069683074951e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1146 2.6789100375026464e-03</internalNodes>
          <leafValues>
            -1.0152669996023178e-01 1.7864160239696503e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1147 -4.0554129518568516e-03</internalNodes>
          <leafValues>
            -2.3337660729885101e-01 1.2279739975929260e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1148 -1.7742250114679337e-02</internalNodes>
          <leafValues>
            1.9190870225429535e-01 -3.1710729002952576e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1149 3.0996970599517226e-04</internalNodes>
          <leafValues>
            -1.9344709813594818e-01 9.9541679024696350e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1150 -3.7737619131803513e-03</internalNodes>
          <leafValues>
            -2.0298850536346436e-01 7.9316012561321259e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1151 1.4448439469560981e-03</internalNodes>
          <leafValues>
            -5.9811491519212723e-02 4.1375398635864258e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1152 4.1589159518480301e-03</internalNodes>
          <leafValues>
            -9.2934109270572662e-02 7.7575348317623138e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1153 9.7764004021883011e-03</internalNodes>
          <leafValues>
            5.3027391433715820e-02 -3.6435180902481079e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1154 -2.8739850968122482e-03</internalNodes>
          <leafValues>
            1.2728120386600494e-01 -3.2182350754737854e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1155 4.3552028946578503e-03</internalNodes>
          <leafValues>
            -1.4472070336341858e-01 1.4171679317951202e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1156 -1.2132039666175842e-01</internalNodes>
          <leafValues>
            1.5284240245819092e-01 -2.6948520913720131e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1157 7.5531532056629658e-03</internalNodes>
          <leafValues>
            1.0153439640998840e-01 -1.8715800344944000e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1158 4.8978552222251892e-03</internalNodes>
          <leafValues>
            2.8034990653395653e-02 -1.4224380254745483e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1159 -1.8711129669100046e-03</internalNodes>
          <leafValues>
            1.5129889547824860e-01 -1.3912929594516754e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1160 4.1867699474096298e-02</internalNodes>
          <leafValues>
            1.8230549991130829e-02 -5.6771957874298096e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1161 -8.4031058941036463e-04</internalNodes>
          <leafValues>
            1.5392039716243744e-01 -1.2112110108137131e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1162 3.6289851414039731e-04</internalNodes>
          <leafValues>
            -7.9913586378097534e-02 7.0097483694553375e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1163 -4.4498889474198222e-04</internalNodes>
          <leafValues>
            1.6784679889678955e-01 -1.3805930316448212e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1164 2.2194290068000555e-03</internalNodes>
          <leafValues>
            5.8453138917684555e-02 -1.2374790012836456e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1165 -2.5759059935808182e-03</internalNodes>
          <leafValues>
            2.2619499266147614e-01 -8.6251437664031982e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1166 5.8989811688661575e-02</internalNodes>
          <leafValues>
            6.9204131141304970e-03 -7.3367577791213989e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1167 -2.7889141440391541e-01</internalNodes>
          <leafValues>
            4.6728101372718811e-01 -3.8612861186265945e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1168 -5.3824000060558319e-03</internalNodes>
          <leafValues>
            -1.6939850151538849e-01 6.1394538730382919e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1169 -8.9165568351745605e-04</internalNodes>
          <leafValues>
            -2.4867910146713257e-01 7.6590277254581451e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1170 1.2071889825165272e-02</internalNodes>
          <leafValues>
            8.9360373094677925e-03 -2.7028709650039673e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1171 3.8453561137430370e-04</internalNodes>
          <leafValues>
            9.9488303065299988e-02 -2.1522629261016846e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1172 -2.2118990309536457e-03</internalNodes>
          <leafValues>
            4.0786389261484146e-02 -1.1563809961080551e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1173 2.0960820838809013e-02</internalNodes>
          <leafValues>
            -3.1355928629636765e-02 7.1006178855895996e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1174 -3.9021030534058809e-03</internalNodes>
          <leafValues>
            -1.7460019886493683e-01 4.0775351226329803e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1175 -4.5169141230871901e-05</internalNodes>
          <leafValues>
            1.2105180323123932e-01 -1.6618220508098602e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1176 6.9195672869682312e-02</internalNodes>
          <leafValues>
            7.6447450555860996e-03 -5.9211570024490356e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1177 -1.1615910334512591e-03</internalNodes>
          <leafValues>
            2.2584970295429230e-01 -9.1772772371768951e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1178 4.5347518607741222e-05</internalNodes>
          <leafValues>
            -2.0863719284534454e-01 9.0364061295986176e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1179 -1.9045149907469749e-02</internalNodes>
          <leafValues>
            4.2344009876251221e-01 -4.6018179506063461e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1180 4.1966438293457031e-03</internalNodes>
          <leafValues>
            -2.8369670733809471e-02 3.0800709128379822e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1181 2.5357000413350761e-04</internalNodes>
          <leafValues>
            -2.8971961140632629e-01 7.5374223291873932e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1182 1.0817909985780716e-01</internalNodes>
          <leafValues>
            -1.4286429621279240e-02 7.2823339700698853e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1183 -5.5140778422355652e-03</internalNodes>
          <leafValues>
            -1.8854649364948273e-01 1.1378549784421921e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1184 5.5264509283006191e-03</internalNodes>
          <leafValues>
            7.0834018290042877e-02 -1.8397599458694458e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1185 6.4198831096291542e-03</internalNodes>
          <leafValues>
            -1.1449480056762695e-01 1.9120390713214874e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1186 1.9314220547676086e-01</internalNodes>
          <leafValues>
            1.4066229574382305e-02 -6.9772118330001831e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1187 4.0670208632946014e-02</internalNodes>
          <leafValues>
            -2.4279089644551277e-02 7.8828179836273193e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1188 -2.1965131163597107e-03</internalNodes>
          <leafValues>
            -2.0105579495429993e-01 5.1050510257482529e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1189 -4.7381771728396416e-03</internalNodes>
          <leafValues>
            2.5222310423851013e-01 -7.3429226875305176e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1190 7.1773640811443329e-02</internalNodes>
          <leafValues>
            -9.0609909966588020e-03 9.2946898937225342e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1191 6.9466611603274941e-04</internalNodes>
          <leafValues>
            1.0625690221786499e-01 -1.9162459671497345e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1192 2.6388010010123253e-03</internalNodes>
          <leafValues>
            6.3330717384815216e-02 -2.0404089987277985e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1193 -3.1406691414304078e-04</internalNodes>
          <leafValues>
            1.7990510165691376e-01 -9.8495960235595703e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1194 -5.8691151207312942e-04</internalNodes>
          <leafValues>
            8.5071258246898651e-02 -7.6974540948867798e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1195 1.0376359568908811e-03</internalNodes>
          <leafValues>
            -1.1096309870481491e-01 1.5985070168972015e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1196 1.6373570542782545e-03</internalNodes>
          <leafValues>
            1.1128730326890945e-01 -1.2352730333805084e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1197 -7.3773309122771025e-04</internalNodes>
          <leafValues>
            1.2890860438346863e-01 -1.4294579625129700e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1198 -1.6841450706124306e-02</internalNodes>
          <leafValues>
            -2.4231070280075073e-01 2.0597470924258232e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1199 -3.0590690672397614e-02</internalNodes>
          <leafValues>
            3.3513951301574707e-01 -4.7183569520711899e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1200 1.0214540176093578e-02</internalNodes>
          <leafValues>
            5.5497199296951294e-02 -2.3405939340591431e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1201 -1.1853770120069385e-03</internalNodes>
          <leafValues>
            9.2074163258075714e-02 -1.7347140610218048e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1202 1.1729650432243943e-03</internalNodes>
          <leafValues>
            -8.4075942635536194e-02 2.0689530670642853e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1203 1.0894170030951500e-02</internalNodes>
          <leafValues>
            5.6475941091775894e-02 -3.1677180528640747e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1204 -2.0437049679458141e-03</internalNodes>
          <leafValues>
            1.8796369433403015e-01 -9.8889023065567017e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1205 -5.7676038704812527e-03</internalNodes>
          <leafValues>
            -2.5189259648323059e-01 7.5108267366886139e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1206 6.9624483585357666e-02</internalNodes>
          <leafValues>
            -1.7661379650235176e-02 4.3390399217605591e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1207 -3.1853429391048849e-04</internalNodes>
          <leafValues>
            -2.9378080368041992e-01 5.8162420988082886e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1208 1.7543470021337271e-03</internalNodes>
          <leafValues>
            2.6858489960432053e-02 -1.5225639939308167e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1209 1.2951970566064119e-03</internalNodes>
          <leafValues>
            -7.1769118309020996e-02 3.8101229071617126e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1210 2.0549140870571136e-02</internalNodes>
          <leafValues>
            -2.3171430453658104e-02 2.7228319644927979e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1211 2.7475480455905199e-03</internalNodes>
          <leafValues>
            6.7207306623458862e-02 -2.7162951231002808e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1212 5.2633951418101788e-03</internalNodes>
          <leafValues>
            -1.3931609690189362e-01 1.1821229755878448e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1213 -5.2199261263012886e-03</internalNodes>
          <leafValues>
            -3.3213511109352112e-01 4.7329191118478775e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1214 9.9096707999706268e-03</internalNodes>
          <leafValues>
            -6.9706782698631287e-02 1.9954280555248260e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1215 -1.0334379971027374e-01</internalNodes>
          <leafValues>
            4.2418560385704041e-01 -3.9896268397569656e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1216 -1.3322319835424423e-02</internalNodes>
          <leafValues>
            -2.5508868694305420e-01 4.1351031512022018e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1217 1.7832260346040130e-03</internalNodes>
          <leafValues>
            -1.7664439976215363e-01 1.0336239635944366e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1218 6.3282333314418793e-02</internalNodes>
          <leafValues>
            1.2395679950714111e-02 -4.6355250477790833e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1219 -5.1022358238697052e-03</internalNodes>
          <leafValues>
            4.0670639276504517e-01 -5.0193451344966888e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1220 3.9891529828310013e-02</internalNodes>
          <leafValues>
            3.7219129502773285e-02 -5.5696451663970947e-01</leafValues></_></weakClassifiers></_></stages>
  <features>
    <_>
      <rects>
        <_>
          3 4 12 16 -1.</_>
        <_>
          7 4 4 16 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 2 20 -1.</_>
        <_>
          11 10 2 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 1 4 22 -1.</_>
        <_>
          4 12 4 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 8 7 12 -1.</_>
        <_>
          9 14 7 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 6 10 -1.</_>
        <_>
          6 0 3 5 2.</_>
        <_>
          9 5 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 18 18 5 -1.</_>
        <_>
          1 18 9 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 20 10 3 -1.</_>
        <_>
          9 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 10 6 -1.</_>
        <_>
          6 20 10 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 4 20 -1.</_>
        <_>
          0 10 4 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 16 14 -1.</_>
        <_>
          3 7 16 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 1 4 13 -1.</_>
        <_>
          7 1 2 13 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 8 18 12 -1.</_>
        <_>
          10 8 9 6 2.</_>
        <_>
          1 14 9 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 15 21 -1.</_>
        <_>
          7 0 5 21 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 5 18 18 -1.</_>
        <_>
          10 5 9 9 2.</_>
        <_>
          1 14 9 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 19 15 3 -1.</_>
        <_>
          7 19 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 20 12 3 -1.</_>
        <_>
          7 20 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 21 14 2 -1.</_>
        <_>
          8 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 18 6 -1.</_>
        <_>
          6 16 6 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 3 4 20 -1.</_>
        <_>
          8 13 4 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 19 18 3 -1.</_>
        <_>
          9 19 9 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 21 14 2 -1.</_>
        <_>
          5 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 9 5 -1.</_>
        <_>
          5 0 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 20 15 3 -1.</_>
        <_>
          8 20 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 9 6 14 -1.</_>
        <_>
          5 9 2 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 3 3 18 -1.</_>
        <_>
          12 12 3 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 14 4 9 -1.</_>
        <_>
          3 14 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 15 11 8 -1.</_>
        <_>
          7 17 11 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 7 6 10 -1.</_>
        <_>
          0 7 3 5 2.</_>
        <_>
          3 12 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 6 4 13 -1.</_>
        <_>
          10 6 2 13 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 6 4 13 -1.</_>
        <_>
          7 6 2 13 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 2 6 8 -1.</_>
        <_>
          8 2 6 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 11 19 12 -1.</_>
        <_>
          0 17 19 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 6 5 -1.</_>
        <_>
          3 18 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 17 9 6 -1.</_>
        <_>
          12 17 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 20 15 3 -1.</_>
        <_>
          5 20 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 19 8 4 -1.</_>
        <_>
          9 19 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 9 6 -1.</_>
        <_>
          3 17 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 17 5 6 -1.</_>
        <_>
          14 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 2 15 14 -1.</_>
        <_>
          7 2 5 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 17 5 6 -1.</_>
        <_>
          14 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 5 6 -1.</_>
        <_>
          0 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 13 8 -1.</_>
        <_>
          3 4 13 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 21 14 2 -1.</_>
        <_>
          7 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 4 4 15 -1.</_>
        <_>
          9 4 2 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 18 8 5 -1.</_>
        <_>
          5 18 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 4 4 15 -1.</_>
        <_>
          9 4 2 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 4 4 15 -1.</_>
        <_>
          8 4 2 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 11 8 8 -1.</_>
        <_>
          15 11 4 4 2.</_>
        <_>
          11 15 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 13 6 7 -1.</_>
        <_>
          6 13 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 1 8 13 -1.</_>
        <_>
          7 1 4 13 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 21 14 2 -1.</_>
        <_>
          5 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 21 18 2 -1.</_>
        <_>
          9 21 9 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 18 8 5 -1.</_>
        <_>
          7 18 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 17 8 6 -1.</_>
        <_>
          8 17 4 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 2 7 10 -1.</_>
        <_>
          10 2 7 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 9 2 14 -1.</_>
        <_>
          3 9 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 7 2 16 -1.</_>
        <_>
          15 7 1 16 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 8 4 15 -1.</_>
        <_>
          3 8 2 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 0 3 14 -1.</_>
        <_>
          14 0 3 7 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 6 8 9 -1.</_>
        <_>
          9 6 4 9 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 15 11 8 -1.</_>
        <_>
          8 17 11 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 4 10 -1.</_>
        <_>
          7 7 2 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 15 9 8 -1.</_>
        <_>
          10 17 9 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 9 8 -1.</_>
        <_>
          0 17 9 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 1 17 18 -1.</_>
        <_>
          2 10 17 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 16 2 -1.</_>
        <_>
          2 0 8 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 0 9 5 -1.</_>
        <_>
          11 0 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 6 10 -1.</_>
        <_>
          6 0 3 5 2.</_>
        <_>
          9 5 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 6 4 7 -1.</_>
        <_>
          10 6 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 4 15 11 -1.</_>
        <_>
          7 4 5 11 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 15 4 8 -1.</_>
        <_>
          15 15 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 4 8 -1.</_>
        <_>
          2 15 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 6 4 11 -1.</_>
        <_>
          7 6 2 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 17 16 4 -1.</_>
        <_>
          7 17 8 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 3 10 8 -1.</_>
        <_>
          9 3 5 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 6 7 10 -1.</_>
        <_>
          12 6 7 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 0 6 5 -1.</_>
        <_>
          5 0 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 18 14 3 -1.</_>
        <_>
          4 19 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 20 14 3 -1.</_>
        <_>
          9 20 7 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 21 14 2 -1.</_>
        <_>
          4 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 8 3 14 -1.</_>
        <_>
          9 8 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 9 3 14 -1.</_>
        <_>
          9 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 9 16 -1.</_>
        <_>
          5 11 9 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 13 6 8 -1.</_>
        <_>
          11 17 6 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 17 7 6 -1.</_>
        <_>
          4 19 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 13 16 8 -1.</_>
        <_>
          10 13 8 4 2.</_>
        <_>
          2 17 8 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 18 15 3 -1.</_>
        <_>
          2 19 15 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 13 15 3 -1.</_>
        <_>
          7 13 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 11 16 -1.</_>
        <_>
          8 4 11 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 19 18 -1.</_>
        <_>
          0 6 19 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 11 16 -1.</_>
        <_>
          8 4 11 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 4 20 -1.</_>
        <_>
          0 6 4 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 6 15 4 -1.</_>
        <_>
          8 6 5 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 18 6 -1.</_>
        <_>
          0 9 9 3 2.</_>
        <_>
          9 12 9 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 5 3 14 -1.</_>
        <_>
          9 5 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 6 8 -1.</_>
        <_>
          3 0 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 6 18 6 -1.</_>
        <_>
          10 6 9 3 2.</_>
        <_>
          1 9 9 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 4 15 -1.</_>
        <_>
          8 7 2 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 5 8 10 -1.</_>
        <_>
          11 10 8 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 5 8 10 -1.</_>
        <_>
          0 10 8 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 20 15 3 -1.</_>
        <_>
          8 20 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 16 9 5 -1.</_>
        <_>
          5 16 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 6 11 -1.</_>
        <_>
          13 11 3 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 4 11 -1.</_>
        <_>
          7 8 2 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 12 5 -1.</_>
        <_>
          8 7 6 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 11 15 3 -1.</_>
        <_>
          7 11 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 1 18 3 -1.</_>
        <_>
          7 1 6 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 1 14 4 -1.</_>
        <_>
          5 1 7 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 9 18 10 -1.</_>
        <_>
          10 9 9 5 2.</_>
        <_>
          1 14 9 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 9 3 14 -1.</_>
        <_>
          8 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 7 4 14 -1.</_>
        <_>
          9 7 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 19 16 -1.</_>
        <_>
          0 9 19 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 7 3 14 -1.</_>
        <_>
          10 7 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 11 14 6 -1.</_>
        <_>
          2 11 7 3 2.</_>
        <_>
          9 14 7 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 7 3 14 -1.</_>
        <_>
          10 7 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 3 14 -1.</_>
        <_>
          8 7 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 17 5 6 -1.</_>
        <_>
          7 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 6 9 15 -1.</_>
        <_>
          5 11 3 5 9.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 6 10 -1.</_>
        <_>
          11 0 3 5 2.</_>
        <_>
          8 5 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 2 6 21 -1.</_>
        <_>
          5 9 2 7 9.</_></rects></_>
    <_>
      <rects>
        <_>
          9 19 10 4 -1.</_>
        <_>
          9 19 5 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 8 4 8 -1.</_>
        <_>
          4 8 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 1 2 22 -1.</_>
        <_>
          11 12 2 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 20 15 3 -1.</_>
        <_>
          5 20 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 19 8 4 -1.</_>
        <_>
          10 19 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 19 8 4 -1.</_>
        <_>
          5 19 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 6 7 -1.</_>
        <_>
          11 0 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 6 7 -1.</_>
        <_>
          6 0 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 2 3 10 -1.</_>
        <_>
          13 2 3 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 4 6 9 -1.</_>
        <_>
          9 4 3 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 7 2 10 -1.</_>
        <_>
          10 7 1 10 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 1 15 9 -1.</_>
        <_>
          7 1 5 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 5 6 7 -1.</_>
        <_>
          10 5 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 5 6 7 -1.</_>
        <_>
          7 5 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 7 2 10 -1.</_>
        <_>
          10 7 1 10 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 7 10 2 -1.</_>
        <_>
          9 7 10 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 16 4 7 -1.</_>
        <_>
          13 16 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 4 10 -1.</_>
        <_>
          8 9 2 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 18 14 4 -1.</_>
        <_>
          12 18 7 2 2.</_>
        <_>
          5 20 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 1 12 3 -1.</_>
        <_>
          5 1 6 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 0 2 22 -1.</_>
        <_>
          11 11 2 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 15 4 8 -1.</_>
        <_>
          5 15 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 2 14 -1.</_>
        <_>
          11 0 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 2 14 -1.</_>
        <_>
          7 0 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 2 20 -1.</_>
        <_>
          11 0 1 20 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 19 16 4 -1.</_>
        <_>
          5 19 8 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 2 20 -1.</_>
        <_>
          11 0 1 20 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 2 20 -1.</_>
        <_>
          7 0 1 20 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 2 22 -1.</_>
        <_>
          11 11 2 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 14 4 -1.</_>
        <_>
          0 18 7 2 2.</_>
        <_>
          7 20 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 1 18 8 -1.</_>
        <_>
          10 1 9 4 2.</_>
        <_>
          1 5 9 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 8 10 4 -1.</_>
        <_>
          9 8 10 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 7 15 3 -1.</_>
        <_>
          8 7 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 6 8 -1.</_>
        <_>
          8 1 6 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 3 3 15 -1.</_>
        <_>
          9 3 1 15 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 14 9 6 -1.</_>
        <_>
          4 14 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 20 15 3 -1.</_>
        <_>
          8 20 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 14 3 -1.</_>
        <_>
          0 19 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 20 10 3 -1.</_>
        <_>
          5 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 5 10 6 -1.</_>
        <_>
          9 5 5 6 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 4 15 14 -1.</_>
        <_>
          7 4 5 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 6 7 -1.</_>
        <_>
          3 16 3 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 18 12 5 -1.</_>
        <_>
          11 18 4 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 18 15 3 -1.</_>
        <_>
          1 19 15 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 19 12 4 -1.</_>
        <_>
          8 19 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 3 12 -1.</_>
        <_>
          5 6 3 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 20 16 3 -1.</_>
        <_>
          3 20 8 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 15 8 -1.</_>
        <_>
          0 17 15 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 14 4 7 -1.</_>
        <_>
          12 14 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 15 3 -1.</_>
        <_>
          6 7 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 8 4 -1.</_>
        <_>
          10 0 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 18 4 -1.</_>
        <_>
          6 0 6 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 20 10 3 -1.</_>
        <_>
          9 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 4 15 16 -1.</_>
        <_>
          7 4 5 16 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 11 12 -1.</_>
        <_>
          4 6 11 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 9 3 14 -1.</_>
        <_>
          8 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 21 14 2 -1.</_>
        <_>
          4 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 21 16 2 -1.</_>
        <_>
          8 21 8 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 7 4 14 -1.</_>
        <_>
          9 7 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 16 12 -1.</_>
        <_>
          5 0 8 12 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 17 16 5 -1.</_>
        <_>
          7 17 8 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 13 6 5 -1.</_>
        <_>
          3 13 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 12 6 6 -1.</_>
        <_>
          13 12 3 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 6 6 -1.</_>
        <_>
          3 12 3 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 7 4 14 -1.</_>
        <_>
          9 7 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 3 4 20 -1.</_>
        <_>
          7 13 4 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 6 4 15 -1.</_>
        <_>
          9 6 2 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 6 4 15 -1.</_>
        <_>
          8 6 2 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 6 12 -1.</_>
        <_>
          16 11 3 6 2.</_>
        <_>
          13 17 3 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 11 6 12 -1.</_>
        <_>
          0 11 3 6 2.</_>
        <_>
          3 17 3 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 2 2 14 -1.</_>
        <_>
          11 2 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 2 2 14 -1.</_>
        <_>
          7 2 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 5 3 14 -1.</_>
        <_>
          12 5 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 4 15 10 -1.</_>
        <_>
          7 4 5 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 11 22 -1.</_>
        <_>
          4 11 11 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 19 14 4 -1.</_>
        <_>
          0 19 7 2 2.</_>
        <_>
          7 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 4 7 -1.</_>
        <_>
          8 0 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 4 15 -1.</_>
        <_>
          8 0 2 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 21 14 2 -1.</_>
        <_>
          5 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 9 3 14 -1.</_>
        <_>
          8 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 9 2 14 -1.</_>
        <_>
          12 9 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 9 2 14 -1.</_>
        <_>
          6 9 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 20 15 3 -1.</_>
        <_>
          8 20 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 3 17 -1.</_>
        <_>
          6 0 1 17 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 20 12 3 -1.</_>
        <_>
          4 20 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 2 3 14 -1.</_>
        <_>
          6 2 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 3 15 18 -1.</_>
        <_>
          7 3 5 18 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 4 7 -1.</_>
        <_>
          9 1 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 9 5 -1.</_>
        <_>
          11 0 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 4 7 -1.</_>
        <_>
          9 0 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 3 12 19 -1.</_>
        <_>
          8 3 6 19 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 3 12 19 -1.</_>
        <_>
          5 3 6 19 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 8 2 14 -1.</_>
        <_>
          13 8 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 16 12 6 -1.</_>
        <_>
          1 18 12 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 8 2 14 -1.</_>
        <_>
          13 8 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 8 2 14 -1.</_>
        <_>
          5 8 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 10 4 -1.</_>
        <_>
          9 0 5 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 1 7 22 -1.</_>
        <_>
          6 12 7 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 17 10 6 -1.</_>
        <_>
          12 17 5 3 2.</_>
        <_>
          7 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 6 6 5 -1.</_>
        <_>
          9 6 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 20 15 3 -1.</_>
        <_>
          8 20 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 15 8 -1.</_>
        <_>
          1 4 15 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 16 6 -1.</_>
        <_>
          6 0 8 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 20 10 3 -1.</_>
        <_>
          7 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 19 10 3 -1.</_>
        <_>
          9 19 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 18 6 5 -1.</_>
        <_>
          6 18 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 6 9 -1.</_>
        <_>
          11 0 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 6 9 -1.</_>
        <_>
          6 0 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 9 4 14 -1.</_>
        <_>
          12 9 2 7 2.</_>
        <_>
          10 16 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 11 4 7 -1.</_>
        <_>
          4 11 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 13 4 9 -1.</_>
        <_>
          12 13 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 13 4 9 -1.</_>
        <_>
          5 13 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 13 10 6 -1.</_>
        <_>
          14 13 5 3 2.</_>
        <_>
          9 16 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 10 15 10 -1.</_>
        <_>
          7 10 5 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 9 4 14 -1.</_>
        <_>
          12 9 2 7 2.</_>
        <_>
          10 16 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 9 4 14 -1.</_>
        <_>
          5 9 2 7 2.</_>
        <_>
          7 16 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 16 4 7 -1.</_>
        <_>
          12 16 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 16 4 7 -1.</_>
        <_>
          5 16 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 17 7 6 -1.</_>
        <_>
          8 19 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 20 15 3 -1.</_>
        <_>
          5 20 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 15 6 8 -1.</_>
        <_>
          9 19 6 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 10 10 -1.</_>
        <_>
          0 0 5 5 2.</_>
        <_>
          5 5 5 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 10 3 -1.</_>
        <_>
          9 0 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 10 3 -1.</_>
        <_>
          5 0 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 4 4 10 -1.</_>
        <_>
          10 4 2 10 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 4 10 4 -1.</_>
        <_>
          9 4 10 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 4 12 12 -1.</_>
        <_>
          10 8 4 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          1 4 12 12 -1.</_>
        <_>
          5 8 4 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          5 6 9 8 -1.</_>
        <_>
          5 8 9 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 1 15 21 -1.</_>
        <_>
          7 8 5 7 9.</_></rects></_>
    <_>
      <rects>
        <_>
          1 16 9 7 -1.</_>
        <_>
          4 16 3 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 5 12 18 -1.</_>
        <_>
          10 5 6 9 2.</_>
        <_>
          4 14 6 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 20 15 3 -1.</_>
        <_>
          6 20 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 4 16 13 -1.</_>
        <_>
          7 4 8 13 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 3 10 8 -1.</_>
        <_>
          9 3 5 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 19 8 4 -1.</_>
        <_>
          11 19 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 19 8 4 -1.</_>
        <_>
          4 19 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 9 5 -1.</_>
        <_>
          11 0 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 6 22 -1.</_>
        <_>
          6 0 3 11 2.</_>
        <_>
          9 11 3 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 7 3 14 -1.</_>
        <_>
          9 7 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 2 14 -1.</_>
        <_>
          6 8 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 3 10 -1.</_>
        <_>
          13 16 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 16 5 -1.</_>
        <_>
          5 0 8 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 10 7 -1.</_>
        <_>
          9 0 5 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 18 23 -1.</_>
        <_>
          9 0 9 23 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 12 15 -1.</_>
        <_>
          9 13 4 5 9.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 6 7 -1.</_>
        <_>
          5 0 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 12 15 -1.</_>
        <_>
          9 13 4 5 9.</_></rects></_>
    <_>
      <rects>
        <_>
          5 2 4 13 -1.</_>
        <_>
          7 2 2 13 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 11 14 2 -1.</_>
        <_>
          3 11 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 12 15 7 -1.</_>
        <_>
          7 12 5 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 12 15 -1.</_>
        <_>
          9 13 4 5 9.</_></rects></_>
    <_>
      <rects>
        <_>
          0 14 15 9 -1.</_>
        <_>
          5 14 5 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 15 12 8 -1.</_>
        <_>
          9 15 6 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 15 12 8 -1.</_>
        <_>
          4 15 6 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 6 3 14 -1.</_>
        <_>
          9 6 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 5 4 14 -1.</_>
        <_>
          5 5 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 5 3 14 -1.</_>
        <_>
          12 5 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 10 6 9 -1.</_>
        <_>
          3 10 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 8 16 10 -1.</_>
        <_>
          6 8 8 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 6 6 -1.</_>
        <_>
          6 20 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 10 18 10 -1.</_>
        <_>
          10 10 9 5 2.</_>
        <_>
          1 15 9 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 7 4 -1.</_>
        <_>
          6 2 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 6 19 3 -1.</_>
        <_>
          0 7 19 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 11 6 6 -1.</_>
        <_>
          9 11 3 6 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 0 9 5 -1.</_>
        <_>
          10 0 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 9 4 -1.</_>
        <_>
          0 5 9 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 18 17 2 -1.</_>
        <_>
          1 19 17 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 3 4 8 -1.</_>
        <_>
          9 3 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 9 2 14 -1.</_>
        <_>
          9 9 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 8 3 14 -1.</_>
        <_>
          9 8 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 1 9 4 -1.</_>
        <_>
          10 3 9 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 10 3 -1.</_>
        <_>
          5 12 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 6 4 12 -1.</_>
        <_>
          8 12 4 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 12 4 7 -1.</_>
        <_>
          5 12 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 12 6 -1.</_>
        <_>
          12 17 6 3 2.</_>
        <_>
          6 20 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 18 6 -1.</_>
        <_>
          9 16 9 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 0 4 14 -1.</_>
        <_>
          14 0 2 7 2.</_>
        <_>
          12 7 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 21 14 2 -1.</_>
        <_>
          8 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 19 8 4 -1.</_>
        <_>
          9 19 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 12 4 -1.</_>
        <_>
          5 0 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 1 8 5 -1.</_>
        <_>
          10 1 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 13 6 10 -1.</_>
        <_>
          2 13 2 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 9 3 14 -1.</_>
        <_>
          9 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 7 10 2 -1.</_>
        <_>
          9 7 10 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 16 15 3 -1.</_>
        <_>
          7 16 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 1 8 17 -1.</_>
        <_>
          9 1 4 17 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 19 8 4 -1.</_>
        <_>
          9 19 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 19 8 4 -1.</_>
        <_>
          6 19 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 8 7 -1.</_>
        <_>
          10 0 4 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 8 7 -1.</_>
        <_>
          5 0 4 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 16 7 4 -1.</_>
        <_>
          12 18 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 4 14 -1.</_>
        <_>
          9 0 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 18 15 3 -1.</_>
        <_>
          2 19 15 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 4 7 -1.</_>
        <_>
          9 1 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 5 3 15 -1.</_>
        <_>
          12 5 1 15 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 10 6 10 -1.</_>
        <_>
          0 10 3 5 2.</_>
        <_>
          3 15 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 5 3 15 -1.</_>
        <_>
          12 5 1 15 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 5 3 15 -1.</_>
        <_>
          6 5 1 15 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 5 12 12 -1.</_>
        <_>
          6 5 6 12 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 4 12 16 -1.</_>
        <_>
          7 4 6 16 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 4 6 7 -1.</_>
        <_>
          13 4 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 4 16 -1.</_>
        <_>
          1 7 2 8 2.</_>
        <_>
          3 15 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 1 2 22 -1.</_>
        <_>
          11 12 2 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 18 14 3 -1.</_>
        <_>
          1 19 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 18 12 5 -1.</_>
        <_>
          11 18 4 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 16 19 -1.</_>
        <_>
          5 0 8 19 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 12 6 -1.</_>
        <_>
          9 17 6 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 11 8 4 -1.</_>
        <_>
          7 11 4 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 9 3 14 -1.</_>
        <_>
          11 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 11 15 8 -1.</_>
        <_>
          7 11 5 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 6 7 8 -1.</_>
        <_>
          11 6 7 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 6 8 7 -1.</_>
        <_>
          8 6 4 7 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 9 3 14 -1.</_>
        <_>
          11 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 3 14 -1.</_>
        <_>
          7 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 6 12 -1.</_>
        <_>
          7 0 3 12 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 2 3 16 -1.</_>
        <_>
          6 2 1 16 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 4 15 7 -1.</_>
        <_>
          6 4 5 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 13 4 8 -1.</_>
        <_>
          12 17 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 11 12 12 -1.</_>
        <_>
          6 15 4 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          12 15 5 6 -1.</_>
        <_>
          12 18 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 19 16 -1.</_>
        <_>
          0 8 19 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 20 15 3 -1.</_>
        <_>
          9 20 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 4 8 -1.</_>
        <_>
          9 0 4 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 15 12 6 -1.</_>
        <_>
          11 15 6 3 2.</_>
        <_>
          5 18 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 15 12 6 -1.</_>
        <_>
          2 15 6 3 2.</_>
        <_>
          8 18 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 9 5 -1.</_>
        <_>
          11 0 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 19 14 4 -1.</_>
        <_>
          0 19 7 2 2.</_>
        <_>
          7 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 14 18 7 -1.</_>
        <_>
          1 14 9 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 1 8 8 -1.</_>
        <_>
          5 1 4 4 2.</_>
        <_>
          9 5 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 6 6 12 -1.</_>
        <_>
          9 6 3 12 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 14 4 -1.</_>
        <_>
          9 0 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 20 15 3 -1.</_>
        <_>
          9 20 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 20 15 3 -1.</_>
        <_>
          5 20 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 6 16 9 -1.</_>
        <_>
          6 6 8 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 6 6 12 -1.</_>
        <_>
          7 6 3 12 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 17 9 6 -1.</_>
        <_>
          12 17 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 4 9 -1.</_>
        <_>
          6 7 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 6 2 16 -1.</_>
        <_>
          13 6 1 16 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 5 12 9 -1.</_>
        <_>
          7 5 6 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 6 2 16 -1.</_>
        <_>
          13 6 1 16 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 6 2 16 -1.</_>
        <_>
          5 6 1 16 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 0 3 15 -1.</_>
        <_>
          13 0 1 15 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 3 15 -1.</_>
        <_>
          5 0 1 15 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 2 8 8 -1.</_>
        <_>
          8 2 4 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 6 5 -1.</_>
        <_>
          9 0 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 11 16 -1.</_>
        <_>
          4 11 11 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 8 5 8 -1.</_>
        <_>
          7 12 5 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 18 14 3 -1.</_>
        <_>
          4 19 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 18 17 3 -1.</_>
        <_>
          1 19 17 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 20 10 3 -1.</_>
        <_>
          9 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 21 14 2 -1.</_>
        <_>
          8 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 18 14 3 -1.</_>
        <_>
          4 19 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 16 5 6 -1.</_>
        <_>
          2 19 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 5 12 -1.</_>
        <_>
          13 15 5 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 9 16 3 -1.</_>
        <_>
          1 10 16 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 6 5 9 -1.</_>
        <_>
          7 9 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 7 14 -1.</_>
        <_>
          6 7 7 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 16 6 7 -1.</_>
        <_>
          13 16 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 4 3 15 -1.</_>
        <_>
          2 4 1 15 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 8 8 -1.</_>
        <_>
          14 0 4 4 2.</_>
        <_>
          10 4 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 9 3 14 -1.</_>
        <_>
          2 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 13 5 9 -1.</_>
        <_>
          13 16 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 13 5 9 -1.</_>
        <_>
          1 16 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 14 7 6 -1.</_>
        <_>
          12 16 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 14 9 6 -1.</_>
        <_>
          4 17 9 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 13 10 3 -1.</_>
        <_>
          7 13 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 10 5 -1.</_>
        <_>
          9 0 5 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 8 2 15 -1.</_>
        <_>
          2 8 1 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 0 6 18 -1.</_>
        <_>
          15 0 2 18 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 21 14 2 -1.</_>
        <_>
          7 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 19 8 4 -1.</_>
        <_>
          9 19 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 21 16 2 -1.</_>
        <_>
          9 21 8 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 16 4 -1.</_>
        <_>
          6 0 8 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 9 5 -1.</_>
        <_>
          6 0 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 5 8 10 -1.</_>
        <_>
          10 5 8 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 1 18 8 -1.</_>
        <_>
          0 5 18 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 5 8 10 -1.</_>
        <_>
          10 5 8 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 20 10 3 -1.</_>
        <_>
          9 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 18 14 3 -1.</_>
        <_>
          4 19 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 16 6 7 -1.</_>
        <_>
          4 16 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 18 14 3 -1.</_>
        <_>
          4 19 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 6 7 -1.</_>
        <_>
          8 0 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 2 15 12 -1.</_>
        <_>
          7 6 5 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          5 10 4 9 -1.</_>
        <_>
          7 10 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 7 8 7 -1.</_>
        <_>
          12 9 4 7 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 1 18 18 -1.</_>
        <_>
          0 1 9 9 2.</_>
        <_>
          9 10 9 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 7 8 6 -1.</_>
        <_>
          9 9 8 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 9 3 14 -1.</_>
        <_>
          8 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 7 8 6 -1.</_>
        <_>
          9 9 8 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 0 8 4 -1.</_>
        <_>
          5 0 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 7 8 6 -1.</_>
        <_>
          9 9 8 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 6 8 -1.</_>
        <_>
          10 9 2 8 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 0 6 19 -1.</_>
        <_>
          15 0 2 19 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 6 19 -1.</_>
        <_>
          2 0 2 19 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 8 2 14 -1.</_>
        <_>
          13 8 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 4 16 3 -1.</_>
        <_>
          0 5 16 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 8 4 10 -1.</_>
        <_>
          8 13 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 17 10 6 -1.</_>
        <_>
          3 17 5 3 2.</_>
        <_>
          8 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 8 2 14 -1.</_>
        <_>
          13 8 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 16 5 -1.</_>
        <_>
          5 7 8 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 5 4 9 -1.</_>
        <_>
          15 5 2 9 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 0 3 14 -1.</_>
        <_>
          7 0 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 4 12 12 -1.</_>
        <_>
          10 8 4 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          7 3 4 9 -1.</_>
        <_>
          9 3 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 4 7 8 -1.</_>
        <_>
          10 6 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 4 7 8 -1.</_>
        <_>
          2 6 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 18 14 3 -1.</_>
        <_>
          4 19 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 9 2 14 -1.</_>
        <_>
          5 9 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 15 7 8 -1.</_>
        <_>
          12 17 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 7 20 -1.</_>
        <_>
          6 5 7 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 1 16 4 -1.</_>
        <_>
          10 1 8 2 2.</_>
        <_>
          2 3 8 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 3 10 -1.</_>
        <_>
          4 12 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 6 8 8 -1.</_>
        <_>
          12 8 4 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 10 12 8 -1.</_>
        <_>
          3 10 6 4 2.</_>
        <_>
          9 14 6 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 4 4 10 -1.</_>
        <_>
          8 9 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 5 9 -1.</_>
        <_>
          7 10 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 4 17 3 -1.</_>
        <_>
          1 5 17 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 3 14 3 -1.</_>
        <_>
          2 4 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 7 14 2 -1.</_>
        <_>
          2 7 7 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 19 8 4 -1.</_>
        <_>
          10 19 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 5 22 -1.</_>
        <_>
          5 11 5 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 19 8 4 -1.</_>
        <_>
          10 19 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 19 8 4 -1.</_>
        <_>
          5 19 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 12 4 9 -1.</_>
        <_>
          8 12 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 16 9 5 -1.</_>
        <_>
          4 16 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 20 15 3 -1.</_>
        <_>
          8 20 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 8 10 14 -1.</_>
        <_>
          8 8 5 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 5 7 6 -1.</_>
        <_>
          10 5 7 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 5 6 7 -1.</_>
        <_>
          9 5 3 7 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 4 9 10 -1.</_>
        <_>
          10 4 9 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 4 10 9 -1.</_>
        <_>
          9 4 5 9 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 15 7 8 -1.</_>
        <_>
          12 17 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 7 8 -1.</_>
        <_>
          0 17 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 19 4 -1.</_>
        <_>
          0 17 19 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 20 10 3 -1.</_>
        <_>
          9 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 8 4 15 -1.</_>
        <_>
          10 8 2 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 4 14 -1.</_>
        <_>
          4 7 2 7 2.</_>
        <_>
          6 14 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 8 2 15 -1.</_>
        <_>
          12 8 1 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 2 15 -1.</_>
        <_>
          6 8 1 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 12 4 11 -1.</_>
        <_>
          8 12 2 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 12 4 11 -1.</_>
        <_>
          9 12 2 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 4 3 10 -1.</_>
        <_>
          10 4 3 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 16 4 7 -1.</_>
        <_>
          5 16 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 17 16 3 -1.</_>
        <_>
          3 18 16 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 4 10 -1.</_>
        <_>
          2 12 2 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 14 12 6 -1.</_>
        <_>
          10 14 6 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 14 12 6 -1.</_>
        <_>
          3 14 6 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 12 4 -1.</_>
        <_>
          11 0 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 4 10 -1.</_>
        <_>
          9 0 2 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 10 3 -1.</_>
        <_>
          9 0 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 10 3 -1.</_>
        <_>
          5 0 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 5 8 8 -1.</_>
        <_>
          10 5 4 4 2.</_>
        <_>
          6 9 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 6 2 14 -1.</_>
        <_>
          5 6 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 6 10 -1.</_>
        <_>
          12 8 2 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 8 6 10 -1.</_>
        <_>
          5 8 2 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 15 12 6 -1.</_>
        <_>
          9 15 4 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 15 12 6 -1.</_>
        <_>
          6 15 4 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 5 5 8 -1.</_>
        <_>
          8 9 5 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 2 14 4 -1.</_>
        <_>
          7 2 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 6 7 -1.</_>
        <_>
          9 1 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 2 4 17 -1.</_>
        <_>
          7 2 2 17 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 9 15 -1.</_>
        <_>
          11 6 3 5 9.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 12 4 -1.</_>
        <_>
          4 0 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 1 8 8 -1.</_>
        <_>
          11 5 8 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 8 8 -1.</_>
        <_>
          0 5 8 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 3 14 -1.</_>
        <_>
          11 8 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 4 10 3 -1.</_>
        <_>
          9 4 5 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 8 2 11 -1.</_>
        <_>
          11 8 1 11 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 13 4 8 -1.</_>
        <_>
          3 17 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 11 8 12 -1.</_>
        <_>
          10 17 8 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 8 3 14 -1.</_>
        <_>
          7 8 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 9 2 10 -1.</_>
        <_>
          10 9 1 10 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 11 6 6 -1.</_>
        <_>
          8 11 3 6 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 6 16 4 -1.</_>
        <_>
          5 6 8 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 0 2 14 -1.</_>
        <_>
          12 7 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 9 3 14 -1.</_>
        <_>
          8 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 7 2 11 -1.</_>
        <_>
          11 7 1 11 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 11 2 -1.</_>
        <_>
          8 7 11 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 0 6 5 -1.</_>
        <_>
          7 0 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 9 5 -1.</_>
        <_>
          8 0 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 17 10 6 -1.</_>
        <_>
          12 17 5 3 2.</_>
        <_>
          7 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 6 4 15 -1.</_>
        <_>
          8 6 2 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 11 10 3 -1.</_>
        <_>
          5 11 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 7 3 14 -1.</_>
        <_>
          9 7 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 2 10 -1.</_>
        <_>
          10 8 1 10 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 3 9 18 -1.</_>
        <_>
          6 9 3 6 9.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 10 12 -1.</_>
        <_>
          13 0 5 6 2.</_>
        <_>
          8 6 5 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 12 12 11 -1.</_>
        <_>
          4 12 6 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 4 15 9 -1.</_>
        <_>
          7 7 5 3 9.</_></rects></_>
    <_>
      <rects>
        <_>
          3 7 10 10 -1.</_>
        <_>
          8 7 5 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 2 10 -1.</_>
        <_>
          10 8 1 10 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 18 6 5 -1.</_>
        <_>
          5 18 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 20 10 3 -1.</_>
        <_>
          9 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 4 14 -1.</_>
        <_>
          5 0 2 7 2.</_>
        <_>
          7 7 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 10 12 -1.</_>
        <_>
          13 0 5 6 2.</_>
        <_>
          8 6 5 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 8 18 -1.</_>
        <_>
          2 0 4 9 2.</_>
        <_>
          6 9 4 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 8 4 -1.</_>
        <_>
          10 0 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 9 9 2 -1.</_>
        <_>
          9 9 9 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 7 3 10 -1.</_>
        <_>
          15 12 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 3 10 -1.</_>
        <_>
          1 12 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 6 4 7 -1.</_>
        <_>
          15 6 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 15 6 7 -1.</_>
        <_>
          6 15 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 2 16 20 -1.</_>
        <_>
          10 2 8 10 2.</_>
        <_>
          2 12 8 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 17 7 6 -1.</_>
        <_>
          4 19 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 15 15 6 -1.</_>
        <_>
          3 18 15 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 14 3 -1.</_>
        <_>
          0 19 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 20 10 3 -1.</_>
        <_>
          9 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 4 18 -1.</_>
        <_>
          2 0 2 9 2.</_>
        <_>
          4 9 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 2 6 8 -1.</_>
        <_>
          10 6 6 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 2 8 8 -1.</_>
        <_>
          5 2 4 4 2.</_>
        <_>
          9 6 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 20 10 3 -1.</_>
        <_>
          9 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 18 3 -1.</_>
        <_>
          6 0 6 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 8 4 -1.</_>
        <_>
          10 0 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 8 4 -1.</_>
        <_>
          5 0 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 20 10 3 -1.</_>
        <_>
          9 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 9 8 2 -1.</_>
        <_>
          9 9 8 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 7 15 9 -1.</_>
        <_>
          9 7 5 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 8 3 14 -1.</_>
        <_>
          9 8 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 6 12 16 -1.</_>
        <_>
          9 6 6 16 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 6 12 16 -1.</_>
        <_>
          4 6 6 16 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 6 4 7 -1.</_>
        <_>
          10 6 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 15 5 6 -1.</_>
        <_>
          2 18 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 19 12 4 -1.</_>
        <_>
          11 19 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 19 12 4 -1.</_>
        <_>
          4 19 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 9 4 7 -1.</_>
        <_>
          10 9 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 9 4 9 -1.</_>
        <_>
          7 9 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 3 4 17 -1.</_>
        <_>
          7 3 2 17 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 21 14 2 -1.</_>
        <_>
          3 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 19 12 3 -1.</_>
        <_>
          6 19 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 3 22 -1.</_>
        <_>
          9 11 3 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 9 2 14 -1.</_>
        <_>
          6 9 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 6 16 -1.</_>
        <_>
          7 11 6 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 12 4 8 -1.</_>
        <_>
          1 16 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 12 15 3 -1.</_>
        <_>
          7 12 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 12 6 -1.</_>
        <_>
          1 17 6 3 2.</_>
        <_>
          7 20 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 4 9 -1.</_>
        <_>
          8 0 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 4 9 -1.</_>
        <_>
          9 0 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 5 20 -1.</_>
        <_>
          7 6 5 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 6 16 -1.</_>
        <_>
          3 7 2 16 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 7 4 10 -1.</_>
        <_>
          8 12 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 3 12 12 -1.</_>
        <_>
          5 7 4 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          8 6 3 14 -1.</_>
        <_>
          9 6 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 6 6 10 -1.</_>
        <_>
          2 6 3 5 2.</_>
        <_>
          5 11 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 6 4 14 -1.</_>
        <_>
          9 6 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 10 18 12 -1.</_>
        <_>
          0 10 9 6 2.</_>
        <_>
          9 16 9 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 6 4 14 -1.</_>
        <_>
          9 6 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 6 4 14 -1.</_>
        <_>
          8 6 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 15 18 6 -1.</_>
        <_>
          1 15 9 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 6 5 -1.</_>
        <_>
          4 17 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 17 12 6 -1.</_>
        <_>
          9 17 6 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 15 12 8 -1.</_>
        <_>
          4 15 6 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 7 19 3 -1.</_>
        <_>
          0 8 19 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 8 16 3 -1.</_>
        <_>
          1 9 16 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 6 7 6 -1.</_>
        <_>
          6 8 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 7 10 14 -1.</_>
        <_>
          4 7 5 7 2.</_>
        <_>
          9 14 5 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 12 10 -1.</_>
        <_>
          5 0 6 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 15 13 -1.</_>
        <_>
          7 0 5 13 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 6 12 6 -1.</_>
        <_>
          8 6 6 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 16 6 7 -1.</_>
        <_>
          4 16 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 4 8 8 -1.</_>
        <_>
          12 6 4 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 5 7 6 -1.</_>
        <_>
          7 7 7 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 7 18 3 -1.</_>
        <_>
          1 8 18 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 4 9 11 -1.</_>
        <_>
          8 4 3 11 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 0 6 7 -1.</_>
        <_>
          15 0 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 11 12 6 -1.</_>
        <_>
          3 11 6 3 2.</_>
        <_>
          9 14 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 4 3 16 -1.</_>
        <_>
          14 4 1 16 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 4 3 16 -1.</_>
        <_>
          4 4 1 16 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 9 16 8 -1.</_>
        <_>
          10 9 8 4 2.</_>
        <_>
          2 13 8 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 3 19 -1.</_>
        <_>
          4 0 1 19 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 1 8 10 -1.</_>
        <_>
          8 1 4 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 14 18 6 -1.</_>
        <_>
          6 14 6 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 6 15 9 -1.</_>
        <_>
          9 9 5 3 9.</_></rects></_>
    <_>
      <rects>
        <_>
          0 14 15 8 -1.</_>
        <_>
          5 14 5 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 20 15 3 -1.</_>
        <_>
          8 20 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 18 2 -1.</_>
        <_>
          0 16 18 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 15 17 3 -1.</_>
        <_>
          2 16 17 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 19 4 -1.</_>
        <_>
          0 2 19 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 12 4 -1.</_>
        <_>
          4 2 12 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 3 21 -1.</_>
        <_>
          4 0 1 21 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 18 8 4 -1.</_>
        <_>
          6 20 8 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 18 14 3 -1.</_>
        <_>
          1 19 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 18 9 5 -1.</_>
        <_>
          12 18 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 19 3 -1.</_>
        <_>
          0 19 19 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 8 3 14 -1.</_>
        <_>
          14 8 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 6 12 7 -1.</_>
        <_>
          5 6 6 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 6 16 16 -1.</_>
        <_>
          6 6 8 16 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 16 20 -1.</_>
        <_>
          4 1 8 20 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 9 4 14 -1.</_>
        <_>
          14 9 2 7 2.</_>
        <_>
          12 16 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 9 4 14 -1.</_>
        <_>
          3 9 2 7 2.</_>
        <_>
          5 16 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 11 6 10 -1.</_>
        <_>
          14 11 3 5 2.</_>
        <_>
          11 16 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 11 6 10 -1.</_>
        <_>
          2 11 3 5 2.</_>
        <_>
          5 16 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 8 16 9 -1.</_>
        <_>
          6 8 8 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 17 10 6 -1.</_>
        <_>
          2 17 5 3 2.</_>
        <_>
          7 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 7 8 7 -1.</_>
        <_>
          13 9 4 7 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 7 8 -1.</_>
        <_>
          6 9 7 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 7 6 16 -1.</_>
        <_>
          7 11 6 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 4 4 10 -1.</_>
        <_>
          7 9 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 9 5 -1.</_>
        <_>
          8 0 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 1 16 18 -1.</_>
        <_>
          5 1 8 18 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 21 14 2 -1.</_>
        <_>
          5 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 20 18 3 -1.</_>
        <_>
          6 20 6 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 9 3 14 -1.</_>
        <_>
          9 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 4 13 2 -1.</_>
        <_>
          2 4 13 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 0 10 16 -1.</_>
        <_>
          11 0 5 8 2.</_>
        <_>
          6 8 5 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 14 5 6 -1.</_>
        <_>
          2 17 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 8 4 8 -1.</_>
        <_>
          12 12 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 8 4 8 -1.</_>
        <_>
          3 12 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 6 3 10 -1.</_>
        <_>
          14 11 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 6 3 10 -1.</_>
        <_>
          2 11 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 5 12 16 -1.</_>
        <_>
          7 9 12 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 11 4 9 -1.</_>
        <_>
          8 11 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 18 10 5 -1.</_>
        <_>
          7 18 5 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 11 14 -1.</_>
        <_>
          4 7 11 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 9 15 -1.</_>
        <_>
          11 6 3 5 9.</_></rects></_>
    <_>
      <rects>
        <_>
          0 6 5 8 -1.</_>
        <_>
          0 10 5 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 0 4 13 -1.</_>
        <_>
          15 0 2 13 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 0 13 4 -1.</_>
        <_>
          4 0 13 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 3 9 5 -1.</_>
        <_>
          9 3 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 3 9 5 -1.</_>
        <_>
          7 3 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 12 4 -1.</_>
        <_>
          7 1 6 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 2 6 12 -1.</_>
        <_>
          0 8 6 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 12 5 -1.</_>
        <_>
          5 0 6 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 14 5 -1.</_>
        <_>
          9 0 7 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 1 4 14 -1.</_>
        <_>
          10 1 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 5 9 8 -1.</_>
        <_>
          3 7 9 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 7 16 9 -1.</_>
        <_>
          6 7 8 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 19 14 2 -1.</_>
        <_>
          7 19 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 20 10 3 -1.</_>
        <_>
          8 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 20 10 3 -1.</_>
        <_>
          6 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 8 3 10 -1.</_>
        <_>
          16 9 1 10 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 21 16 2 -1.</_>
        <_>
          8 21 8 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 6 15 3 -1.</_>
        <_>
          4 7 15 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 4 3 14 -1.</_>
        <_>
          7 4 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 18 10 5 -1.</_>
        <_>
          7 18 5 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 18 10 5 -1.</_>
        <_>
          7 18 5 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 10 16 -1.</_>
        <_>
          11 0 5 8 2.</_>
        <_>
          6 8 5 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 10 16 -1.</_>
        <_>
          3 0 5 8 2.</_>
        <_>
          8 8 5 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 7 4 -1.</_>
        <_>
          6 2 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 2 19 3 -1.</_>
        <_>
          0 3 19 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 12 4 -1.</_>
        <_>
          7 2 12 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 2 15 3 -1.</_>
        <_>
          0 3 15 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 5 18 3 -1.</_>
        <_>
          1 6 18 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 12 6 -1.</_>
        <_>
          3 2 12 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 10 10 -1.</_>
        <_>
          5 5 10 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 1 9 4 -1.</_>
        <_>
          5 3 9 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 2 12 6 -1.</_>
        <_>
          5 4 12 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 15 9 6 -1.</_>
        <_>
          1 17 9 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 13 14 9 -1.</_>
        <_>
          5 16 14 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 12 8 3 -1.</_>
        <_>
          7 13 8 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 8 2 15 -1.</_>
        <_>
          12 8 1 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 2 15 -1.</_>
        <_>
          6 8 1 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 5 3 14 -1.</_>
        <_>
          12 5 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 2 14 -1.</_>
        <_>
          6 8 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 6 3 14 -1.</_>
        <_>
          12 6 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 8 22 -1.</_>
        <_>
          0 0 4 11 2.</_>
        <_>
          4 11 4 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 10 4 8 -1.</_>
        <_>
          13 10 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 13 16 7 -1.</_>
        <_>
          5 13 8 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 10 4 8 -1.</_>
        <_>
          13 10 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 10 4 8 -1.</_>
        <_>
          4 10 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 10 6 -1.</_>
        <_>
          10 7 5 3 2.</_>
        <_>
          5 10 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 19 8 4 -1.</_>
        <_>
          4 19 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 15 15 3 -1.</_>
        <_>
          3 16 15 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 2 4 16 -1.</_>
        <_>
          7 2 2 8 2.</_>
        <_>
          9 10 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 6 4 12 -1.</_>
        <_>
          8 10 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 6 4 12 -1.</_>
        <_>
          7 10 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 15 14 2 -1.</_>
        <_>
          3 16 14 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 17 8 -1.</_>
        <_>
          0 17 17 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 3 9 10 -1.</_>
        <_>
          10 3 9 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 8 4 10 -1.</_>
        <_>
          7 13 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 8 7 15 -1.</_>
        <_>
          7 13 7 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 16 20 -1.</_>
        <_>
          5 0 8 20 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 18 9 5 -1.</_>
        <_>
          12 18 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 18 9 5 -1.</_>
        <_>
          4 18 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 7 8 12 -1.</_>
        <_>
          12 7 4 6 2.</_>
        <_>
          8 13 4 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 9 4 13 -1.</_>
        <_>
          4 9 2 13 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 14 7 4 -1.</_>
        <_>
          12 16 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 6 18 3 -1.</_>
        <_>
          0 7 18 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 16 18 7 -1.</_>
        <_>
          1 16 9 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 15 5 -1.</_>
        <_>
          5 18 5 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 5 4 8 -1.</_>
        <_>
          10 5 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 5 4 8 -1.</_>
        <_>
          7 5 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 6 5 -1.</_>
        <_>
          7 0 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 2 2 15 -1.</_>
        <_>
          7 2 1 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 12 4 -1.</_>
        <_>
          4 0 6 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 2 14 -1.</_>
        <_>
          5 7 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 16 14 4 -1.</_>
        <_>
          5 17 14 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 9 2 14 -1.</_>
        <_>
          3 9 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 0 4 7 -1.</_>
        <_>
          12 0 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 4 7 -1.</_>
        <_>
          5 0 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 9 15 -1.</_>
        <_>
          11 5 3 5 9.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 9 15 -1.</_>
        <_>
          5 5 3 5 9.</_></rects></_>
    <_>
      <rects>
        <_>
          16 5 2 16 -1.</_>
        <_>
          16 5 1 16 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 5 16 2 -1.</_>
        <_>
          3 5 16 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 11 6 9 -1.</_>
        <_>
          11 11 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 6 8 4 -1.</_>
        <_>
          7 6 4 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 0 8 8 -1.</_>
        <_>
          14 0 4 4 2.</_>
        <_>
          10 4 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 12 4 -1.</_>
        <_>
          7 0 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 11 6 9 -1.</_>
        <_>
          11 11 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 10 4 10 -1.</_>
        <_>
          5 10 2 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 12 6 5 -1.</_>
        <_>
          11 12 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 11 6 9 -1.</_>
        <_>
          6 11 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 12 7 4 -1.</_>
        <_>
          12 12 7 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 0 8 8 -1.</_>
        <_>
          1 0 4 4 2.</_>
        <_>
          5 4 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 4 9 10 -1.</_>
        <_>
          10 4 9 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 1 12 8 -1.</_>
        <_>
          1 1 6 4 2.</_>
        <_>
          7 5 6 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 14 16 2 -1.</_>
        <_>
          2 14 8 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 3 4 14 -1.</_>
        <_>
          8 3 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 6 7 -1.</_>
        <_>
          9 1 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 10 4 12 -1.</_>
        <_>
          3 14 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 4 6 7 -1.</_>
        <_>
          10 4 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 4 6 7 -1.</_>
        <_>
          7 4 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 7 14 8 -1.</_>
        <_>
          5 7 7 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 12 6 5 -1.</_>
        <_>
          5 12 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 9 4 7 -1.</_>
        <_>
          12 9 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 9 4 7 -1.</_>
        <_>
          5 9 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 2 4 12 -1.</_>
        <_>
          13 6 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 2 4 12 -1.</_>
        <_>
          2 6 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 2 16 8 -1.</_>
        <_>
          10 2 8 4 2.</_>
        <_>
          2 6 8 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 2 15 9 -1.</_>
        <_>
          7 5 5 3 9.</_></rects></_>
    <_>
      <rects>
        <_>
          8 7 3 12 -1.</_>
        <_>
          8 13 3 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 3 15 -1.</_>
        <_>
          3 0 1 15 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 8 16 4 -1.</_>
        <_>
          5 8 8 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 8 8 -1.</_>
        <_>
          10 0 4 4 2.</_>
        <_>
          6 4 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 9 2 14 -1.</_>
        <_>
          9 9 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 5 3 10 -1.</_>
        <_>
          8 10 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 9 3 14 -1.</_>
        <_>
          9 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 12 16 -1.</_>
        <_>
          6 11 12 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 3 16 -1.</_>
        <_>
          5 0 1 16 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 9 4 11 -1.</_>
        <_>
          13 9 2 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 14 3 -1.</_>
        <_>
          7 18 7 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 12 11 -1.</_>
        <_>
          9 9 6 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 16 9 -1.</_>
        <_>
          5 7 8 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 6 4 7 -1.</_>
        <_>
          11 6 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 11 12 12 -1.</_>
        <_>
          7 15 4 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          11 6 4 7 -1.</_>
        <_>
          11 6 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 6 10 -1.</_>
        <_>
          6 0 2 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 9 2 14 -1.</_>
        <_>
          13 9 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 9 2 14 -1.</_>
        <_>
          5 9 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 6 16 -1.</_>
        <_>
          7 11 6 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 16 4 7 -1.</_>
        <_>
          4 16 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 17 9 6 -1.</_>
        <_>
          12 17 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 16 6 7 -1.</_>
        <_>
          4 16 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          14 13 5 6 -1.</_>
        <_>
          14 16 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 12 6 -1.</_>
        <_>
          6 0 6 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 14 7 -1.</_>
        <_>
          4 0 7 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 9 22 -1.</_>
        <_>
          5 11 9 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 8 8 4 -1.</_>
        <_>
          11 10 8 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 4 8 -1.</_>
        <_>
          9 0 2 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 17 14 2 -1.</_>
        <_>
          5 18 14 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 14 3 -1.</_>
        <_>
          1 18 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 1 12 12 -1.</_>
        <_>
          10 5 4 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          1 1 12 12 -1.</_>
        <_>
          5 5 4 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 7 18 -1.</_>
        <_>
          6 9 7 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 12 9 -1.</_>
        <_>
          3 0 6 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 9 3 14 -1.</_>
        <_>
          10 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 5 5 9 -1.</_>
        <_>
          7 8 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 9 3 14 -1.</_>
        <_>
          10 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 9 3 14 -1.</_>
        <_>
          8 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 10 5 8 -1.</_>
        <_>
          12 10 5 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 6 10 7 -1.</_>
        <_>
          8 6 5 7 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 15 7 4 -1.</_>
        <_>
          12 17 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 7 4 -1.</_>
        <_>
          0 17 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 6 2 16 -1.</_>
        <_>
          15 6 1 16 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 9 4 8 -1.</_>
        <_>
          3 13 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 14 19 3 -1.</_>
        <_>
          0 15 19 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 12 4 7 -1.</_>
        <_>
          3 12 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 12 4 11 -1.</_>
        <_>
          14 12 2 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 5 6 -1.</_>
        <_>
          0 11 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 14 3 -1.</_>
        <_>
          4 0 7 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 14 3 -1.</_>
        <_>
          8 0 7 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 3 7 4 -1.</_>
        <_>
          12 5 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 7 4 -1.</_>
        <_>
          0 5 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 4 7 -1.</_>
        <_>
          10 8 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 12 4 11 -1.</_>
        <_>
          3 12 2 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 10 16 4 -1.</_>
        <_>
          2 11 16 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 11 9 3 -1.</_>
        <_>
          6 12 9 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 6 12 16 -1.</_>
        <_>
          8 6 6 16 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 6 14 4 -1.</_>
        <_>
          2 6 7 2 2.</_>
        <_>
          9 8 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 6 10 6 -1.</_>
        <_>
          10 6 5 3 2.</_>
        <_>
          5 9 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 2 14 -1.</_>
        <_>
          1 9 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 18 9 5 -1.</_>
        <_>
          13 18 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 9 10 3 -1.</_>
        <_>
          3 10 10 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 18 9 5 -1.</_>
        <_>
          13 18 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 9 5 -1.</_>
        <_>
          3 18 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 12 9 -1.</_>
        <_>
          9 8 4 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 8 12 9 -1.</_>
        <_>
          6 8 4 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 6 4 14 -1.</_>
        <_>
          10 6 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 20 15 3 -1.</_>
        <_>
          7 20 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 4 9 5 -1.</_>
        <_>
          8 4 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 6 4 14 -1.</_>
        <_>
          7 6 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 2 14 -1.</_>
        <_>
          10 0 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 2 14 -1.</_>
        <_>
          8 0 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 0 4 8 -1.</_>
        <_>
          12 0 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 14 3 -1.</_>
        <_>
          0 4 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 20 10 3 -1.</_>
        <_>
          5 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 18 7 4 -1.</_>
        <_>
          6 20 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 6 6 9 -1.</_>
        <_>
          5 6 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 0 6 7 -1.</_>
        <_>
          15 0 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 13 4 10 -1.</_>
        <_>
          5 13 2 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 12 4 10 -1.</_>
        <_>
          12 12 2 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 12 4 7 -1.</_>
        <_>
          5 12 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 0 6 14 -1.</_>
        <_>
          15 0 2 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 6 12 -1.</_>
        <_>
          2 0 2 12 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 19 14 4 -1.</_>
        <_>
          12 19 7 2 2.</_>
        <_>
          5 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 9 10 -1.</_>
        <_>
          0 17 9 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 13 5 6 -1.</_>
        <_>
          14 16 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 8 4 -1.</_>
        <_>
          0 18 8 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 16 16 3 -1.</_>
        <_>
          3 17 16 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 6 7 -1.</_>
        <_>
          8 0 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 16 5 -1.</_>
        <_>
          6 0 8 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 17 10 -1.</_>
        <_>
          0 5 17 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 3 15 -1.</_>
        <_>
          9 1 1 15 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 2 8 20 -1.</_>
        <_>
          0 7 8 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 7 4 10 -1.</_>
        <_>
          8 12 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 4 10 -1.</_>
        <_>
          7 12 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 3 17 -1.</_>
        <_>
          12 0 1 17 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 3 17 -1.</_>
        <_>
          6 0 1 17 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 9 3 14 -1.</_>
        <_>
          13 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 2 6 10 -1.</_>
        <_>
          9 2 3 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 21 14 2 -1.</_>
        <_>
          4 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 8 4 -1.</_>
        <_>
          9 0 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 4 8 -1.</_>
        <_>
          10 0 4 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 0 12 6 -1.</_>
        <_>
          3 0 6 3 2.</_>
        <_>
          9 3 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 8 6 8 -1.</_>
        <_>
          10 8 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 13 12 8 -1.</_>
        <_>
          4 13 6 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 8 6 8 -1.</_>
        <_>
          10 8 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 6 8 -1.</_>
        <_>
          7 8 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 13 8 10 -1.</_>
        <_>
          9 13 4 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 14 8 9 -1.</_>
        <_>
          6 14 4 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 15 9 5 -1.</_>
        <_>
          12 15 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 15 4 7 -1.</_>
        <_>
          9 15 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 19 12 4 -1.</_>
        <_>
          4 19 6 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 15 6 8 -1.</_>
        <_>
          8 15 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 5 8 8 -1.</_>
        <_>
          12 5 4 4 2.</_>
        <_>
          8 9 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 14 7 4 -1.</_>
        <_>
          0 16 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 2 4 8 -1.</_>
        <_>
          11 3 2 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 12 17 3 -1.</_>
        <_>
          1 13 17 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 8 4 15 -1.</_>
        <_>
          14 8 2 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 12 14 3 -1.</_>
        <_>
          2 13 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 12 7 6 -1.</_>
        <_>
          6 14 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 2 12 6 -1.</_>
        <_>
          2 2 6 3 2.</_>
        <_>
          8 5 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 8 5 -1.</_>
        <_>
          11 0 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 8 5 -1.</_>
        <_>
          4 0 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 2 18 20 -1.</_>
        <_>
          1 2 9 20 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 5 10 8 -1.</_>
        <_>
          9 5 5 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 8 7 10 -1.</_>
        <_>
          7 13 7 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 4 14 -1.</_>
        <_>
          8 7 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 7 4 16 -1.</_>
        <_>
          15 7 2 16 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 12 7 -1.</_>
        <_>
          4 0 4 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 7 4 7 -1.</_>
        <_>
          11 7 2 7 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 4 6 15 -1.</_>
        <_>
          7 4 3 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 10 9 13 -1.</_>
        <_>
          9 10 3 13 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 14 4 7 -1.</_>
        <_>
          3 14 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 1 3 14 -1.</_>
        <_>
          12 1 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 11 4 8 -1.</_>
        <_>
          7 11 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 6 4 7 -1.</_>
        <_>
          11 6 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 6 4 7 -1.</_>
        <_>
          6 6 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 5 9 9 -1.</_>
        <_>
          10 5 3 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 1 12 12 -1.</_>
        <_>
          6 5 4 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          4 19 14 4 -1.</_>
        <_>
          11 19 7 2 2.</_>
        <_>
          4 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 19 14 4 -1.</_>
        <_>
          1 19 7 2 2.</_>
        <_>
          8 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 18 9 5 -1.</_>
        <_>
          12 18 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 18 9 5 -1.</_>
        <_>
          4 18 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 4 8 6 -1.</_>
        <_>
          11 4 4 6 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 8 7 6 -1.</_>
        <_>
          6 10 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 17 14 2 -1.</_>
        <_>
          5 18 14 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 6 9 3 -1.</_>
        <_>
          5 7 9 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 9 4 11 -1.</_>
        <_>
          13 9 2 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 9 4 11 -1.</_>
        <_>
          4 9 2 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 0 3 14 -1.</_>
        <_>
          13 0 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 3 14 -1.</_>
        <_>
          5 0 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 10 5 6 -1.</_>
        <_>
          7 13 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 17 4 -1.</_>
        <_>
          0 14 17 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 5 6 10 -1.</_>
        <_>
          12 7 2 10 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 9 12 12 -1.</_>
        <_>
          6 13 4 4 9.</_></rects></_>
    <_>
      <rects>
        <_>
          1 15 12 8 -1.</_>
        <_>
          7 15 6 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 8 8 -1.</_>
        <_>
          10 0 4 4 2.</_>
        <_>
          6 4 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 7 8 -1.</_>
        <_>
          0 17 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 7 4 8 -1.</_>
        <_>
          8 11 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 8 2 14 -1.</_>
        <_>
          6 8 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 8 7 4 -1.</_>
        <_>
          12 10 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 13 14 4 -1.</_>
        <_>
          0 13 7 2 2.</_>
        <_>
          7 15 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 13 7 8 -1.</_>
        <_>
          6 15 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 4 15 -1.</_>
        <_>
          8 7 2 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 16 5 6 -1.</_>
        <_>
          11 19 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 6 10 -1.</_>
        <_>
          4 0 3 5 2.</_>
        <_>
          7 5 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 10 7 6 -1.</_>
        <_>
          9 12 7 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 0 14 2 -1.</_>
        <_>
          9 0 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 10 18 8 -1.</_>
        <_>
          10 10 9 4 2.</_>
        <_>
          1 14 9 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 18 15 3 -1.</_>
        <_>
          1 19 15 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 18 14 3 -1.</_>
        <_>
          4 19 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 19 18 -1.</_>
        <_>
          0 9 19 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 11 20 -1.</_>
        <_>
          4 10 11 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 9 18 -1.</_>
        <_>
          5 9 9 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 4 20 -1.</_>
        <_>
          9 10 4 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 11 6 6 -1.</_>
        <_>
          1 14 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 16 6 6 -1.</_>
        <_>
          12 19 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 8 2 14 -1.</_>
        <_>
          4 8 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 11 5 12 -1.</_>
        <_>
          7 15 5 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 11 5 12 -1.</_>
        <_>
          5 14 5 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 0 4 16 -1.</_>
        <_>
          15 0 2 8 2.</_>
        <_>
          13 8 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 12 8 -1.</_>
        <_>
          7 0 6 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 6 7 -1.</_>
        <_>
          15 11 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 7 8 -1.</_>
        <_>
          0 10 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 6 7 6 -1.</_>
        <_>
          6 8 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 4 14 -1.</_>
        <_>
          7 8 4 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 17 6 6 -1.</_>
        <_>
          13 17 3 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 11 4 12 -1.</_>
        <_>
          5 17 4 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 17 6 6 -1.</_>
        <_>
          13 17 3 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 2 14 -1.</_>
        <_>
          0 15 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 18 6 5 -1.</_>
        <_>
          13 18 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 2 14 -1.</_>
        <_>
          5 0 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 6 8 -1.</_>
        <_>
          15 11 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 11 3 12 -1.</_>
        <_>
          1 17 3 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 18 6 5 -1.</_>
        <_>
          12 18 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 4 8 -1.</_>
        <_>
          0 19 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 11 6 8 -1.</_>
        <_>
          15 11 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 11 6 8 -1.</_>
        <_>
          2 11 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 17 14 3 -1.</_>
        <_>
          5 18 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 7 6 -1.</_>
        <_>
          0 17 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 4 10 -1.</_>
        <_>
          10 8 2 10 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 11 16 7 -1.</_>
        <_>
          5 11 8 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 9 16 -1.</_>
        <_>
          8 0 3 16 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 6 2 14 -1.</_>
        <_>
          7 6 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 5 4 15 -1.</_>
        <_>
          12 5 2 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 8 10 4 -1.</_>
        <_>
          9 8 10 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 1 4 14 -1.</_>
        <_>
          8 1 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 4 14 -1.</_>
        <_>
          9 1 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 14 18 9 -1.</_>
        <_>
          7 17 6 3 9.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 7 9 -1.</_>
        <_>
          6 12 7 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 11 18 2 -1.</_>
        <_>
          1 12 18 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 4 16 -1.</_>
        <_>
          7 11 4 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 10 15 3 -1.</_>
        <_>
          2 11 15 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 12 7 9 -1.</_>
        <_>
          6 15 7 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 10 15 3 -1.</_>
        <_>
          4 11 15 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 19 14 4 -1.</_>
        <_>
          0 19 7 2 2.</_>
        <_>
          7 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 17 14 3 -1.</_>
        <_>
          5 18 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 3 14 -1.</_>
        <_>
          2 7 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 6 7 -1.</_>
        <_>
          11 0 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 6 7 -1.</_>
        <_>
          6 0 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 5 8 6 -1.</_>
        <_>
          6 5 4 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 2 3 16 -1.</_>
        <_>
          6 2 1 16 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 4 4 15 -1.</_>
        <_>
          16 4 2 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 12 6 5 -1.</_>
        <_>
          6 12 3 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 9 3 14 -1.</_>
        <_>
          9 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 16 7 4 -1.</_>
        <_>
          0 18 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 16 14 3 -1.</_>
        <_>
          5 17 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 4 4 15 -1.</_>
        <_>
          1 4 2 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 2 8 6 -1.</_>
        <_>
          10 4 8 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 2 8 6 -1.</_>
        <_>
          1 4 8 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 6 4 16 -1.</_>
        <_>
          12 6 2 8 2.</_>
        <_>
          10 14 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 4 18 -1.</_>
        <_>
          7 1 2 9 2.</_>
        <_>
          9 10 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 4 4 7 -1.</_>
        <_>
          8 4 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 4 4 7 -1.</_>
        <_>
          9 4 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 12 14 -1.</_>
        <_>
          7 0 6 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 1 2 14 -1.</_>
        <_>
          3 1 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 14 4 -1.</_>
        <_>
          0 18 7 2 2.</_>
        <_>
          7 20 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 8 8 -1.</_>
        <_>
          10 0 4 4 2.</_>
        <_>
          6 4 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 9 6 10 -1.</_>
        <_>
          4 9 3 5 2.</_>
        <_>
          7 14 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 18 6 -1.</_>
        <_>
          10 17 9 3 2.</_>
        <_>
          1 20 9 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 6 21 -1.</_>
        <_>
          7 7 2 7 9.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 12 7 -1.</_>
        <_>
          6 7 6 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 12 3 -1.</_>
        <_>
          7 0 6 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 0 9 5 -1.</_>
        <_>
          8 0 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 9 3 14 -1.</_>
        <_>
          8 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 14 16 9 -1.</_>
        <_>
          3 17 16 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 6 6 -1.</_>
        <_>
          4 17 3 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 1 10 20 -1.</_>
        <_>
          5 6 10 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 16 12 7 -1.</_>
        <_>
          4 16 6 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 9 4 -1.</_>
        <_>
          5 2 9 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 13 6 -1.</_>
        <_>
          3 2 13 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          11 13 7 8 -1.</_>
        <_>
          11 15 7 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 4 8 -1.</_>
        <_>
          3 4 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 17 9 6 -1.</_>
        <_>
          12 17 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 5 7 6 -1.</_>
        <_>
          6 7 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 17 7 6 -1.</_>
        <_>
          8 19 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 12 5 8 -1.</_>
        <_>
          5 16 5 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 15 19 2 -1.</_>
        <_>
          0 16 19 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 7 4 -1.</_>
        <_>
          6 9 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 2 21 -1.</_>
        <_>
          9 7 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 19 15 4 -1.</_>
        <_>
          5 19 5 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 20 10 3 -1.</_>
        <_>
          9 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 15 3 -1.</_>
        <_>
          0 18 15 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 13 6 5 -1.</_>
        <_>
          12 13 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 7 6 -1.</_>
        <_>
          6 9 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 15 14 3 -1.</_>
        <_>
          3 16 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 20 10 3 -1.</_>
        <_>
          5 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 8 4 -1.</_>
        <_>
          6 7 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 7 6 -1.</_>
        <_>
          1 19 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 17 12 4 -1.</_>
        <_>
          11 17 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 15 6 7 -1.</_>
        <_>
          5 15 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 7 12 7 -1.</_>
        <_>
          6 7 6 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 9 12 12 -1.</_>
        <_>
          1 13 12 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 6 5 9 -1.</_>
        <_>
          12 9 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 6 5 9 -1.</_>
        <_>
          2 9 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 6 6 7 -1.</_>
        <_>
          14 8 2 7 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 9 8 10 -1.</_>
        <_>
          5 9 4 5 2.</_>
        <_>
          9 14 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 11 16 6 -1.</_>
        <_>
          10 11 8 3 2.</_>
        <_>
          2 14 8 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 4 3 16 -1.</_>
        <_>
          9 4 1 16 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 9 4 14 -1.</_>
        <_>
          9 9 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 9 4 14 -1.</_>
        <_>
          8 9 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 17 12 4 -1.</_>
        <_>
          11 17 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 12 4 -1.</_>
        <_>
          4 17 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 12 6 10 -1.</_>
        <_>
          16 12 3 5 2.</_>
        <_>
          13 17 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 6 6 -1.</_>
        <_>
          3 17 3 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 4 6 8 -1.</_>
        <_>
          12 4 3 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 6 10 15 -1.</_>
        <_>
          8 6 5 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 10 7 4 -1.</_>
        <_>
          10 10 7 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 9 9 7 -1.</_>
        <_>
          4 9 3 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 18 6 -1.</_>
        <_>
          10 17 9 3 2.</_>
        <_>
          1 20 9 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 13 3 -1.</_>
        <_>
          5 1 13 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 0 3 9 -1.</_>
        <_>
          11 1 1 9 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 0 9 3 -1.</_>
        <_>
          8 1 9 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 1 12 12 -1.</_>
        <_>
          13 1 6 6 2.</_>
        <_>
          7 7 6 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 4 8 6 -1.</_>
        <_>
          7 4 8 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 11 8 4 -1.</_>
        <_>
          11 11 8 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 11 4 8 -1.</_>
        <_>
          8 11 2 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 10 7 4 -1.</_>
        <_>
          10 10 7 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 10 4 7 -1.</_>
        <_>
          9 10 2 7 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 3 14 -1.</_>
        <_>
          9 7 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 6 10 7 -1.</_>
        <_>
          8 6 5 7 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 6 16 3 -1.</_>
        <_>
          3 7 16 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 5 2 17 -1.</_>
        <_>
          5 5 1 17 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 0 6 18 -1.</_>
        <_>
          15 0 3 9 2.</_>
        <_>
          12 9 3 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 4 6 16 -1.</_>
        <_>
          3 4 3 8 2.</_>
        <_>
          6 12 3 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 0 6 18 -1.</_>
        <_>
          15 0 3 9 2.</_>
        <_>
          12 9 3 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 1 16 4 -1.</_>
        <_>
          0 1 8 2 2.</_>
        <_>
          8 3 8 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 12 12 5 -1.</_>
        <_>
          6 12 6 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 7 3 10 -1.</_>
        <_>
          3 12 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 3 7 12 -1.</_>
        <_>
          11 7 7 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 6 8 6 -1.</_>
        <_>
          0 8 8 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 3 7 6 -1.</_>
        <_>
          12 5 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 7 6 -1.</_>
        <_>
          0 5 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 10 6 8 -1.</_>
        <_>
          15 10 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 14 2 -1.</_>
        <_>
          0 18 14 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 10 6 8 -1.</_>
        <_>
          15 10 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 14 2 -1.</_>
        <_>
          0 18 14 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 8 8 -1.</_>
        <_>
          10 0 4 4 2.</_>
        <_>
          6 4 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 10 6 8 -1.</_>
        <_>
          2 10 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 0 3 14 -1.</_>
        <_>
          14 0 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 6 7 -1.</_>
        <_>
          8 0 2 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 8 8 -1.</_>
        <_>
          10 0 4 4 2.</_>
        <_>
          6 4 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 8 8 -1.</_>
        <_>
          5 0 4 4 2.</_>
        <_>
          9 4 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 7 16 7 -1.</_>
        <_>
          3 7 8 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 7 16 7 -1.</_>
        <_>
          8 7 8 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 11 10 8 -1.</_>
        <_>
          7 11 5 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 8 6 9 -1.</_>
        <_>
          14 8 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 8 6 9 -1.</_>
        <_>
          3 8 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 3 14 11 -1.</_>
        <_>
          4 3 7 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 5 13 3 -1.</_>
        <_>
          4 6 13 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 0 6 9 -1.</_>
        <_>
          9 0 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 0 14 12 -1.</_>
        <_>
          1 0 7 6 2.</_>
        <_>
          8 6 7 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 0 8 4 -1.</_>
        <_>
          10 0 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 10 4 12 -1.</_>
        <_>
          5 10 2 12 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 2 22 -1.</_>
        <_>
          11 11 2 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 19 14 4 -1.</_>
        <_>
          0 19 7 2 2.</_>
        <_>
          7 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 8 2 8 -1.</_>
        <_>
          10 8 1 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 0 4 14 -1.</_>
        <_>
          5 0 2 7 2.</_>
        <_>
          7 7 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 4 4 10 -1.</_>
        <_>
          8 9 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 8 8 2 -1.</_>
        <_>
          9 8 8 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          0 7 19 3 -1.</_>
        <_>
          0 8 19 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 8 19 2 -1.</_>
        <_>
          0 9 19 1 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 6 18 4 -1.</_>
        <_>
          10 6 9 2 2.</_>
        <_>
          1 8 9 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 1 8 18 -1.</_>
        <_>
          6 1 4 18 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 11 10 12 -1.</_>
        <_>
          11 11 5 6 2.</_>
        <_>
          6 17 5 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 7 9 11 -1.</_>
        <_>
          6 7 3 11 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 0 6 14 -1.</_>
        <_>
          11 0 2 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 16 12 7 -1.</_>
        <_>
          6 16 4 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 15 15 6 -1.</_>
        <_>
          7 15 5 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          5 2 8 7 -1.</_>
        <_>
          7 2 4 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 0 4 14 -1.</_>
        <_>
          9 0 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 4 14 -1.</_>
        <_>
          8 0 2 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 18 12 5 -1.</_>
        <_>
          11 18 4 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 18 15 3 -1.</_>
        <_>
          1 19 15 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 17 9 6 -1.</_>
        <_>
          12 17 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 8 9 6 -1.</_>
        <_>
          5 10 9 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 10 4 9 -1.</_>
        <_>
          12 11 2 9 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 10 9 4 -1.</_>
        <_>
          7 11 9 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 3 2 16 -1.</_>
        <_>
          15 11 2 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 5 6 -1.</_>
        <_>
          1 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 16 5 6 -1.</_>
        <_>
          12 19 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 2 3 14 -1.</_>
        <_>
          6 2 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 17 9 6 -1.</_>
        <_>
          12 17 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 1 6 9 -1.</_>
        <_>
          8 1 2 9 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 7 10 5 -1.</_>
        <_>
          7 7 5 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 4 20 -1.</_>
        <_>
          6 0 2 10 2.</_>
        <_>
          8 10 2 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 10 3 9 -1.</_>
        <_>
          14 11 1 9 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 10 9 3 -1.</_>
        <_>
          5 11 9 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 21 14 2 -1.</_>
        <_>
          5 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 6 3 14 -1.</_>
        <_>
          9 6 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 1 4 9 -1.</_>
        <_>
          8 1 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 1 4 9 -1.</_>
        <_>
          9 1 2 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 17 12 6 -1.</_>
        <_>
          13 17 6 3 2.</_>
        <_>
          7 20 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 4 10 6 -1.</_>
        <_>
          8 4 5 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 0 4 8 -1.</_>
        <_>
          15 4 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 5 6 8 -1.</_>
        <_>
          5 5 2 8 3.</_></rects></_>
    <_>
      <rects>
        <_>
          15 0 4 8 -1.</_>
        <_>
          15 4 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 4 8 -1.</_>
        <_>
          0 4 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 9 5 -1.</_>
        <_>
          10 0 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 0 6 5 -1.</_>
        <_>
          6 0 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 21 14 2 -1.</_>
        <_>
          5 21 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 3 8 9 -1.</_>
        <_>
          9 3 4 9 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 1 12 8 -1.</_>
        <_>
          12 1 6 4 2.</_>
        <_>
          6 5 6 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 10 10 11 -1.</_>
        <_>
          9 10 5 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 1 3 15 -1.</_>
        <_>
          13 1 1 15 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 3 8 12 -1.</_>
        <_>
          8 3 4 12 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 2 10 8 -1.</_>
        <_>
          8 2 5 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 4 19 6 -1.</_>
        <_>
          0 6 19 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 0 11 16 -1.</_>
        <_>
          4 4 11 8 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 1 6 5 -1.</_>
        <_>
          7 1 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 5 14 18 -1.</_>
        <_>
          10 5 7 9 2.</_>
        <_>
          3 14 7 9 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 5 6 -1.</_>
        <_>
          1 20 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 0 4 14 -1.</_>
        <_>
          15 0 2 7 2.</_>
        <_>
          13 7 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 4 14 -1.</_>
        <_>
          2 0 2 7 2.</_>
        <_>
          4 7 2 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 2 2 10 -1.</_>
        <_>
          10 2 1 10 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 1 9 3 -1.</_>
        <_>
          8 2 9 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 2 10 6 -1.</_>
        <_>
          11 2 5 3 2.</_>
        <_>
          6 5 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 12 9 6 -1.</_>
        <_>
          1 14 9 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 2 10 6 -1.</_>
        <_>
          11 2 5 3 2.</_>
        <_>
          6 5 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 2 10 6 -1.</_>
        <_>
          3 2 5 3 2.</_>
        <_>
          8 5 5 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 5 20 -1.</_>
        <_>
          7 5 5 10 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 10 12 7 -1.</_>
        <_>
          5 10 6 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 18 14 4 -1.</_>
        <_>
          0 18 7 2 2.</_>
        <_>
          7 20 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 7 3 15 -1.</_>
        <_>
          10 7 1 15 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 8 6 5 -1.</_>
        <_>
          9 8 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 4 2 17 -1.</_>
        <_>
          9 4 1 17 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 4 2 17 -1.</_>
        <_>
          9 4 1 17 2.</_></rects></_>
    <_>
      <rects>
        <_>
          8 18 9 5 -1.</_>
        <_>
          11 18 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 18 9 5 -1.</_>
        <_>
          5 18 3 5 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 18 6 5 -1.</_>
        <_>
          12 18 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 15 6 5 -1.</_>
        <_>
          8 15 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 0 6 10 -1.</_>
        <_>
          15 0 2 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 14 10 9 -1.</_>
        <_>
          2 17 10 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 0 6 10 -1.</_>
        <_>
          15 0 2 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 0 6 10 -1.</_>
        <_>
          2 0 2 10 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 5 3 12 -1.</_>
        <_>
          12 5 3 6 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 18 7 4 -1.</_>
        <_>
          6 20 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          14 7 4 12 -1.</_>
        <_>
          15 8 2 12 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 7 12 4 -1.</_>
        <_>
          4 8 12 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 13 5 9 -1.</_>
        <_>
          14 16 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 13 5 9 -1.</_>
        <_>
          0 16 5 3 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 14 7 6 -1.</_>
        <_>
          12 16 7 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 16 6 6 -1.</_>
        <_>
          1 19 6 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 9 4 -1.</_>
        <_>
          7 2 9 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 9 18 3 -1.</_>
        <_>
          0 10 18 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          9 17 9 6 -1.</_>
        <_>
          12 17 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          2 14 15 9 -1.</_>
        <_>
          7 17 5 3 9.</_></rects></_>
    <_>
      <rects>
        <_>
          9 13 8 8 -1.</_>
        <_>
          9 17 8 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          4 9 2 14 -1.</_>
        <_>
          5 9 1 14 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 10 4 13 -1.</_>
        <_>
          12 10 2 13 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 10 4 13 -1.</_>
        <_>
          5 10 2 13 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 5 14 2 -1.</_>
        <_>
          5 5 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 5 14 2 -1.</_>
        <_>
          7 5 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          13 12 6 10 -1.</_>
        <_>
          16 12 3 5 2.</_>
        <_>
          13 17 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 12 6 10 -1.</_>
        <_>
          0 12 3 5 2.</_>
        <_>
          3 17 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 8 5 12 -1.</_>
        <_>
          12 11 5 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 8 5 12 -1.</_>
        <_>
          2 11 5 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 8 7 4 -1.</_>
        <_>
          6 10 7 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 14 3 -1.</_>
        <_>
          0 18 14 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 7 2 15 -1.</_>
        <_>
          12 7 1 15 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 17 9 6 -1.</_>
        <_>
          4 17 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 6 9 7 -1.</_>
        <_>
          13 9 3 7 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 6 7 9 -1.</_>
        <_>
          6 9 7 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 8 10 4 -1.</_>
        <_>
          5 10 10 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 6 6 14 -1.</_>
        <_>
          0 13 6 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 1 18 22 -1.</_>
        <_>
          10 1 9 11 2.</_>
        <_>
          1 12 9 11 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 5 17 3 -1.</_>
        <_>
          1 6 17 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          13 12 6 5 -1.</_>
        <_>
          13 12 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 5 16 3 -1.</_>
        <_>
          0 6 16 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          12 6 6 17 -1.</_>
        <_>
          12 6 3 17 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 6 6 17 -1.</_>
        <_>
          4 6 3 17 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 15 18 2 -1.</_>
        <_>
          1 15 9 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 5 2 16 -1.</_>
        <_>
          1 5 1 16 2.</_></rects></_>
    <_>
      <rects>
        <_>
          15 12 4 10 -1.</_>
        <_>
          15 17 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 5 16 3 -1.</_>
        <_>
          1 6 16 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          6 9 9 12 -1.</_>
        <_>
          6 12 9 6 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 13 4 8 -1.</_>
        <_>
          3 17 4 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          9 13 8 8 -1.</_>
        <_>
          9 17 8 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 0 8 10 -1.</_>
        <_>
          5 0 4 5 2.</_>
        <_>
          9 5 4 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 4 18 6 -1.</_>
        <_>
          10 4 9 3 2.</_>
        <_>
          1 7 9 3 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 16 9 6 -1.</_>
        <_>
          3 18 9 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 17 14 4 -1.</_>
        <_>
          3 18 14 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 3 9 6 -1.</_>
        <_>
          2 5 9 2 3.</_></rects></_>
    <_>
      <rects>
        <_>
          0 3 19 3 -1.</_>
        <_>
          0 4 19 1 3.</_></rects></_>
    <_>
      <rects>
        <_>
          1 3 16 4 -1.</_>
        <_>
          1 4 16 2 2.</_></rects></_>
    <_>
      <rects>
        <_>
          11 0 6 14 -1.</_>
        <_>
          14 0 3 7 2.</_>
        <_>
          11 7 3 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          0 17 9 6 -1.</_>
        <_>
          3 17 3 6 3.</_></rects></_>
    <_>
      <rects>
        <_>
          7 16 8 7 -1.</_>
        <_>
          9 16 4 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          3 14 10 5 -1.</_>
        <_>
          8 14 5 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          12 9 3 14 -1.</_>
        <_>
          13 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          4 9 3 14 -1.</_>
        <_>
          5 9 1 14 3.</_></rects></_>
    <_>
      <rects>
        <_>
          10 9 6 14 -1.</_>
        <_>
          13 9 3 7 2.</_>
        <_>
          10 16 3 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          6 0 6 5 -1.</_>
        <_>
          9 0 3 5 2.</_></rects></_>
    <_>
      <rects>
        <_>
          7 0 6 8 -1.</_>
        <_>
          7 4 6 4 2.</_></rects></_>
    <_>
      <rects>
        <_>
          2 0 11 21 -1.</_>
        <_>
          2 7 11 7 3.</_></rects></_>
    <_>
      <rects>
        <_>
          8 8 4 12 -1.</_>
        <_>
          8 12 4 4 3.</_></rects></_>
    <_>
      <rects>
        <_>
          3 9 6 14 -1.</_>
        <_>
          3 9 3 7 2.</_>
        <_>
          6 16 3 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          10 7 8 7 -1.</_>
        <_>
          12 7 4 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          1 7 8 7 -1.</_>
        <_>
          3 7 4 7 2.</_></rects></_>
    <_>
      <rects>
        <_>
          5 2 9 20 -1.</_>
        <_>
          8 2 3 20 3.</_></rects></_></features></cascade>
</opencv_storage>

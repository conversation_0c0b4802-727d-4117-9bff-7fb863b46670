# 并行处理配置文件
# Parallel Processing Configuration

class ParallelConfig:
    """并行处理配置类"""
    
    # 基本并行参数
    MAX_WORKERS = 4  # 最大工作线程数
    BATCH_SIZE = 4   # 批处理大小
    FRAME_BUFFER_SIZE = 50  # 帧缓冲区大小
    
    # GPU处理参数
    GPU_MEMORY_FRACTION = 0.8  # GPU内存使用比例
    ENABLE_GPU_BATCH_PROCESSING = True  # 启用GPU批处理
    
    # 性能优化参数
    PROCESSING_TIMEOUT = 2.0  # 处理超时时间（秒）
    ENABLE_PERFORMANCE_MONITORING = True  # 启用性能监控
    PERFORMANCE_LOG_INTERVAL = 100  # 性能日志输出间隔（帧数）
    
    # 检测参数
    DETECTION_INTERVAL = 25  # 检测间隔（帧）
    CONTOUR_THRESHOLD = 400  # 轮廓阈值
    THRESHOLD_FLOW = 1.5     # 光流阈值
    MIN_AREA = 500           # 最小区域面积
    
    # 光流计算参数
    OPTICAL_FLOW_PARAMS = {
        'numLevels': 3,
        'pyrScale': 0.5,
        'fastPyramids': True,
        'winSize': 15,
        'numIters': 3,
        'polyN': 5,
        'polySigma': 1.2,
        'flags': 0
    }
    
    # 形态学处理参数
    MORPHOLOGY_KERNEL_SIZE = (5, 5)
    
    @classmethod
    def get_optimal_workers(cls):
        """根据系统配置获取最优工作线程数"""
        import multiprocessing
        cpu_count = multiprocessing.cpu_count()
        return min(cls.MAX_WORKERS, max(2, cpu_count // 2))
    
    @classmethod
    def get_optimal_batch_size(cls):
        """根据系统配置获取最优批处理大小"""
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        if memory_gb >= 16:
            return 8
        elif memory_gb >= 8:
            return 4
        else:
            return 2
    
    @classmethod
    def auto_configure(cls):
        """自动配置参数"""
        cls.MAX_WORKERS = cls.get_optimal_workers()
        cls.BATCH_SIZE = cls.get_optimal_batch_size()
        print(f"自动配置完成: MAX_WORKERS={cls.MAX_WORKERS}, BATCH_SIZE={cls.BATCH_SIZE}")


# 使用示例
if __name__ == "__main__":
    # 自动配置
    ParallelConfig.auto_configure()
    
    # 打印配置信息
    print("并行处理配置:")
    print(f"最大工作线程数: {ParallelConfig.MAX_WORKERS}")
    print(f"批处理大小: {ParallelConfig.BATCH_SIZE}")
    print(f"帧缓冲区大小: {ParallelConfig.FRAME_BUFFER_SIZE}")
    print(f"GPU内存使用比例: {ParallelConfig.GPU_MEMORY_FRACTION}")
    print(f"处理超时时间: {ParallelConfig.PROCESSING_TIMEOUT}秒")

# 烟雾检测系统使用指南

## 问题解决方案

### 遇到的问题
您遇到了以下错误：
1. `RuntimeWarning: invalid value encountered in sqrt` - 数值计算错误
2. `KeyboardInterrupt` - 程序被中断

### 解决方案
我已经修复了这些问题并提供了三个版本的检测器：

## 三个检测器版本

### 1. 简化检测器 (推荐) - `simple_smoke_detector.py`
**特点:**
- ✅ 高性能，低资源消耗
- ✅ 稳定的算法，避免数值计算错误
- ✅ 简单易用的界面
- ✅ 完善的错误处理

**使用方法:**
```bash
python simple_smoke_detector.py
```

**按键控制:**
- `q` - 退出程序
- `s` - 截图保存
- `空格` - 暂停/继续
- `f` - 全屏/窗口切换

### 2. 优化检测器 - `optimized_smoke_detector.py`
**特点:**
- ✅ 已修复数值计算错误
- ✅ 增强的错误处理
- ✅ 更多检测特征
- ✅ GPU加速支持

**使用方法:**
```bash
python optimized_smoke_detector.py
```

**按键控制:**
- `q` - 退出程序
- `s` - 截图保存
- `p` - 暂停/继续

### 3. 高级检测器 - `advanced_smoke_detector.py`
**特点:**
- ✅ 最完整的特征提取
- ✅ 详细的检测数据记录
- ✅ 机器学习风格的分析
- ⚠️ 资源消耗较高

## 推荐使用顺序

### 第一步：测试简化检测器
```bash
python simple_smoke_detector.py
```
- 选择视频文件 (1.mp4 或 2.mp4)
- 观察检测效果
- 如果运行正常，可以继续使用

### 第二步：如果需要更高精度
```bash
python optimized_smoke_detector.py
```
- 提供更多检测特征
- 更准确的结果
- 适中的性能消耗

### 第三步：如果需要详细分析
```bash
python advanced_smoke_detector.py
```
- 最完整的检测功能
- 详细的数据记录
- 适合研究和分析

## 性能对比

| 特性 | 简化版 | 优化版 | 高级版 |
|------|--------|--------|--------|
| 处理速度 | 最快 | 快 | 中等 |
| 检测精度 | 良好 | 很好 | 最佳 |
| 资源消耗 | 最低 | 中等 | 较高 |
| 稳定性 | 最高 | 高 | 高 |
| 易用性 | 最简单 | 简单 | 复杂 |

## 故障排除

### 如果程序无法启动
1. **检查视频文件**
   ```bash
   # 确保文件存在
   dir 1.mp4
   dir 2.mp4
   ```

2. **检查OpenCV安装**
   ```bash
   python -c "import cv2; print(cv2.__version__)"
   ```

3. **使用摄像头测试**
   - 选择摄像头选项而不是视频文件

### 如果检测效果不好
1. **调整检测参数**
   - 编辑检测器文件中的参数
   - 例如：`brightness_min`, `brightness_max`, `min_area`

2. **检查视频质量**
   - 确保视频清晰度足够
   - 检查光照条件

### 如果程序运行缓慢
1. **使用简化检测器**
   ```bash
   python simple_smoke_detector.py
   ```

2. **调整检测间隔**
   - 修改代码中的 `frame_count % 10` 为更大的数字

3. **降低视频分辨率**
   - 在代码中添加帧缩放

## 检测结果说明

### 检测输出
- **绿色框**: 低置信度检测 (0.3-0.6)
- **橙色框**: 中等置信度检测 (0.6-0.8)
- **红色框**: 高置信度检测 (0.8-1.0)

### 保存的文件
- **检测图像**: `smoke_detection_*.jpg`
- **手动截图**: `manual_capture_*.jpg` 或 `screenshot_*.jpg`
- **检测数据**: `detection_data_*.json` (仅高级版)

## 最佳实践

### 1. 首次使用
```bash
# 推荐从简化版开始
python simple_smoke_detector.py
```

### 2. 生产环境
```bash
# 使用优化版获得最佳平衡
python optimized_smoke_detector.py
```

### 3. 研究分析
```bash
# 使用高级版获得详细数据
python advanced_smoke_detector.py
```

## 技术支持

### 常见问题
1. **Q: 程序启动后立即退出**
   A: 检查视频文件路径和OpenCV安装

2. **Q: 检测不到烟雾**
   A: 调整亮度阈值参数，检查视频内容

3. **Q: 误检太多**
   A: 增加最小面积阈值，使用时间过滤

4. **Q: 程序运行太慢**
   A: 使用简化检测器，增加检测间隔

### 参数调优建议
```python
# 明亮环境
brightness_min = 100
brightness_max = 200

# 昏暗环境
brightness_min = 60
brightness_max = 150

# 减少误检
min_area = 1500
max_area = 20000

# 提高性能
detection_interval = 15  # 每15帧检测一次
```

### 4. 面积警报检测器 (新增) - `area_alarm_smoke_detector.py`
**特点:**
- ✅ 基于面积阈值的智能警报
- ✅ 当检测面积>5cm²时触发警报
- ✅ 播放1.mp3警报音
- ✅ 自动保存警报图像到results文件夹
- ✅ 实时显示面积信息

**使用方法:**
```bash
python area_alarm_smoke_detector.py
```

**按键控制:**
- `q` - 退出程序
- `s` - 手动截图
- `a` - 调整面积阈值
- `空格` - 暂停/继续

**警报功能:**
- 🚨 面积超过5cm²时自动触发警报
- 🔊 播放1.mp3音频文件
- 💾 自动保存警报图像到results文件夹
- 📊 实时显示检测面积和统计信息

## 总结

推荐使用流程：
1. **面积警报检测器** - 专门用于面积阈值警报 (新功能)
2. **简化检测器** - 快速测试和日常使用
3. **优化检测器** - 生产环境和精确检测
4. **高级检测器** - 研究分析和数据收集

所有版本都已修复了您遇到的错误，可以安全使用。

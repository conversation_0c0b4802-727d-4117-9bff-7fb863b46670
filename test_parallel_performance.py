#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并行处理性能测试脚本
Performance Test Script for Parallel Processing
"""

import time
import cv2
import numpy as np
import threading
from concurrent.futures import ThreadPoolExecutor
import matplotlib.pyplot as plt
import psutil
import os

class PerformanceTester:
    """性能测试类"""
    
    def __init__(self):
        self.results = {
            'serial': {'times': [], 'fps': [], 'cpu_usage': [], 'memory_usage': []},
            'parallel': {'times': [], 'fps': [], 'cpu_usage': [], 'memory_usage': []}
        }
    
    def create_test_video_frames(self, num_frames=100, width=640, height=480):
        """创建测试视频帧"""
        frames = []
        for i in range(num_frames):
            # 创建带有运动的测试帧
            frame = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
            # 添加一些运动模式
            cv2.circle(frame, (int(width/2 + 50*np.sin(i*0.1)), int(height/2)), 20, (255, 255, 255), -1)
            frames.append(frame)
        return frames
    
    def process_frame_serial(self, frame, prev_frame):
        """串行处理单帧"""
        if prev_frame is None:
            return None
            
        # 转换为灰度图
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
        
        # 计算光流（使用CPU版本进行对比）
        flow = cv2.calcOpticalFlowPyrLK(prev_gray, gray, None, None)
        
        # 简单的处理逻辑
        magnitude = np.sqrt(flow[0]**2 + flow[1]**2) if flow[0] is not None else np.zeros_like(gray)
        
        return magnitude
    
    def process_frame_batch_parallel(self, frames, prev_frames, max_workers=4):
        """并行批处理帧"""
        def process_single(frame_pair):
            frame, prev_frame = frame_pair
            return self.process_frame_serial(frame, prev_frame)
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            frame_pairs = list(zip(frames, prev_frames))
            results = list(executor.map(process_single, frame_pairs))
        
        return results
    
    def test_serial_processing(self, frames):
        """测试串行处理"""
        print("测试串行处理...")
        start_time = time.time()
        results = []
        
        for i in range(1, len(frames)):
            frame_start = time.time()
            
            # 监控系统资源
            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent
            
            result = self.process_frame_serial(frames[i], frames[i-1])
            results.append(result)
            
            frame_time = time.time() - frame_start
            fps = 1.0 / frame_time if frame_time > 0 else 0
            
            self.results['serial']['times'].append(frame_time)
            self.results['serial']['fps'].append(fps)
            self.results['serial']['cpu_usage'].append(cpu_percent)
            self.results['serial']['memory_usage'].append(memory_percent)
        
        total_time = time.time() - start_time
        avg_fps = len(frames) / total_time
        print(f"串行处理完成: 总时间={total_time:.2f}s, 平均FPS={avg_fps:.2f}")
        
        return results, total_time
    
    def test_parallel_processing(self, frames, batch_size=4, max_workers=4):
        """测试并行处理"""
        print(f"测试并行处理 (批大小={batch_size}, 工作线程={max_workers})...")
        start_time = time.time()
        results = []
        
        # 批处理
        for i in range(0, len(frames) - batch_size, batch_size):
            batch_start = time.time()
            
            # 监控系统资源
            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent
            
            batch_frames = frames[i:i+batch_size]
            batch_prev_frames = frames[i-1:i+batch_size-1] if i > 0 else [frames[0]] + frames[i:i+batch_size-1]
            
            batch_results = self.process_frame_batch_parallel(batch_frames, batch_prev_frames, max_workers)
            results.extend(batch_results)
            
            batch_time = time.time() - batch_start
            fps = batch_size / batch_time if batch_time > 0 else 0
            
            self.results['parallel']['times'].append(batch_time)
            self.results['parallel']['fps'].append(fps)
            self.results['parallel']['cpu_usage'].append(cpu_percent)
            self.results['parallel']['memory_usage'].append(memory_percent)
        
        total_time = time.time() - start_time
        avg_fps = len(frames) / total_time
        print(f"并行处理完成: 总时间={total_time:.2f}s, 平均FPS={avg_fps:.2f}")
        
        return results, total_time
    
    def plot_results(self):
        """绘制性能对比图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('串行 vs 并行处理性能对比', fontsize=16)
        
        # FPS对比
        axes[0, 0].plot(self.results['serial']['fps'], label='串行', color='red')
        axes[0, 0].plot(self.results['parallel']['fps'], label='并行', color='blue')
        axes[0, 0].set_title('FPS对比')
        axes[0, 0].set_ylabel('FPS')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 处理时间对比
        axes[0, 1].plot(self.results['serial']['times'], label='串行', color='red')
        axes[0, 1].plot(self.results['parallel']['times'], label='并行', color='blue')
        axes[0, 1].set_title('处理时间对比')
        axes[0, 1].set_ylabel('时间 (秒)')
        axes[0, 1].legend()
        axes[0, 1].grid(True)
        
        # CPU使用率对比
        axes[1, 0].plot(self.results['serial']['cpu_usage'], label='串行', color='red')
        axes[1, 0].plot(self.results['parallel']['cpu_usage'], label='并行', color='blue')
        axes[1, 0].set_title('CPU使用率对比')
        axes[1, 0].set_ylabel('CPU使用率 (%)')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
        
        # 内存使用率对比
        axes[1, 1].plot(self.results['serial']['memory_usage'], label='串行', color='red')
        axes[1, 1].plot(self.results['parallel']['memory_usage'], label='并行', color='blue')
        axes[1, 1].set_title('内存使用率对比')
        axes[1, 1].set_ylabel('内存使用率 (%)')
        axes[1, 1].legend()
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        plt.savefig('performance_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_report(self):
        """生成性能报告"""
        serial_avg_fps = np.mean(self.results['serial']['fps'])
        parallel_avg_fps = np.mean(self.results['parallel']['fps'])
        
        serial_avg_time = np.mean(self.results['serial']['times'])
        parallel_avg_time = np.mean(self.results['parallel']['times'])
        
        speedup = serial_avg_time / parallel_avg_time if parallel_avg_time > 0 else 0
        fps_improvement = (parallel_avg_fps - serial_avg_fps) / serial_avg_fps * 100 if serial_avg_fps > 0 else 0
        
        report = f"""
=== 并行处理性能测试报告 ===

串行处理:
- 平均FPS: {serial_avg_fps:.2f}
- 平均处理时间: {serial_avg_time:.4f}秒
- 平均CPU使用率: {np.mean(self.results['serial']['cpu_usage']):.1f}%
- 平均内存使用率: {np.mean(self.results['serial']['memory_usage']):.1f}%

并行处理:
- 平均FPS: {parallel_avg_fps:.2f}
- 平均处理时间: {parallel_avg_time:.4f}秒
- 平均CPU使用率: {np.mean(self.results['parallel']['cpu_usage']):.1f}%
- 平均内存使用率: {np.mean(self.results['parallel']['memory_usage']):.1f}%

性能提升:
- 加速比: {speedup:.2f}x
- FPS提升: {fps_improvement:.1f}%

系统信息:
- CPU核心数: {psutil.cpu_count()}
- 物理内存: {psutil.virtual_memory().total / (1024**3):.1f}GB
        """
        
        print(report)
        
        # 保存报告到文件
        with open('performance_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        
        return report


def main():
    """主函数"""
    print("开始并行处理性能测试...")
    
    # 创建测试器
    tester = PerformanceTester()
    
    # 创建测试数据
    print("创建测试视频帧...")
    frames = tester.create_test_video_frames(num_frames=50)  # 减少帧数以加快测试
    
    # 测试串行处理
    serial_results, serial_time = tester.test_serial_processing(frames)
    
    # 测试并行处理
    parallel_results, parallel_time = tester.test_parallel_processing(frames, batch_size=4, max_workers=4)
    
    # 生成报告
    tester.generate_report()
    
    # 绘制对比图
    try:
        tester.plot_results()
    except Exception as e:
        print(f"绘图失败: {e}")
        print("请确保安装了matplotlib: pip install matplotlib")
    
    print("性能测试完成！")


if __name__ == "__main__":
    main()

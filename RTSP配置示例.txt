# 16路RTSP监控相机配置示例
# 格式：每两行为一个通道（第一行是RTSP地址，第二行是通道名称）

# 通道1
rtsp://admin:admin@*************:554/Streaming/Channels/101
监控相机1

# 通道2
rtsp://admin:admin@192.168.1.201:554/Streaming/Channels/101
监控相机2

# 通道3
rtsp://admin:admin@192.168.1.202:554/Streaming/Channels/101
监控相机3

# 通道4
rtsp://admin:admin@192.168.1.203:554/Streaming/Channels/101
监控相机4

# 通道5
rtsp://admin:admin@192.168.1.204:554/Streaming/Channels/101
监控相机5

# 通道6
rtsp://admin:admin@192.168.1.205:554/Streaming/Channels/101
监控相机6

# 通道7
rtsp://admin:admin@192.168.1.206:554/Streaming/Channels/101
监控相机7

# 通道8
rtsp://admin:admin@192.168.1.207:554/Streaming/Channels/101
监控相机8

# 通道9
rtsp://admin:admin@192.168.1.208:554/Streaming/Channels/101
监控相机9

# 通道10
rtsp://admin:admin@192.168.1.209:554/Streaming/Channels/101
监控相机10

# 通道11
rtsp://admin:admin@192.168.1.210:554/Streaming/Channels/101
监控相机11

# 通道12
rtsp://admin:admin@192.168.1.211:554/Streaming/Channels/101
监控相机12

# 通道13
rtsp://admin:admin@*************:554/Streaming/Channels/101
监控相机13

# 通道14
rtsp://admin:admin@*************:554/Streaming/Channels/101
监控相机14

# 通道15
rtsp://admin:admin@*************:554/Streaming/Channels/101
监控相机15

# 通道16
rtsp://admin:admin@*************:554/Streaming/Channels/101
监控相机16

# ========================================
# 常见RTSP地址格式参考
# ========================================

# 海康威视 (Hikvision)
# 主码流: rtsp://用户名:密码@IP地址:554/Streaming/Channels/101
# 子码流: rtsp://用户名:密码@IP地址:554/Streaming/Channels/102
# 示例: rtsp://admin:12345@*************:554/Streaming/Channels/101

# 大华 (Dahua)
# 主码流: rtsp://用户名:密码@IP地址:554/cam/realmonitor?channel=1&subtype=0
# 子码流: rtsp://用户名:密码@IP地址:554/cam/realmonitor?channel=1&subtype=1
# 示例: rtsp://admin:admin@*************:554/cam/realmonitor?channel=1&subtype=0

# 宇视 (Uniview)
# 主码流: rtsp://用户名:密码@IP地址:554/video1
# 子码流: rtsp://用户名:密码@IP地址:554/video2
# 示例: rtsp://admin:123456@*************:554/video1

# 华为 (Huawei)
# 主码流: rtsp://用户名:密码@IP地址:554/LiveMedia/ch1/Media1
# 子码流: rtsp://用户名:密码@IP地址:554/LiveMedia/ch1/Media2
# 示例: rtsp://admin:admin@*************:554/LiveMedia/ch1/Media1

# 天地伟业 (Tiandy)
# 主码流: rtsp://用户名:密码@IP地址:554/stream1
# 子码流: rtsp://用户名:密码@IP地址:554/stream2
# 示例: rtsp://admin:admin@*************:554/stream1

# 萤石云 (Ezviz)
# 格式: rtsp://用户名:密码@IP地址:554/h264/ch1/main/av_stream
# 示例: rtsp://admin:admin@*************:554/h264/ch1/main/av_stream

# ========================================
# 配置说明
# ========================================

# 1. IP地址配置
#    - 将*************-215修改为实际相机IP地址
#    - 确保IP地址在同一网段内
#    - 确保网络连通性

# 2. 认证信息配置
#    - 用户名：通常为admin
#    - 密码：相机的登录密码
#    - 端口：通常为554

# 3. RTSP路径配置
#    - 根据相机品牌选择正确的RTSP路径
#    - 主码流：高清晰度，占用带宽大
#    - 子码流：低清晰度，占用带宽小

# 4. 网络要求
#    - 确保网络带宽充足
#    - 建议使用有线网络连接
#    - 避免网络拥塞

# 5. 相机设置
#    - 启用RTSP服务
#    - 设置正确的编码格式（H.264推荐）
#    - 配置合适的分辨率和帧率

# ========================================
# 故障排除
# ========================================

# 1. 连接失败
#    - 检查IP地址是否正确
#    - 检查用户名密码是否正确
#    - 检查网络连接是否正常
#    - 检查相机RTSP服务是否启用

# 2. 视频卡顿
#    - 降低视频分辨率
#    - 使用子码流
#    - 检查网络带宽
#    - 减少同时连接的通道数

# 3. 频繁断线
#    - 检查网络稳定性
#    - 增加重连间隔时间
#    - 检查相机性能
#    - 优化网络配置

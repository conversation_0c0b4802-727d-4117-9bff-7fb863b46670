#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
16路实时监控烟雾检测系统 - 基于gpu20250514(1)界面效果
16-Channel Real-time Smoke Detection System - Based on gpu20250514(1) UI
"""

import os
import sys
import time
import math
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
import json

# 添加CUDA路径
try:
    os.add_dll_directory(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin')
    os.add_dll_directory(r'E:\JSZK\DICHENG\opencv_contrib_cuda_4.6.0.20221106_win_amd64\install\x64\vc17\bin')
except:
    pass

import cv2
import numpy as np

# 导入pygame用于警报音
try:
    import pygame
    pygame.mixer.init()
    HAS_PYGAME = True
except ImportError:
    HAS_PYGAME = False
    print("⚠️ pygame未安装，将使用系统警报音")

# 导入PyQt5
from PyQt5.QtWidgets import (QApplication, QMainWindow, QLabel, QVBoxLayout, 
                           QHBoxLayout, QPushButton, QWidget, QSizePolicy, 
                           QComboBox, QScrollArea, QGridLayout, QTextEdit, 
                           QMessageBox, QSpacerItem, QSpinBox, QCheckBox,
                           QGroupBox, QTabWidget, QProgressBar, QSlider)
from PyQt5.QtGui import QImage, QPixmap, QFont
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer


class PerformanceMonitor:
    """性能监控类"""
    
    def __init__(self):
        self.frame_count = 0
        self.detection_count = 0
        self.alarm_count = 0
        self.start_time = time.time()
        self.fps = 0
        self.processing_times = []
        
    def update_stats(self, processing_time=None):
        self.frame_count += 1
        if processing_time:
            self.processing_times.append(processing_time)
            if len(self.processing_times) > 100:
                self.processing_times.pop(0)
        
        # 计算FPS
        elapsed = time.time() - self.start_time
        if elapsed > 0:
            self.fps = self.frame_count / elapsed
    
    def get_stats(self):
        avg_processing_time = np.mean(self.processing_times) if self.processing_times else 0
        return {
            'fps': self.fps,
            'frame_count': self.frame_count,
            'detection_count': self.detection_count,
            'alarm_count': self.alarm_count,
            'avg_processing_time': avg_processing_time
        }


class EnhancedVideoThread(QThread):
    """增强的视频处理线程 - 支持面积阈值和时间区域忽略"""
    
    change_pixmap_signal = pyqtSignal(np.ndarray)
    log_signal = pyqtSignal(str)
    alarm_signal = pyqtSignal(int, float)  # 通道号, 面积
    stats_signal = pyqtSignal(dict)  # 性能统计
    
    def __init__(self, video_path, thread_id, thread_name):
        super().__init__()
        self.video_path = video_path
        self.thread_id = thread_id
        self.thread_name = thread_name
        self._run_flag = True
        
        # 视频捕获
        self.cap = cv2.VideoCapture(video_path)
        if not self.cap.isOpened():
            raise ValueError(f"无法打开视频源: {video_path}")
        
        # 获取视频信息
        self.video_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.video_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # 检测参数
        self.area_threshold_cm2 = 5.0  # 面积阈值
        self.area_threshold_pixels = self.calculate_pixel_area_threshold()
        self.brightness_min = 80
        self.brightness_max = 180
        self.min_area = 500
        self.max_area = 30000
        
        # 状态变量
        self.frame_count = 0
        self.prev_gray = None
        self.alarm_triggered = False
        self.stop_detection_flag = False
        self.last_alarm_time = 0
        self.alarm_cooldown = 3.0
        
        # 时间区域设置
        self.time_region = self.calculate_time_region()
        
        # 性能监控
        self.performance_monitor = PerformanceMonitor()
        
        # GPU光流计算器
        try:
            self.gpu_flow = cv2.cuda_FarnebackOpticalFlow.create()
            self.use_gpu = True
        except:
            self.use_gpu = False
        
        # 创建输出目录
        self.output_dir = f'results/channel_{thread_id}'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        
        # 警报音
        self.alarm_sound = None
        if HAS_PYGAME and os.path.exists('1.mp3'):
            try:
                self.alarm_sound = pygame.mixer.Sound('1.mp3')
            except:
                pass
        
        print(f"✅ 通道{thread_id}初始化完成: {thread_name}")
    
    def calculate_pixel_area_threshold(self):
        """计算像素面积阈值"""
        total_area_cm2 = 100 * 60  # 假设覆盖100x60cm
        total_pixels = self.video_width * self.video_height
        pixels_per_cm2 = total_pixels / total_area_cm2
        return int(self.area_threshold_cm2 * pixels_per_cm2)
    
    def calculate_time_region(self):
        """计算时间标记区域"""
        return {
            'x': 0,
            'y': 0,
            'width': int(self.video_width * 0.25),
            'height': int(self.video_height * 0.08)
        }
    
    def mask_time_region(self, mask):
        """屏蔽时间区域"""
        time_region = self.time_region
        mask[time_region['y']:time_region['y'] + time_region['height'],
             time_region['x']:time_region['x'] + time_region['width']] = 0
        return mask
    
    def detect_smoke(self, frame):
        """烟雾检测算法"""
        start_time = time.time()
        
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 亮度过滤
            brightness_mask = cv2.inRange(gray, self.brightness_min, self.brightness_max)
            
            # 运动检测
            motion_mask = np.ones_like(gray) * 255
            if self.prev_gray is not None:
                diff = cv2.absdiff(gray, self.prev_gray)
                motion_mask = cv2.threshold(diff, 20, 255, cv2.THRESH_BINARY)[1]
                
                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
                motion_mask = cv2.morphologyEx(motion_mask, cv2.MORPH_CLOSE, kernel)
            
            # 结合特征
            combined_mask = cv2.bitwise_and(brightness_mask, motion_mask)
            
            # 屏蔽时间区域
            combined_mask = self.mask_time_region(combined_mask)
            
            # 形态学处理
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
            
            # 轮廓检测
            contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 分析轮廓
            smoke_regions = []
            total_area = 0
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if self.min_area < area < self.max_area:
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h
                    if 0.3 < aspect_ratio < 3.0:
                        smoke_regions.append({
                            'bbox': (x, y, w, h),
                            'area': area,
                            'contour': contour
                        })
                        total_area += area
            
            self.prev_gray = gray.copy()
            
            # 更新性能统计
            processing_time = time.time() - start_time
            self.performance_monitor.update_stats(processing_time)
            
            return smoke_regions, total_area
            
        except Exception as e:
            print(f"通道{self.thread_id}检测出错: {e}")
            return [], 0
    
    def check_area_alarm(self, total_area):
        """检查面积警报"""
        current_time = time.time()
        
        if total_area >= self.area_threshold_pixels:
            if current_time - self.last_alarm_time >= self.alarm_cooldown:
                self.trigger_alarm(total_area)
                self.last_alarm_time = current_time
                return True
        return False
    
    def trigger_alarm(self, total_area):
        """触发警报"""
        self.alarm_triggered = True
        self.performance_monitor.alarm_count += 1
        
        area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels
        
        # 发送警报信号
        self.alarm_signal.emit(self.thread_id, area_cm2)
        
        # 记录日志
        log_message = f"通道{self.thread_id}({self.thread_name}) 检测到烟雾! 面积: {area_cm2:.2f}cm²"
        self.log_signal.emit(log_message)
        
        # 播放警报音
        if self.alarm_sound and not self.stop_detection_flag:
            try:
                self.alarm_sound.play()
            except:
                pass
    
    def draw_results(self, frame, smoke_regions, total_area):
        """绘制检测结果"""
        result_frame = frame.copy()
        
        # 计算实际面积
        area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels
        
        # 绘制时间区域标记
        time_region = self.time_region
        cv2.rectangle(result_frame,
                     (time_region['x'], time_region['y']),
                     (time_region['x'] + time_region['width'], 
                      time_region['y'] + time_region['height']),
                     (128, 128, 128), 1)
        
        # 绘制检测区域
        for region in smoke_regions:
            x, y, w, h = region['bbox']
            
            if total_area >= self.area_threshold_pixels:
                color = (0, 0, 255)  # 红色 - 警报
                thickness = 3
            else:
                color = (0, 255, 255)  # 黄色 - 检测
                thickness = 2
            
            cv2.rectangle(result_frame, (x, y), (x + w, y + h), color, thickness)
        
        # 绘制状态信息
        if total_area >= self.area_threshold_pixels:
            status_text = f"ALARM! Area: {area_cm2:.2f}cm²"
            color = (0, 0, 255)
        else:
            status_text = f"Normal - Area: {area_cm2:.2f}cm²"
            color = (0, 255, 0)
        
        cv2.putText(result_frame, status_text, (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        # 绘制通道信息
        channel_text = f"CH{self.thread_id}: {self.thread_name}"
        cv2.putText(result_frame, channel_text, (10, self.video_height - 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        return result_frame
    
    def save_alarm_image(self, frame, total_area):
        """保存警报图像"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels
        
        filename = f"ALARM_CH{self.thread_id}_{timestamp}_area_{area_cm2:.2f}cm2.jpg"
        filepath = os.path.join(self.output_dir, filename)
        
        cv2.imwrite(filepath, frame)
        return filepath
    
    def update_params(self, **kwargs):
        """更新检测参数"""
        if 'area_threshold' in kwargs:
            self.area_threshold_cm2 = kwargs['area_threshold']
            self.area_threshold_pixels = self.calculate_pixel_area_threshold()
        
        if 'brightness_min' in kwargs:
            self.brightness_min = kwargs['brightness_min']
        
        if 'brightness_max' in kwargs:
            self.brightness_max = kwargs['brightness_max']
    
    def run(self):
        """主运行循环"""
        while self._run_flag:
            ret, frame = self.cap.read()
            if not ret:
                # 尝试重新连接
                self.cap.release()
                time.sleep(1)
                self.cap = cv2.VideoCapture(self.video_path)
                continue
            
            self.frame_count += 1
            
            # 每5帧检测一次
            if self.frame_count % 5 == 0 and not self.stop_detection_flag:
                try:
                    smoke_regions, total_area = self.detect_smoke(frame)
                    
                    # 检查警报
                    alarm_triggered = self.check_area_alarm(total_area)
                    
                    # 绘制结果
                    result_frame = self.draw_results(frame, smoke_regions, total_area)
                    
                    # 保存警报图像
                    if alarm_triggered:
                        self.save_alarm_image(result_frame, total_area)
                    
                    # 发送帧信号
                    self.change_pixmap_signal.emit(result_frame)
                    
                    # 发送统计信号
                    if self.frame_count % 100 == 0:
                        stats = self.performance_monitor.get_stats()
                        stats['channel_id'] = self.thread_id
                        self.stats_signal.emit(stats)
                    
                except Exception as e:
                    print(f"通道{self.thread_id}处理出错: {e}")
                    self.change_pixmap_signal.emit(frame)
            else:
                self.change_pixmap_signal.emit(frame)
            
            time.sleep(0.03)  # 约30fps
    
    def stop(self):
        """停止线程"""
        self._run_flag = False
        self.stop_detection_flag = True
        
        if self.alarm_sound and HAS_PYGAME:
            try:
                pygame.mixer.stop()
            except:
                pass
        
        if hasattr(self, 'cap') and self.cap.isOpened():
            self.cap.release()
        
        self.quit()
        self.wait()


class SquareLabel(QLabel):
    """保持16:9比例的视频显示标签"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setMinimumSize(160, 90)
        self.setStyleSheet("border: 1px solid gray;")

    def resizeEvent(self, event):
        super().resizeEvent(event)
        width = self.width()
        height = int(width * 9 / 16)
        self.setFixedHeight(height)


class MultiChannelSmokeDetector(QMainWindow):
    """16路实时监控烟雾检测主窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle('16路实时监控烟雾检测系统 V2.0')
        self.setGeometry(100, 100, 1600, 1000)

        # 系统配置
        self.num_channels = 16
        self.video_threads = [None] * self.num_channels
        self.video_labels = [None] * self.num_channels
        self.channel_labels = [None] * self.num_channels
        self.status_labels = [None] * self.num_channels

        # 性能统计
        self.channel_stats = {}
        self.total_alarms = 0

        # 输出目录
        self.output_dir = 'results'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

        # 初始化pygame
        if HAS_PYGAME:
            pygame.mixer.init()

        # 初始化UI
        self.init_ui()

        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_system_status)
        self.status_timer.start(1000)  # 每秒更新

        print("✅ 16路监控系统初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 标题区域
        self.create_title_section(main_layout)

        # 创建标签页
        tab_widget = QTabWidget()

        # 监控页面
        monitor_tab = QWidget()
        self.create_monitor_tab(monitor_tab)
        tab_widget.addTab(monitor_tab, "实时监控")

        # 控制页面
        control_tab = QWidget()
        self.create_control_tab(control_tab)
        tab_widget.addTab(control_tab, "系统控制")

        # 统计页面
        stats_tab = QWidget()
        self.create_stats_tab(stats_tab)
        tab_widget.addTab(stats_tab, "性能统计")

        main_layout.addWidget(tab_widget)

        # 状态栏
        self.create_status_bar(main_layout)

        # 设置样式
        self.set_styles()

    def create_title_section(self, layout):
        """创建标题区域"""
        title_layout = QHBoxLayout()

        # 标题图片（如果存在）
        if os.path.exists('title.jpg'):
            title_image = QLabel()
            pixmap = QPixmap('title.jpg')
            scaled_pixmap = pixmap.scaled(80, 80, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            title_image.setPixmap(scaled_pixmap)
            title_layout.addWidget(title_image)

        # 标题文字
        title_label = QLabel('16路实时监控烟雾检测系统')
        title_label.setStyleSheet("font-size: 36px; font-weight: bold; color: #2c3e50;")
        title_label.setAlignment(Qt.AlignCenter)

        title_layout.addStretch()
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        layout.addLayout(title_layout)

    def create_monitor_tab(self, tab_widget):
        """创建监控标签页"""
        layout = QVBoxLayout(tab_widget)

        # 视频网格
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        grid_layout = QGridLayout(scroll_widget)

        # 创建16个视频显示区域
        for i in range(self.num_channels):
            # 视频显示标签
            self.video_labels[i] = SquareLabel()
            self.video_labels[i].setAlignment(Qt.AlignCenter)
            self.video_labels[i].setText(f"通道 {i+1}\n未连接")
            self.video_labels[i].setStyleSheet("""
                QLabel {
                    background-color: #34495e;
                    color: white;
                    font-size: 14px;
                    border: 2px solid #7f8c8d;
                }
            """)

            # 通道编号标签
            self.channel_labels[i] = QLabel(f"{i+1}")
            self.channel_labels[i].setAlignment(Qt.AlignCenter)
            self.channel_labels[i].setStyleSheet("""
                QLabel {
                    background-color: #27ae60;
                    color: white;
                    font-size: 18px;
                    font-weight: bold;
                    border-radius: 15px;
                    min-width: 30px;
                    max-width: 30px;
                    min-height: 30px;
                    max-height: 30px;
                }
            """)

            # 状态标签
            self.status_labels[i] = QLabel("离线")
            self.status_labels[i].setAlignment(Qt.AlignCenter)
            self.status_labels[i].setStyleSheet("""
                QLabel {
                    background-color: #e74c3c;
                    color: white;
                    font-size: 12px;
                    padding: 2px;
                    border-radius: 3px;
                }
            """)

            # 布局
            row = i // 4
            col = i % 4

            # 创建单个通道的容器
            channel_widget = QWidget()
            channel_layout = QVBoxLayout(channel_widget)

            # 顶部信息
            top_layout = QHBoxLayout()
            top_layout.addWidget(self.channel_labels[i])
            top_layout.addStretch()
            top_layout.addWidget(self.status_labels[i])

            channel_layout.addLayout(top_layout)
            channel_layout.addWidget(self.video_labels[i])

            grid_layout.addWidget(channel_widget, row, col)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

    def create_control_tab(self, tab_widget):
        """创建控制标签页"""
        layout = QVBoxLayout(tab_widget)

        # 系统控制组
        system_group = QGroupBox("系统控制")
        system_layout = QHBoxLayout(system_group)

        self.start_all_btn = QPushButton("开始全部检测")
        self.start_all_btn.clicked.connect(self.start_all_detection)
        self.start_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 16px;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)

        self.stop_all_btn = QPushButton("停止全部检测")
        self.stop_all_btn.clicked.connect(self.stop_all_detection)
        self.stop_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-size: 16px;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        self.stop_alarm_btn = QPushButton("停止全部警报")
        self.stop_alarm_btn.clicked.connect(self.stop_all_alarms)
        self.stop_alarm_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                font-size: 16px;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)

        system_layout.addWidget(self.start_all_btn)
        system_layout.addWidget(self.stop_all_btn)
        system_layout.addWidget(self.stop_alarm_btn)

        layout.addWidget(system_group)

        # 参数设置组
        param_group = QGroupBox("检测参数设置")
        param_layout = QGridLayout(param_group)

        # 面积阈值
        param_layout.addWidget(QLabel("面积阈值(cm²):"), 0, 0)
        self.area_threshold_spin = QSpinBox()
        self.area_threshold_spin.setRange(1, 50)
        self.area_threshold_spin.setValue(5)
        param_layout.addWidget(self.area_threshold_spin, 0, 1)

        # 亮度范围
        param_layout.addWidget(QLabel("最小亮度:"), 1, 0)
        self.brightness_min_spin = QSpinBox()
        self.brightness_min_spin.setRange(0, 255)
        self.brightness_min_spin.setValue(80)
        param_layout.addWidget(self.brightness_min_spin, 1, 1)

        param_layout.addWidget(QLabel("最大亮度:"), 1, 2)
        self.brightness_max_spin = QSpinBox()
        self.brightness_max_spin.setRange(0, 255)
        self.brightness_max_spin.setValue(180)
        param_layout.addWidget(self.brightness_max_spin, 1, 3)

        # 应用参数按钮
        apply_params_btn = QPushButton("应用参数到所有通道")
        apply_params_btn.clicked.connect(self.apply_parameters)
        param_layout.addWidget(apply_params_btn, 2, 0, 1, 4)

        layout.addWidget(param_group)

        # 单通道控制组
        channel_group = QGroupBox("单通道控制")
        channel_layout = QHBoxLayout(channel_group)

        channel_layout.addWidget(QLabel("选择通道:"))
        self.channel_combo = QComboBox()
        for i in range(self.num_channels):
            self.channel_combo.addItem(f"通道 {i+1}")
        channel_layout.addWidget(self.channel_combo)

        start_single_btn = QPushButton("启动选中通道")
        start_single_btn.clicked.connect(self.start_single_channel)
        channel_layout.addWidget(start_single_btn)

        stop_single_btn = QPushButton("停止选中通道")
        stop_single_btn.clicked.connect(self.stop_single_channel)
        channel_layout.addWidget(stop_single_btn)

        layout.addWidget(channel_group)

        layout.addStretch()

    def create_stats_tab(self, tab_widget):
        """创建统计标签页"""
        layout = QVBoxLayout(tab_widget)

        # 系统统计
        system_stats_group = QGroupBox("系统统计")
        stats_layout = QGridLayout(system_stats_group)

        self.total_frames_label = QLabel("总处理帧数: 0")
        self.total_alarms_label = QLabel("总警报次数: 0")
        self.avg_fps_label = QLabel("平均FPS: 0.0")
        self.active_channels_label = QLabel("活跃通道: 0/16")

        stats_layout.addWidget(self.total_frames_label, 0, 0)
        stats_layout.addWidget(self.total_alarms_label, 0, 1)
        stats_layout.addWidget(self.avg_fps_label, 1, 0)
        stats_layout.addWidget(self.active_channels_label, 1, 1)

        layout.addWidget(system_stats_group)

        # 通道详细统计
        channel_stats_group = QGroupBox("通道详细统计")
        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        self.stats_text.setMaximumHeight(300)

        channel_stats_layout = QVBoxLayout(channel_stats_group)
        channel_stats_layout.addWidget(self.stats_text)

        layout.addWidget(channel_stats_group)

        # 日志显示
        log_group = QGroupBox("系统日志")
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)

        log_layout = QVBoxLayout(log_group)
        log_layout.addWidget(self.log_text)

        layout.addWidget(log_group)

    def create_status_bar(self, layout):
        """创建状态栏"""
        status_layout = QHBoxLayout()

        self.system_status_label = QLabel("系统状态: 就绪")
        self.system_status_label.setStyleSheet("color: #27ae60; font-weight: bold;")

        self.time_label = QLabel()
        self.update_time()

        status_layout.addWidget(self.system_status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.time_label)

        layout.addLayout(status_layout)

    def set_styles(self):
        """设置全局样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ecf0f1;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin: 5px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                font-size: 14px;
                padding: 8px 16px;
                border-radius: 4px;
                border: none;
            }
            QSpinBox, QComboBox {
                font-size: 14px;
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
            }
            QTextEdit {
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
            }
        """)

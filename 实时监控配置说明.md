# 16路实时监控面积警报烟雾检测系统 - 实时监控配置说明

## 系统概述

该系统已经完全支持通过16路RTSP地址实时读取监控相机的视频，并进行实时烟雾检测和面积警报。系统会根据`video_paths.txt`配置文件中的真实监控相机地址进行实时监控。

## 🎯 主要改进

### 1. 智能视频源识别
- ✅ **自动检测RTSP流** - 系统启动时自动分析视频源类型
- ✅ **连接状态显示** - 实时显示每个通道的连接状态
- ✅ **统计信息** - 显示RTSP流、本地视频的数量统计

### 2. 增强的RTSP连接处理
- ✅ **智能重连机制** - 网络断开时自动尝试重新连接
- ✅ **连接参数优化** - 针对RTSP流优化缓冲和FPS设置
- ✅ **错误处理** - 完善的连接失败处理和日志记录
- ✅ **最大重试次数** - 避免无限重连，设置最大尝试次数

### 3. 双模式支持
- ✅ **RTSP实时监控模式** - 连接真实监控相机
- ✅ **Pro1测试模式** - 使用本地视频进行功能测试
- ✅ **一键切换** - 通过界面按钮快速切换模式

## 🚀 使用方法

### 方法1: 直接修改配置文件（推荐）

#### 1. 编辑video_paths.txt文件
将文件内容替换为您的真实监控相机地址：

```
rtsp://admin:password@*************:554/Streaming/Channels/101
监控相机1-大厅
rtsp://admin:password@***********01:554/Streaming/Channels/101
监控相机2-走廊
rtsp://admin:password@***********02:554/Streaming/Channels/101
监控相机3-办公室
...
```

#### 2. 启动系统
```bash
python multi_channel_area_alarm_detector_fixed.py
```

#### 3. 开始检测
点击"开始全部检测"按钮，系统将：
- 自动识别RTSP流
- 显示连接状态
- 开始实时烟雾检测

### 方法2: 使用RTSP模式按钮

#### 1. 启动系统
```bash
python multi_channel_area_alarm_detector_fixed.py
```

#### 2. 点击"RTSP实时监控模式"按钮
系统将：
- 自动生成RTSP配置模板
- 创建video_paths.txt文件

#### 3. 修改配置文件
根据提示修改video_paths.txt中的IP地址和认证信息

#### 4. 开始检测
点击"开始全部检测"启动实时监控

## 📋 配置文件格式

### 标准格式
```
RTSP地址1
通道名称1
RTSP地址2
通道名称2
...
```

### 实际示例
```
rtsp://admin:admin123@*************:554/Streaming/Channels/101
仓库监控-1号位
rtsp://admin:admin123@*************:554/Streaming/Channels/101
仓库监控-2号位
rtsp://admin:admin123@*************:554/Streaming/Channels/101
办公区监控-前台
rtsp://admin:admin123@*************:554/Streaming/Channels/101
办公区监控-会议室
```

## 🔧 RTSP地址配置

### 海康威视 (Hikvision)
```
# 主码流 (高清)
rtsp://用户名:密码@IP地址:554/Streaming/Channels/101

# 子码流 (标清，推荐用于多路监控)
rtsp://用户名:密码@IP地址:554/Streaming/Channels/102

# 实际示例
rtsp://admin:hik12345@*************:554/Streaming/Channels/102
```

### 大华 (Dahua)
```
# 主码流
rtsp://用户名:密码@IP地址:554/cam/realmonitor?channel=1&subtype=0

# 子码流 (推荐)
rtsp://用户名:密码@IP地址:554/cam/realmonitor?channel=1&subtype=1

# 实际示例
rtsp://admin:dh123456@*************:554/cam/realmonitor?channel=1&subtype=1
```

### 宇视 (Uniview)
```
# 主码流
rtsp://用户名:密码@IP地址:554/video1

# 子码流 (推荐)
rtsp://用户名:密码@IP地址:554/video2

# 实际示例
rtsp://admin:uv123456@*************:554/video2
```

## 🌐 网络配置建议

### 1. IP地址规划
```
监控相机网段: *************-199
系统服务器: ************
网关: ***********
```

### 2. 带宽计算
```
单路子码流: 约1-2Mbps
16路总带宽: 约16-32Mbps
建议网络带宽: 50Mbps以上
```

### 3. 网络优化
- 使用千兆交换机
- 优先使用有线网络
- 避免WiFi连接监控相机
- 配置QoS保证监控流量优先级

## 📊 系统状态显示

### 启动时的分析信息
```bash
🔍 分析视频源配置:
   通道 1: RTSP流 - 仓库监控-1号位
   通道 2: RTSP流 - 仓库监控-2号位
   通道 3: RTSP流 - 办公区监控-前台
   ...

📊 视频源统计:
   🌐 RTSP网络流: 16 路
   📹 本地视频: 0 路
   ❓ 其他源: 0 路

🚀 检测到RTSP流，系统将进入实时监控模式
```

### 连接过程信息
```bash
🌐 通道0连接RTSP流: rtsp://admin:***@*************:554/... (尝试 1/5)
✅ 通道0RTSP流连接成功: 1920x1080
🌐 通道1连接RTSP流: rtsp://admin:***@*************:554/... (尝试 1/5)
✅ 通道1RTSP流连接成功: 1920x1080
```

### 运行时状态
```bash
🔄 通道2RTSP流断开，尝试重新连接...
✅ 通道2重新连接成功
❌ 通道3达到最大重连次数，停止尝试
```

## 🛠️ 故障排除

### 1. 连接失败问题

#### 问题现象
```
⚠️ 通道0无法打开视频源
⚠️ 通道0连接异常: [Errno 110] Connection timed out
```

#### 解决方法
1. **检查网络连通性**
   ```bash
   ping *************
   telnet ************* 554
   ```

2. **验证RTSP地址**
   - 使用VLC播放器测试RTSP地址
   - 检查用户名密码是否正确
   - 确认RTSP路径是否正确

3. **检查相机设置**
   - 确保RTSP服务已启用
   - 检查最大连接数限制
   - 验证编码格式设置

### 2. 视频卡顿问题

#### 解决方法
1. **降低视频质量**
   - 使用子码流而非主码流
   - 降低分辨率设置
   - 减少帧率设置

2. **优化网络**
   - 检查网络带宽使用情况
   - 使用有线网络连接
   - 配置QoS优先级

3. **系统优化**
   - 增加检测间隔 `detection_interval`
   - 减少同时运行的通道数
   - 优化系统性能

### 3. 频繁断线问题

#### 解决方法
1. **网络稳定性**
   - 检查网络设备状态
   - 更换网线或交换机端口
   - 检查网络拥塞情况

2. **相机性能**
   - 检查相机负载情况
   - 重启相机设备
   - 更新相机固件

3. **系统参数调整**
   ```python
   self.reconnect_delay = 5.0  # 增加重连间隔
   self.max_connection_attempts = 10  # 增加最大重试次数
   ```

## 🔍 测试和验证

### 1. 单路测试
```python
# 测试单个RTSP地址
import cv2
cap = cv2.VideoCapture("rtsp://admin:password@*************:554/Streaming/Channels/102")
if cap.isOpened():
    ret, frame = cap.read()
    if ret:
        print(f"连接成功，视频尺寸: {frame.shape}")
    cap.release()
else:
    print("连接失败")
```

### 2. 网络测试
```bash
# 测试网络延迟
ping -c 10 *************

# 测试端口连通性
telnet ************* 554

# 测试带宽
iperf3 -c *************
```

### 3. 功能验证清单
- [ ] 所有16路RTSP流正常连接
- [ ] 视频画面清晰流畅
- [ ] 烟雾检测功能正常
- [ ] 面积警报功能正常
- [ ] 不重复报警功能正常
- [ ] 断线重连功能正常
- [ ] 警报音播放正常
- [ ] 图像保存功能正常

## 📈 性能优化建议

### 1. 硬件配置
- **CPU**: Intel i5以上或同等性能
- **内存**: 8GB以上
- **网卡**: 千兆网卡
- **存储**: SSD硬盘

### 2. 软件优化
```python
# 在AreaAlarmVideoThread中调整参数
self.detection_interval = 3  # 增加检测间隔
self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减少缓冲
self.cap.set(cv2.CAP_PROP_FPS, 15)  # 降低FPS
```

### 3. 系统配置
- 关闭不必要的后台程序
- 优化网络设置
- 配置防火墙规则
- 设置系统优先级

## 🎯 总结

现在系统已经完全支持实时RTSP监控：

✅ **完整的RTSP支持** - 支持主流监控相机品牌  
✅ **智能连接管理** - 自动重连和错误处理  
✅ **双模式切换** - RTSP实时监控 + Pro1测试模式  
✅ **完整检测功能** - 所有烟雾检测功能完全保持  
✅ **用户友好界面** - 直观的状态显示和操作按钮  
✅ **详细日志记录** - 完整的连接和运行日志  

只需要将`video_paths.txt`中的地址替换为您的真实监控相机RTSP地址，系统就能实现完整的16路实时监控烟雾检测功能！

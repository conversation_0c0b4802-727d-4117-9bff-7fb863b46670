#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证画面显示问题修复
"""

import os
import sys
import cv2

def test_video_connection():
    """测试视频连接"""
    print("🔍 快速测试视频连接...")
    
    # 读取配置文件
    try:
        with open('video_paths.txt', 'r', encoding='utf-8') as file:
            lines = [line.strip() for line in file.readlines() if line.strip()]
        print(f"✅ 配置文件读取成功: {len(lines)}行")
    except Exception as e:
        print(f"❌ 配置文件读取失败: {e}")
        return False
    
    # 测试前2个通道
    test_channels = min(2, len(lines) // 2)
    success_count = 0
    
    for i in range(test_channels):
        video_path = lines[i * 2]
        channel_name = lines[i * 2 + 1]
        
        print(f"\n📹 测试通道{i+1}: {channel_name}")
        print(f"   路径: {video_path}")
        
        # 测试连接
        cap = cv2.VideoCapture(video_path)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret and frame is not None:
                h, w = frame.shape[:2]
                print(f"   ✅ 连接成功，分辨率: {w}x{h}")
                success_count += 1
            else:
                print(f"   ❌ 无法读取帧")
            cap.release()
        else:
            print(f"   ❌ 无法打开视频源")
    
    print(f"\n📊 测试结果: {success_count}/{test_channels} 个通道连接成功")
    return success_count > 0

def test_thread_creation():
    """测试线程创建"""
    print("\n🧵 测试线程创建...")
    
    try:
        # 导入必要模块
        sys.path.append('.')
        from multi_channel_area_alarm_detector_fixed import AreaAlarmVideoThread
        
        # 测试创建线程
        video_path = "1.mp4" if os.path.exists("1.mp4") else "test_video.mp4"
        
        print(f"🔄 创建测试线程: {video_path}")
        thread = AreaAlarmVideoThread(video_path, 0, "测试通道")
        
        if thread:
            print("✅ 线程创建成功")
            
            # 测试线程属性
            print(f"   视频宽度: {thread.video_width}")
            print(f"   视频高度: {thread.video_height}")
            print(f"   是否本地视频: {thread.is_local_video}")
            
            # 清理
            thread.stop()
            return True
        else:
            print("❌ 线程创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 线程创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_dependencies():
    """检查依赖"""
    print("\n📦 检查依赖...")
    
    dependencies = {
        'cv2': 'OpenCV',
        'PyQt5': 'PyQt5',
        'numpy': 'NumPy'
    }
    
    missing = []
    for module, name in dependencies.items():
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except ImportError:
            print(f"   ❌ {name} 未安装")
            missing.append(name)
    
    if missing:
        print(f"\n⚠️ 缺少依赖: {', '.join(missing)}")
        return False
    else:
        print("✅ 所有依赖都已安装")
        return True

def main():
    """主函数"""
    print("🚀 16路监控系统快速测试")
    print("=" * 50)
    
    # 1. 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，请安装缺少的依赖")
        return
    
    # 2. 测试视频连接
    if not test_video_connection():
        print("❌ 视频连接测试失败")
        return
    
    # 3. 测试线程创建
    if not test_thread_creation():
        print("❌ 线程创建测试失败")
        return
    
    print("\n✅ 所有测试通过！")
    print("📋 建议:")
    print("   1. 启动主程序: python multi_channel_area_alarm_detector_fixed.py")
    print("   2. 点击'开始全部检测'按钮")
    print("   3. 观察控制台输出和界面显示")
    
    # 询问是否启动主程序
    try:
        choice = input("\n是否现在启动主程序? (y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            print("🚀 启动主程序...")
            os.system('python multi_channel_area_alarm_detector_fixed.py')
    except KeyboardInterrupt:
        print("\n测试结束")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化烟雾检测器 - 高性能版本
Simple Smoke Detector - High Performance Version
"""

import os
import cv2
import numpy as np
from datetime import datetime
import time

# 添加CUDA路径
try:
    os.add_dll_directory(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin')
    os.add_dll_directory(r'E:\JSZK\DICHENG\opencv_contrib_cuda_4.6.0.20221106_win_amd64\install\x64\vc17\bin')
except:
    pass

class SimpleSmokeDetector:
    """简化的高性能烟雾检测器"""
    
    def __init__(self, video_source):
        self.video_source = video_source
        self.cap = cv2.VideoCapture(video_source)
        
        if not self.cap.isOpened():
            raise ValueError(f"无法打开视频源: {video_source}")
        
        # 简化的检测参数
        self.brightness_min = 80
        self.brightness_max = 180
        self.min_area = 1000
        self.max_area = 25000
        
        # 状态变量
        self.frame_count = 0
        self.detection_count = 0
        self.prev_gray = None
        
        # 创建输出目录
        self.output_dir = 'simple_smoke_results'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            
        print(f"✅ 简化烟雾检测器初始化完成")
        print(f"   视频源: {video_source}")
        
    def detect_smoke(self, frame):
        """简化的烟雾检测算法"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            h, w = gray.shape
            
            # 1. 亮度过滤
            brightness_mask = cv2.inRange(gray, self.brightness_min, self.brightness_max)
            
            # 2. 运动检测 (如果有前一帧)
            motion_mask = np.ones_like(gray) * 255
            if self.prev_gray is not None:
                # 简单的帧差法
                diff = cv2.absdiff(gray, self.prev_gray)
                motion_mask = cv2.threshold(diff, 20, 255, cv2.THRESH_BINARY)[1]
                
                # 简单的形态学处理
                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
                motion_mask = cv2.morphologyEx(motion_mask, cv2.MORPH_CLOSE, kernel)
            
            # 3. 结合亮度和运动
            combined_mask = cv2.bitwise_and(brightness_mask, motion_mask)
            
            # 4. 再次形态学处理
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
            
            # 5. 轮廓检测
            contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 6. 过滤轮廓
            smoke_regions = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if self.min_area < area < self.max_area:
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # 简单的形状过滤
                    aspect_ratio = w / h
                    if 0.3 < aspect_ratio < 3.0:
                        # 简单的置信度计算
                        confidence = min(area / 10000, 1.0)  # 基于面积的简单置信度
                        
                        smoke_regions.append({
                            'bbox': (x, y, w, h),
                            'area': area,
                            'confidence': confidence,
                            'contour': contour
                        })
            
            self.prev_gray = gray.copy()
            return smoke_regions, combined_mask
            
        except Exception as e:
            print(f"⚠️ 检测出错: {e}")
            return [], np.zeros_like(frame[:,:,0])
    
    def draw_results(self, frame, smoke_regions):
        """绘制检测结果"""
        result_frame = frame.copy()
        
        for region in smoke_regions:
            x, y, w, h = region['bbox']
            confidence = region['confidence']
            
            # 根据置信度选择颜色
            if confidence > 0.7:
                color = (0, 0, 255)  # 红色
                thickness = 3
            elif confidence > 0.5:
                color = (0, 165, 255)  # 橙色
                thickness = 2
            else:
                color = (0, 255, 255)  # 黄色
                thickness = 2
            
            # 绘制边界框
            cv2.rectangle(result_frame, (x, y), (x + w, y + h), color, thickness)
            
            # 绘制标签
            label = f"SMOKE {confidence:.2f}"
            cv2.putText(result_frame, label, (x, y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        # 状态信息
        status = f"Frame: {self.frame_count} | Detections: {len(smoke_regions)}"
        cv2.putText(result_frame, status, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        return result_frame
    
    def save_detection(self, frame, smoke_regions):
        """保存检测结果"""
        if smoke_regions:
            self.detection_count += 1
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            filename = f"smoke_{timestamp}_frame_{self.frame_count}.jpg"
            filepath = os.path.join(self.output_dir, filename)
            cv2.imwrite(filepath, frame)
            
            max_confidence = max(r['confidence'] for r in smoke_regions)
            print(f"🔥 检测到烟雾! 帧:{self.frame_count}, 置信度:{max_confidence:.2f}")
    
    def run(self):
        """运行检测"""
        print("🚀 开始简化烟雾检测...")
        print("按键说明:")
        print("  'q' - 退出")
        print("  's' - 截图")
        print("  'f' - 切换全屏")
        print("  空格 - 暂停/继续")
        
        start_time = time.time()
        paused = False
        fullscreen = False
        
        try:
            while True:
                if not paused:
                    ret, frame = self.cap.read()
                    if not ret:
                        print("📹 视频结束")
                        break
                    
                    self.frame_count += 1
                    
                    # 每10帧检测一次 (提高性能)
                    if self.frame_count % 10 == 0:
                        try:
                            # 检测烟雾
                            smoke_regions, mask = self.detect_smoke(frame)
                            
                            # 绘制结果
                            result_frame = self.draw_results(frame, smoke_regions)
                            
                            # 保存检测结果
                            if smoke_regions:
                                self.save_detection(result_frame, smoke_regions)
                            
                            # 显示结果
                            cv2.imshow('Simple Smoke Detection', result_frame)
                            
                            # 显示掩码 (可选)
                            if len(smoke_regions) > 0:
                                cv2.imshow('Detection Mask', mask)
                            
                        except Exception as e:
                            print(f"⚠️ 处理帧 {self.frame_count} 时出错: {e}")
                            cv2.imshow('Simple Smoke Detection', frame)
                    else:
                        # 只显示原始帧
                        cv2.imshow('Simple Smoke Detection', frame)
                
                # 键盘控制
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    print("🛑 用户退出")
                    break
                elif key == ord('s'):
                    # 截图
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    filename = f"screenshot_{timestamp}.jpg"
                    cv2.imwrite(os.path.join(self.output_dir, filename), frame)
                    print(f"📸 截图保存: {filename}")
                elif key == ord(' '):
                    # 暂停/继续
                    paused = not paused
                    print(f"⏸️ {'暂停' if paused else '继续'}")
                elif key == ord('f'):
                    # 切换全屏
                    fullscreen = not fullscreen
                    if fullscreen:
                        cv2.setWindowProperty('Simple Smoke Detection', cv2.WND_PROP_FULLSCREEN, cv2.WINDOW_FULLSCREEN)
                    else:
                        cv2.setWindowProperty('Simple Smoke Detection', cv2.WND_PROP_FULLSCREEN, cv2.WINDOW_NORMAL)
                    print(f"🖥️ {'全屏' if fullscreen else '窗口'}模式")
                
        except KeyboardInterrupt:
            print("\n🛑 检测被用户中断")
        except Exception as e:
            print(f"\n❌ 运行时错误: {e}")
        finally:
            self.cleanup(start_time)
    
    def cleanup(self, start_time):
        """清理资源并显示统计"""
        elapsed_time = time.time() - start_time
        fps = self.frame_count / elapsed_time if elapsed_time > 0 else 0
        
        print(f"\n📊 检测统计:")
        print(f"  - 总帧数: {self.frame_count}")
        print(f"  - 检测次数: {self.detection_count}")
        print(f"  - 平均FPS: {fps:.2f}")
        print(f"  - 运行时间: {elapsed_time:.2f}秒")
        print(f"  - 检测率: {(self.detection_count/max(self.frame_count//10, 1)*100):.1f}%")
        
        self.cap.release()
        cv2.destroyAllWindows()
        print("✅ 检测完成")


def main():
    """主函数"""
    print("🔥 简化烟雾检测系统")
    print("=" * 40)
    
    # 检查视频文件
    video_files = ['1.mp4', '2.mp4']
    available_files = [f for f in video_files if os.path.exists(f)]
    
    if available_files:
        print("📁 可用视频文件:")
        for i, file in enumerate(available_files, 1):
            print(f"  {i}. {file}")
        print(f"  {len(available_files) + 1}. 摄像头")
        
        try:
            choice = int(input(f"请选择 (1-{len(available_files) + 1}): "))
            if 1 <= choice <= len(available_files):
                video_source = available_files[choice - 1]
            else:
                video_source = 0
        except:
            video_source = available_files[0]
            print(f"使用默认视频: {video_source}")
    else:
        print("❌ 未找到视频文件")
        video_source = input("请输入视频路径 (或按回车使用摄像头): ").strip()
        if not video_source:
            video_source = 0
    
    try:
        detector = SimpleSmokeDetector(video_source)
        detector.run()
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("💡 建议:")
        print("  1. 检查视频文件是否存在")
        print("  2. 检查OpenCV是否正确安装")
        print("  3. 尝试使用摄像头 (选择摄像头选项)")


if __name__ == "__main__":
    main()

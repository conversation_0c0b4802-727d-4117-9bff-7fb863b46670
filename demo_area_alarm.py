#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
面积警报检测器演示脚本
Demo script for area alarm detector
"""

import os
import sys

def check_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    # 检查视频文件
    video_files = ['1.mp4', '2.mp4']
    available_videos = [f for f in video_files if os.path.exists(f)]
    
    print(f"📹 视频文件: {len(available_videos)}/2 可用")
    for video in available_videos:
        size_mb = os.path.getsize(video) / (1024*1024)
        print(f"   ✅ {video} ({size_mb:.1f}MB)")
    
    # 检查警报音文件
    if os.path.exists('1.mp3'):
        size_kb = os.path.getsize('1.mp3') / 1024
        print(f"🔊 警报音: ✅ 1.mp3 ({size_kb:.1f}KB)")
    else:
        print("🔊 警报音: ❌ 1.mp3 未找到")
    
    # 检查results文件夹
    if os.path.exists('results'):
        print("📁 结果文件夹: ✅ results 已存在")
    else:
        print("📁 结果文件夹: ⚠️ results 将自动创建")
    
    # 检查Python包
    try:
        # 添加CUDA路径
        try:
            os.add_dll_directory(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin')
            os.add_dll_directory(r'E:\JSZK\DICHENG\opencv_contrib_cuda_4.6.0.20221106_win_amd64\install\x64\vc17\bin')
        except:
            pass

        import cv2
        print(f"📦 OpenCV: ✅ {cv2.__version__}")
    except ImportError:
        print("📦 OpenCV: ❌ 未安装")
        return False
    
    try:
        import numpy
        print(f"📦 NumPy: ✅ {numpy.__version__}")
    except ImportError:
        print("📦 NumPy: ❌ 未安装")
        return False
    
    try:
        import pygame
        print(f"📦 Pygame: ✅ {pygame.version.ver}")
    except ImportError:
        print("📦 Pygame: ⚠️ 未安装 (将使用系统警报音)")
    
    return len(available_videos) > 0

def show_features():
    """显示功能特点"""
    print("\n🚨 面积警报烟雾检测器功能:")
    print("=" * 50)
    print("✅ 智能烟雾检测算法")
    print("✅ 基于面积阈值的警报系统")
    print("✅ 当检测面积 > 5平方厘米时触发警报")
    print("✅ 播放1.mp3警报音文件")
    print("✅ 自动保存警报图像到results文件夹")
    print("✅ 实时显示检测面积和统计信息")
    print("✅ 支持手动调整面积阈值")
    print("✅ 支持暂停/继续功能")
    print("✅ 支持手动截图功能")

def show_usage():
    """显示使用说明"""
    print("\n📖 使用说明:")
    print("=" * 50)
    print("1. 运行程序:")
    print("   python area_alarm_smoke_detector.py")
    print()
    print("2. 选择视频源:")
    print("   - 选择1.mp4或2.mp4")
    print("   - 或使用摄像头")
    print()
    print("3. 按键控制:")
    print("   q     - 退出程序")
    print("   s     - 手动截图")
    print("   a     - 调整面积阈值")
    print("   空格  - 暂停/继续")
    print()
    print("4. 警报触发:")
    print("   - 当检测到的烟雾总面积 > 5cm²时")
    print("   - 自动播放1.mp3警报音")
    print("   - 自动保存警报图像到results文件夹")
    print("   - 屏幕显示红色警报信息")

def show_area_calculation():
    """显示面积计算说明"""
    print("\n📐 面积计算说明:")
    print("=" * 50)
    print("视频分辨率: 2304×1296 像素")
    print("假设覆盖区域: 100cm × 60cm = 6000cm²")
    print("像素密度: 约 497 像素/cm²")
    print("5cm² 阈值 ≈ 2485 像素")
    print()
    print("注意: 实际应用中需要根据摄像头安装")
    print("距离和角度调整面积换算比例")

def run_demo():
    """运行演示"""
    print("🎬 面积警报烟雾检测器演示")
    print("=" * 50)
    
    if not check_requirements():
        print("\n❌ 系统要求检查失败")
        print("请确保:")
        print("1. 安装了OpenCV: pip install opencv-python")
        print("2. 安装了NumPy: pip install numpy")
        print("3. 存在视频文件: 1.mp4 或 2.mp4")
        return
    
    show_features()
    show_usage()
    show_area_calculation()
    
    print("\n🚀 准备启动检测器...")
    
    # 询问是否启动
    try:
        choice = input("\n是否现在启动面积警报检测器? (y/n): ").strip().lower()
        if choice in ['y', 'yes', '是', '启动']:
            print("正在启动...")
            try:
                import area_alarm_smoke_detector
                area_alarm_smoke_detector.main()
            except ImportError as e:
                print(f"❌ 导入失败: {e}")
                print("请确保area_alarm_smoke_detector.py文件存在")
            except Exception as e:
                print(f"❌ 运行失败: {e}")
        else:
            print("演示结束")
    except KeyboardInterrupt:
        print("\n演示被中断")

def main():
    """主函数"""
    try:
        run_demo()
    except Exception as e:
        print(f"❌ 演示出错: {e}")
    
    print("\n📋 文件清单:")
    print("- area_alarm_smoke_detector.py  # 主检测程序")
    print("- demo_area_alarm.py           # 演示脚本")
    print("- 使用指南.md                  # 详细使用说明")
    print("- 1.mp3                        # 警报音文件")
    print("- 1.mp4, 2.mp4                 # 测试视频")
    print("- results/                     # 警报图像保存文件夹")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级烟雾检测算法 - 基于1.mp4和2.mp4视频分析结果
Advanced Smoke Detection Algorithm - Based on analysis of 1.mp4 and 2.mp4
"""

import os
import sys
import time
import cv2
import numpy as np
from datetime import datetime
import threading
from concurrent.futures import ThreadPoolExecutor
import json
import logging

# 添加CUDA路径
try:
    os.add_dll_directory(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin')
    os.add_dll_directory(r'E:\JSZK\DICHENG\opencv_contrib_cuda_4.6.0.20221106_win_amd64\install\x64\vc17\bin')
except:
    pass

class SmokeDetectionConfig:
    """烟雾检测配置类"""
    
    # 基于视频分析的优化参数
    BRIGHTNESS_RANGE = (80, 180)  # 烟雾亮度范围
    MOTION_THRESHOLD = 5.0        # 运动阈值
    EDGE_DENSITY_THRESHOLD = 0.02 # 边缘密度阈值
    
    # 烟雾特征参数
    SMOKE_MIN_AREA = 500          # 最小烟雾区域
    SMOKE_MAX_AREA = 50000        # 最大烟雾区域
    OPACITY_THRESHOLD = 0.3       # 透明度阈值
    TEMPORAL_CONSISTENCY = 3      # 时间一致性帧数
    
    # 光流参数
    OPTICAL_FLOW_THRESHOLD = 1.5
    FLOW_MAGNITUDE_THRESHOLD = 2.0
    
    # 形态学参数
    MORPH_KERNEL_SIZE = (7, 7)
    GAUSSIAN_BLUR_SIZE = (15, 15)
    
    # 检测参数
    DETECTION_INTERVAL = 5        # 每5帧检测一次
    CONFIDENCE_THRESHOLD = 0.7    # 置信度阈值


class SmokeFeatureExtractor:
    """烟雾特征提取器"""
    
    def __init__(self):
        self.config = SmokeDetectionConfig()
        
    def extract_texture_features(self, region):
        """提取纹理特征"""
        # 计算局部二值模式 (LBP)
        gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY) if len(region.shape) == 3 else region
        
        # 计算梯度
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        # 计算纹理特征
        features = {
            'mean_gradient': np.mean(gradient_magnitude),
            'std_gradient': np.std(gradient_magnitude),
            'gradient_uniformity': np.std(gradient_magnitude) / (np.mean(gradient_magnitude) + 1e-6),
            'edge_density': np.sum(gradient_magnitude > 30) / gradient_magnitude.size
        }
        
        return features
    
    def extract_motion_features(self, current_frame, previous_frame):
        """提取运动特征"""
        if previous_frame is None:
            return {'motion_magnitude': 0, 'motion_direction': 0, 'motion_consistency': 0}
            
        # 计算光流
        gray_current = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)
        gray_previous = cv2.cvtColor(previous_frame, cv2.COLOR_BGR2GRAY)
        
        # 使用Farneback光流
        flow = cv2.calcOpticalFlowPyrLK(gray_previous, gray_current, None, None)
        
        if flow[0] is not None:
            # 计算运动幅度和方向
            magnitude, angle = cv2.cartToPolar(flow[0][:, :, 0], flow[0][:, :, 1])
            
            features = {
                'motion_magnitude': np.mean(magnitude),
                'motion_direction': np.mean(angle),
                'motion_consistency': np.std(angle)
            }
        else:
            features = {'motion_magnitude': 0, 'motion_direction': 0, 'motion_consistency': 0}
            
        return features
    
    def extract_color_features(self, region):
        """提取颜色特征"""
        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(region, cv2.COLOR_BGR2HSV)
        
        # 计算颜色直方图
        hist_h = cv2.calcHist([hsv], [0], None, [180], [0, 180])
        hist_s = cv2.calcHist([hsv], [1], None, [256], [0, 256])
        hist_v = cv2.calcHist([hsv], [2], None, [256], [0, 256])
        
        # 计算颜色特征
        features = {
            'mean_hue': np.mean(hsv[:, :, 0]),
            'mean_saturation': np.mean(hsv[:, :, 1]),
            'mean_value': np.mean(hsv[:, :, 2]),
            'saturation_variance': np.var(hsv[:, :, 1]),
            'hue_uniformity': np.std(hsv[:, :, 0]),
            'color_entropy': -np.sum(hist_v * np.log(hist_v + 1e-6))
        }
        
        return features


class AdvancedSmokeDetector:
    """高级烟雾检测器"""
    
    def __init__(self, video_source):
        self.video_source = video_source
        self.config = SmokeDetectionConfig()
        self.feature_extractor = SmokeFeatureExtractor()
        
        # 初始化视频捕获
        self.cap = cv2.VideoCapture(video_source)
        if not self.cap.isOpened():
            raise ValueError(f"无法打开视频源: {video_source}")
            
        # 初始化检测状态
        self.previous_frame = None
        self.frame_count = 0
        self.smoke_history = []
        self.detection_results = []
        
        # 初始化GPU光流计算器
        try:
            self.gpu_flow = cv2.cuda_FarnebackOpticalFlow.create()
            self.use_gpu = True
            print("GPU加速已启用")
        except:
            self.use_gpu = False
            print("使用CPU计算")
            
        # 创建输出目录
        self.output_dir = 'smoke_detection_results'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('smoke_detection.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def preprocess_frame(self, frame):
        """预处理帧"""
        # 高斯模糊去噪
        blurred = cv2.GaussianBlur(frame, self.config.GAUSSIAN_BLUR_SIZE, 0)
        
        # 直方图均衡化增强对比度
        gray = cv2.cvtColor(blurred, cv2.COLOR_BGR2GRAY)
        enhanced = cv2.equalizeHist(gray)
        enhanced_bgr = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)
        
        return enhanced_bgr
    
    def detect_potential_smoke_regions(self, frame):
        """检测潜在烟雾区域"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 1. 亮度过滤 - 基于分析结果优化
        brightness_mask = cv2.inRange(gray, 
                                    self.config.BRIGHTNESS_RANGE[0], 
                                    self.config.BRIGHTNESS_RANGE[1])
        
        # 2. 边缘检测 - 烟雾边缘模糊
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / edges.size
        
        # 3. 纹理分析 - 烟雾纹理不规则
        kernel = np.ones(self.config.MORPH_KERNEL_SIZE, np.uint8)
        texture_mask = cv2.morphologyEx(brightness_mask, cv2.MORPH_CLOSE, kernel)
        
        # 4. 运动分析
        motion_mask = np.zeros_like(gray)
        if self.previous_frame is not None:
            motion_features = self.feature_extractor.extract_motion_features(frame, self.previous_frame)
            if motion_features['motion_magnitude'] > self.config.MOTION_THRESHOLD:
                # 计算帧差
                diff = cv2.absdiff(gray, cv2.cvtColor(self.previous_frame, cv2.COLOR_BGR2GRAY))
                motion_mask = cv2.threshold(diff, 25, 255, cv2.THRESH_BINARY)[1]
        
        # 5. 结合所有掩码
        combined_mask = cv2.bitwise_and(texture_mask, motion_mask)
        
        # 6. 形态学操作优化
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
        
        return combined_mask, edge_density
    
    def analyze_smoke_candidates(self, frame, mask):
        """分析烟雾候选区域"""
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        smoke_regions = []
        
        for contour in contours:
            area = cv2.contourArea(contour)
            
            # 面积过滤
            if area < self.config.SMOKE_MIN_AREA or area > self.config.SMOKE_MAX_AREA:
                continue
                
            # 获取边界框
            x, y, w, h = cv2.boundingRect(contour)
            roi = frame[y:y+h, x:x+w]
            
            if roi.size == 0:
                continue
                
            # 提取特征
            texture_features = self.feature_extractor.extract_texture_features(roi)
            color_features = self.feature_extractor.extract_color_features(roi)
            motion_features = self.feature_extractor.extract_motion_features(
                frame, self.previous_frame) if self.previous_frame is not None else {}
            
            # 计算烟雾置信度
            confidence = self.calculate_smoke_confidence(
                texture_features, color_features, motion_features, area)
            
            if confidence > self.config.CONFIDENCE_THRESHOLD:
                smoke_regions.append({
                    'contour': contour,
                    'bbox': (x, y, w, h),
                    'area': area,
                    'confidence': confidence,
                    'features': {
                        'texture': texture_features,
                        'color': color_features,
                        'motion': motion_features
                    }
                })
                
        return smoke_regions
    
    def calculate_smoke_confidence(self, texture_features, color_features, motion_features, area):
        """计算烟雾置信度"""
        confidence = 0.0
        
        # 纹理特征评分 (30%)
        if texture_features['gradient_uniformity'] > 0.5:  # 烟雾纹理不均匀
            confidence += 0.3
            
        # 颜色特征评分 (25%)
        if (color_features['saturation_variance'] < 500 and  # 饱和度低
            color_features['mean_value'] > 80):  # 亮度适中
            confidence += 0.25
            
        # 运动特征评分 (25%)
        if motion_features and motion_features['motion_magnitude'] > 2:
            confidence += 0.25
            
        # 形状特征评分 (20%)
        if 1000 < area < 20000:  # 合理的面积范围
            confidence += 0.2
            
        return min(confidence, 1.0)
    
    def temporal_consistency_check(self, current_detections):
        """时间一致性检查"""
        self.smoke_history.append(current_detections)
        
        # 保持历史记录长度
        if len(self.smoke_history) > self.config.TEMPORAL_CONSISTENCY:
            self.smoke_history.pop(0)
            
        # 检查时间一致性
        if len(self.smoke_history) >= self.config.TEMPORAL_CONSISTENCY:
            consistent_detections = []
            for detection in current_detections:
                consistency_count = 0
                for hist_detections in self.smoke_history[-self.config.TEMPORAL_CONSISTENCY:]:
                    for hist_detection in hist_detections:
                        # 检查位置相似性
                        if self.calculate_bbox_overlap(detection['bbox'], hist_detection['bbox']) > 0.3:
                            consistency_count += 1
                            break
                            
                if consistency_count >= self.config.TEMPORAL_CONSISTENCY - 1:
                    consistent_detections.append(detection)
                    
            return consistent_detections
        
        return current_detections
    
    def calculate_bbox_overlap(self, bbox1, bbox2):
        """计算边界框重叠率"""
        x1, y1, w1, h1 = bbox1
        x2, y2, w2, h2 = bbox2
        
        # 计算交集
        x_overlap = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
        y_overlap = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
        intersection = x_overlap * y_overlap
        
        # 计算并集
        area1 = w1 * h1
        area2 = w2 * h2
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0
    
    def draw_detections(self, frame, smoke_regions):
        """绘制检测结果"""
        result_frame = frame.copy()
        
        for region in smoke_regions:
            x, y, w, h = region['bbox']
            confidence = region['confidence']
            
            # 绘制边界框
            color = (0, 255, 0) if confidence > 0.8 else (0, 255, 255)
            cv2.rectangle(result_frame, (x, y), (x + w, y + h), color, 2)
            
            # 绘制置信度
            label = f"Smoke: {confidence:.2f}"
            cv2.putText(result_frame, label, (x, y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            
            # 绘制轮廓
            cv2.drawContours(result_frame, [region['contour']], -1, color, 1)
            
        return result_frame
    
    def save_detection_result(self, frame, smoke_regions):
        """保存检测结果"""
        if smoke_regions:
            timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
            
            # 保存图像
            filename = f"smoke_detected_{timestamp}_frame_{self.frame_count}.jpg"
            filepath = os.path.join(self.output_dir, filename)
            cv2.imwrite(filepath, frame)
            
            # 保存检测数据
            detection_data = {
                'timestamp': timestamp,
                'frame_number': self.frame_count,
                'detections': []
            }
            
            for region in smoke_regions:
                detection_data['detections'].append({
                    'bbox': region['bbox'],
                    'area': region['area'],
                    'confidence': region['confidence'],
                    'features': region['features']
                })
                
            # 保存JSON数据
            json_filename = f"detection_data_{timestamp}.json"
            json_filepath = os.path.join(self.output_dir, json_filename)
            with open(json_filepath, 'w', encoding='utf-8') as f:
                json.dump(detection_data, f, ensure_ascii=False, indent=2)
                
            self.logger.info(f"检测到烟雾! 置信度: {max(r['confidence'] for r in smoke_regions):.2f}")
            
    def process_video(self):
        """处理视频"""
        self.logger.info(f"开始处理视频: {self.video_source}")
        
        while True:
            ret, frame = self.cap.read()
            if not ret:
                break
                
            self.frame_count += 1
            
            # 每隔指定帧数进行检测
            if self.frame_count % self.config.DETECTION_INTERVAL != 0:
                continue
                
            # 预处理
            processed_frame = self.preprocess_frame(frame)
            
            # 检测潜在烟雾区域
            mask, edge_density = self.detect_potential_smoke_regions(processed_frame)
            
            # 分析候选区域
            smoke_candidates = self.analyze_smoke_candidates(processed_frame, mask)
            
            # 时间一致性检查
            consistent_detections = self.temporal_consistency_check(smoke_candidates)
            
            # 绘制结果
            result_frame = self.draw_detections(frame, consistent_detections)
            
            # 保存检测结果
            if consistent_detections:
                self.save_detection_result(result_frame, consistent_detections)
                
            # 显示结果
            cv2.imshow('Smoke Detection', result_frame)
            cv2.imshow('Detection Mask', mask)
            
            # 更新前一帧
            self.previous_frame = frame.copy()
            
            # 按'q'退出
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
                
        self.cleanup()
        
    def cleanup(self):
        """清理资源"""
        self.cap.release()
        cv2.destroyAllWindows()
        self.logger.info("视频处理完成")


def main():
    """主函数"""
    print("高级烟雾检测系统")
    print("支持的视频源:")
    print("1. 1.mp4")
    print("2. 2.mp4")
    print("3. 自定义视频文件")
    print("4. 摄像头 (0)")
    
    choice = input("请选择视频源 (1-4): ").strip()
    
    if choice == '1':
        video_source = '1.mp4'
    elif choice == '2':
        video_source = '2.mp4'
    elif choice == '3':
        video_source = input("请输入视频文件路径: ").strip()
    elif choice == '4':
        video_source = 0
    else:
        print("无效选择，使用默认视频 1.mp4")
        video_source = '1.mp4'
        
    try:
        detector = AdvancedSmokeDetector(video_source)
        detector.process_video()
    except Exception as e:
        print(f"错误: {e}")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化烟雾检测器 - 基于1.mp4和2.mp4视频特征分析
Optimized Smoke Detector - Based on video analysis results
"""

import os

import numpy as np
from datetime import datetime
import threading
import time

# 添加CUDA路径
try:
    os.add_dll_directory(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin')
    os.add_dll_directory(r'E:\JSZK\DICHENG\opencv_contrib_cuda_4.6.0.20221106_win_amd64\install\x64\vc17\bin')

except:
    pass
import cv2
class OptimizedSmokeDetector:
    """优化的烟雾检测器"""
    
    def __init__(self, video_source):
        self.video_source = video_source
        self.cap = cv2.VideoCapture(video_source)
        
        # 基于视频分析的优化参数
        self.brightness_min = 80    # 基于分析结果的亮度范围
        self.brightness_max = 180
        self.motion_threshold = 5.0  # 运动阈值
        self.min_area = 800         # 最小烟雾区域
        self.max_area = 30000       # 最大烟雾区域
        
        # 检测状态
        self.prev_gray = None
        self.frame_count = 0
        self.detection_count = 0
        self.smoke_history = []
        
        # GPU光流计算器
        try:
            self.gpu_flow = cv2.cuda_FarnebackOpticalFlow.create(
                numLevels=3, pyrScale=0.5, fastPyramids=True, 
                winSize=15, numIters=3, polyN=5, polySigma=1.2
            )
            self.use_gpu = True
            print("✓ GPU加速已启用")
        except:
            self.use_gpu = False
            print("⚠ 使用CPU计算")
            
        # 创建输出目录
        self.output_dir = 'optimized_smoke_results'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            
        print(f"✓ 烟雾检测器初始化完成")
        print(f"  - 视频源: {video_source}")
        print(f"  - 亮度范围: {self.brightness_min}-{self.brightness_max}")
        print(f"  - 运动阈值: {self.motion_threshold}")
        
    def detect_smoke_regions(self, frame):
        """检测烟雾区域 - 基于视频特征优化"""
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        h, w = gray.shape
        
        # 1. 亮度过滤 - 基于分析的亮度分布
        brightness_mask = cv2.inRange(gray, self.brightness_min, self.brightness_max)
        
        # 2. 运动检测 - 烟雾通常有运动特征
        motion_mask = np.zeros_like(gray)
        if self.prev_gray is not None:
            if self.use_gpu:
                motion_mask = self.gpu_motion_detection(gray)
            else:
                motion_mask = self.cpu_motion_detection(gray)
        
        # 3. 纹理分析 - 烟雾纹理特征
        texture_mask = self.analyze_texture(gray)
        
        # 4. 结合多种特征
        combined_mask = cv2.bitwise_and(brightness_mask, motion_mask)
        combined_mask = cv2.bitwise_and(combined_mask, texture_mask)
        
        # 5. 形态学处理
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
        
        # 6. 轮廓检测和过滤
        contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        smoke_regions = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if self.min_area < area < self.max_area:
                # 计算轮廓特征
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h
                extent = area / (w * h)
                
                # 烟雾形状特征过滤
                if 0.3 < aspect_ratio < 3.0 and extent > 0.3:
                    confidence = self.calculate_confidence(frame[y:y+h, x:x+w], area)
                    if confidence > 0.5:
                        smoke_regions.append({
                            'contour': contour,
                            'bbox': (x, y, w, h),
                            'area': area,
                            'confidence': confidence
                        })
        
        self.prev_gray = gray.copy()
        return smoke_regions, combined_mask
    
    def gpu_motion_detection(self, gray):
        """GPU加速运动检测"""
        try:
            # 上传到GPU
            gpu_current = cv2.cuda_GpuMat()
            gpu_prev = cv2.cuda_GpuMat()
            gpu_current.upload(gray)
            gpu_prev.upload(self.prev_gray)
            
            # GPU光流计算
            gpu_flow = self.gpu_flow.calc(gpu_prev, gpu_current, None)
            flow = gpu_flow.download()
            
            # 计算运动幅度
            magnitude, _ = cv2.cartToPolar(flow[..., 0], flow[..., 1])
            motion_mask = (magnitude > self.motion_threshold).astype(np.uint8) * 255
            
            return motion_mask
        except:
            return self.cpu_motion_detection(gray)
    
    def cpu_motion_detection(self, gray):
        """CPU运动检测"""
        # 帧差法
        diff = cv2.absdiff(gray, self.prev_gray)
        motion_mask = cv2.threshold(diff, 25, 255, cv2.THRESH_BINARY)[1]
        
        # 高斯模糊减少噪声
        motion_mask = cv2.GaussianBlur(motion_mask, (5, 5), 0)
        
        return motion_mask
    
    def analyze_texture(self, gray):
        """分析纹理特征 - 优化版本"""
        try:
            # 使用更快的方法计算纹理特征
            # 方法1: 使用高斯模糊差值
            blurred = cv2.GaussianBlur(gray, (9, 9), 2)
            texture_diff = cv2.absdiff(gray, blurred)

            # 烟雾区域纹理差异较小
            texture_mask = cv2.threshold(texture_diff, 15, 255, cv2.THRESH_BINARY_INV)[1]

            return texture_mask

        except Exception as e:
            print(f"纹理分析出错: {e}")
            # 返回全白掩码作为备用
            return np.ones_like(gray) * 255
    
    def calculate_confidence(self, region, area):
        """计算烟雾置信度"""
        if region.size == 0:
            return 0.0
            
        confidence = 0.0
        
        # 1. 亮度特征 (30%)
        gray_region = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
        mean_brightness = np.mean(gray_region)
        if self.brightness_min <= mean_brightness <= self.brightness_max:
            confidence += 0.3
            
        # 2. 颜色特征 (25%) - 烟雾通常饱和度低
        hsv_region = cv2.cvtColor(region, cv2.COLOR_BGR2HSV)
        mean_saturation = np.mean(hsv_region[:, :, 1])
        if mean_saturation < 50:  # 低饱和度
            confidence += 0.25
            
        # 3. 纹理特征 (25%) - 烟雾边缘模糊
        edges = cv2.Canny(gray_region, 50, 150)
        edge_density = np.sum(edges > 0) / edges.size
        if edge_density < 0.05:  # 边缘密度低
            confidence += 0.25
            
        # 4. 面积特征 (20%)
        if 1000 < area < 15000:  # 合理的烟雾面积
            confidence += 0.2
            
        return min(confidence, 1.0)
    
    def temporal_filtering(self, current_detections):
        """时间过滤 - 减少误检"""
        self.smoke_history.append(current_detections)
        
        # 保持3帧历史
        if len(self.smoke_history) > 3:
            self.smoke_history.pop(0)
            
        # 如果连续检测到烟雾，增加置信度
        if len(self.smoke_history) >= 2:
            filtered_detections = []
            for detection in current_detections:
                # 检查历史中是否有相似位置的检测
                has_history = False
                for hist_detections in self.smoke_history[:-1]:
                    for hist_detection in hist_detections:
                        if self.bbox_overlap(detection['bbox'], hist_detection['bbox']) > 0.3:
                            has_history = True
                            detection['confidence'] = min(detection['confidence'] + 0.2, 1.0)
                            break
                    if has_history:
                        break
                        
                if has_history or detection['confidence'] > 0.7:
                    filtered_detections.append(detection)
                    
            return filtered_detections
        
        return current_detections
    
    def bbox_overlap(self, bbox1, bbox2):
        """计算边界框重叠率"""
        x1, y1, w1, h1 = bbox1
        x2, y2, w2, h2 = bbox2
        
        x_overlap = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
        y_overlap = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
        intersection = x_overlap * y_overlap
        
        union = w1 * h1 + w2 * h2 - intersection
        return intersection / union if union > 0 else 0
    
    def draw_results(self, frame, smoke_regions):
        """绘制检测结果"""
        result_frame = frame.copy()
        
        for region in smoke_regions:
            x, y, w, h = region['bbox']
            confidence = region['confidence']
            
            # 根据置信度选择颜色
            if confidence > 0.8:
                color = (0, 0, 255)  # 红色 - 高置信度
                thickness = 3
            elif confidence > 0.6:
                color = (0, 165, 255)  # 橙色 - 中等置信度
                thickness = 2
            else:
                color = (0, 255, 255)  # 黄色 - 低置信度
                thickness = 2
                
            # 绘制边界框
            cv2.rectangle(result_frame, (x, y), (x + w, y + h), color, thickness)
            
            # 绘制标签
            label = f"SMOKE {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
            cv2.rectangle(result_frame, (x, y - label_size[1] - 10), 
                         (x + label_size[0], y), color, -1)
            cv2.putText(result_frame, label, (x, y - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
        # 添加状态信息
        status_text = f"Frame: {self.frame_count} | Detections: {len(smoke_regions)}"
        cv2.putText(result_frame, status_text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        return result_frame
    
    def save_detection(self, frame, smoke_regions):
        """保存检测结果"""
        if smoke_regions:
            self.detection_count += 1
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 保存检测图像
            filename = f"smoke_detection_{timestamp}_frame_{self.frame_count}.jpg"
            filepath = os.path.join(self.output_dir, filename)
            cv2.imwrite(filepath, frame)
            
            # 记录检测信息
            max_confidence = max(r['confidence'] for r in smoke_regions)
            print(f"🔥 检测到烟雾! 帧:{self.frame_count}, 置信度:{max_confidence:.2f}, 区域数:{len(smoke_regions)}")
            
    def run(self):
        """运行检测 - 增强错误处理"""
        print("🚀 开始烟雾检测...")
        print("按 'q' 退出, 按 's' 截图, 按 'p' 暂停/继续")

        start_time = time.time()
        paused = False

        try:
            while True:
                if not paused:
                    ret, frame = self.cap.read()
                    if not ret:
                        print("📹 视频结束")
                        break

                    self.frame_count += 1

                    try:
                        # 每5帧检测一次以提高性能
                        if self.frame_count % 5 == 0:
                            # 检测烟雾
                            smoke_regions, mask = self.detect_smoke_regions(frame)

                            # 时间过滤
                            filtered_regions = self.temporal_filtering(smoke_regions)

                            # 绘制结果
                            result_frame = self.draw_results(frame, filtered_regions)

                            # 保存检测结果
                            if filtered_regions:
                                self.save_detection(result_frame, filtered_regions)

                            # 显示结果
                            cv2.imshow('Smoke Detection', result_frame)
                            cv2.imshow('Detection Mask', mask)
                        else:
                            cv2.imshow('Smoke Detection', frame)

                    except Exception as e:
                        print(f"⚠️ 处理帧 {self.frame_count} 时出错: {e}")
                        # 继续处理下一帧
                        continue

                # 键盘控制
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    print("🛑 用户退出")
                    break
                elif key == ord('s'):
                    # 手动截图
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    filename = f"manual_capture_{timestamp}.jpg"
                    cv2.imwrite(os.path.join(self.output_dir, filename), frame)
                    print(f"📸 截图保存: {filename}")
                elif key == ord('p'):
                    # 暂停/继续
                    paused = not paused
                    status = "暂停" if paused else "继续"
                    print(f"⏸️ {status}")

        except KeyboardInterrupt:
            print("\n🛑 检测被用户中断")
        except Exception as e:
            print(f"\n❌ 运行时错误: {e}")
        finally:
            # 统计信息
            elapsed_time = time.time() - start_time
            fps = self.frame_count / elapsed_time if elapsed_time > 0 else 0

            print(f"\n📊 检测统计:")
            print(f"  - 总帧数: {self.frame_count}")
            print(f"  - 检测次数: {self.detection_count}")
            print(f"  - 平均FPS: {fps:.2f}")
            print(f"  - 运行时间: {elapsed_time:.2f}秒")

            self.cleanup()
        
    def cleanup(self):
        """清理资源"""
        self.cap.release()
        cv2.destroyAllWindows()
        print("✅ 检测完成")


def main():
    """主函数"""
    print("🔥 优化烟雾检测系统")
    print("=" * 40)
    
    # 选择视频源
    video_files = ['1.mp4', '2.mp4']
    available_files = [f for f in video_files if os.path.exists(f)]
    
    if not available_files:
        print("❌ 未找到视频文件 1.mp4 或 2.mp4")
        video_source = input("请输入视频文件路径 (或按回车使用摄像头): ").strip()
        if not video_source:
            video_source = 0
    else:
        print("📁 可用视频文件:")
        for i, file in enumerate(available_files, 1):
            print(f"  {i}. {file}")
        print(f"  {len(available_files) + 1}. 摄像头")
        print(f"  {len(available_files) + 2}. 自定义文件")
        
        try:
            choice = int(input("请选择 (1-{}): ".format(len(available_files) + 2)))
            if 1 <= choice <= len(available_files):
                video_source = available_files[choice - 1]
            elif choice == len(available_files) + 1:
                video_source = 0
            else:
                video_source = input("请输入视频文件路径: ").strip()
        except:
            video_source = available_files[0]
            
    try:
        detector = OptimizedSmokeDetector(video_source)
        detector.run()
    except Exception as e:
        print(f"❌ 错误: {e}")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
16路实时监控面积警报烟雾检测系统
基于gpu20250514(1)界面效果 + area_alarm_smoke_detector功能
16-Channel Real-time Area Alarm Smoke Detection System
"""

import os
import sys
import time
from datetime import datetime

# 添加CUDA路径
try:
    os.add_dll_directory(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin')
    os.add_dll_directory(r'E:\JSZK\DICHENG\opencv_contrib_cuda_4.6.0.20221106_win_amd64\install\x64\vc17\bin')
except:
    pass

import cv2
import numpy as np

# 导入pygame用于警报音
try:
    import pygame
    pygame.mixer.init()
    HAS_PYGAME = True
    print("✅ pygame音频支持已启用")
except ImportError:
    HAS_PYGAME = False
    print("⚠️ pygame未安装，将使用系统警报音")

# 导入PyQt5
from PyQt5.QtWidgets import (QApplication, QMainWindow, QLabel, QVBoxLayout,
                           QHBoxLayout, QPushButton, QWidget, QSizePolicy,
                           QComboBox, QScrollArea, QGridLayout, QTextEdit,
                           QMessageBox, QSpacerItem, QSpinBox)
from PyQt5.QtGui import QImage, QPixmap
from PyQt5.QtCore import Qt, QThread, pyqtSignal


def write_log(message):
    """写入日志文件"""
    current_directory = os.getcwd()
    file_name = "16路烟雾检测日志.txt"
    file_path = os.path.join(current_directory, file_name)
    
    try:
        with open(file_path, 'a', encoding='utf-8') as file:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            file.write(f"[{timestamp}] {message}\n")
    except Exception as e:
        print(f"Error writing to log file: {e}")


def read_video_paths_from_file(file_path):
    """从文件读取视频路径"""
    try:
        with open(file_path, 'r', encoding="utf-8") as file:
            paths = []
            for line in file:
                line = line.strip()
                if line:
                    paths.append(line)
            return paths
    except FileNotFoundError:
        print(f"文件 {file_path} 不存在")
        return []


class SquareLabel(QLabel):
    """保持16:9比例的视频显示标签"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setMinimumSize(1, 1)
    
    def resizeEvent(self, event):
        super().resizeEvent(event)
        width = self.width()
        height = int(width * 9 / 16)
        self.setFixedHeight(height)


class AreaAlarmVideoThread(QThread):
    """面积警报视频处理线程"""
    
    change_pixmap_signal = pyqtSignal(np.ndarray)
    log_signal = pyqtSignal(str)
    alarm_signal = pyqtSignal(int, float)  # 通道号, 面积
    
    def __init__(self, video_path, thread_id, thread_name):
        super().__init__()
        self.video_path = video_path
        self.thread_id = thread_id
        self.thread_name = thread_name
        self._run_flag = True
        self.stop_detection_flag = False
        self.alarm_triggered = False
        
        # 初始化视频捕获
        self.cap = cv2.VideoCapture(video_path)
        if not self.cap.isOpened():
            print(f"⚠️ 无法打开视频源: {video_path}")
            return
        
        # 获取视频信息
        self.video_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.video_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # 面积阈值设置
        self.area_threshold_cm2 = 5.0  # 5平方厘米
        self.area_threshold_pixels = self.calculate_pixel_area_threshold()
        
        # 检测参数
        self.brightness_min = 80
        self.brightness_max = 180
        self.min_area = 500
        self.max_area = 50000
        self.detection_interval = 25  # 检测间隔
        self.contour_threshold = 400   # 轮廓阈值
        
        # 状态变量
        self.frame_count = 0
        self.prev_gray = None
        self.last_alarm_time = 0
        self.alarm_cooldown = 3.0
        
        # 时间标记区域设置（忽略左上角时间）
        self.time_region = self.calculate_time_region()
        
        # 创建输出目录
        self.output_dir = f'detection_results/channel_{thread_id}'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        
        # 警报音
        self.alarm_sound = None
        if HAS_PYGAME and os.path.exists('1.mp3'):
            try:
                self.alarm_sound = pygame.mixer.Sound('1.mp3')
                print(f"✅ 通道{thread_id}警报音加载成功")
            except Exception as e:
                print(f"⚠️ 通道{thread_id}警报音加载失败: {e}")
        
        print(f"✅ 通道{thread_id}({thread_name})初始化完成")
    
    def calculate_pixel_area_threshold(self):
        """计算5平方厘米对应的像素面积"""
        # 假设视频覆盖区域：100cm × 60cm = 6000cm²
        total_area_cm2 = 100 * 60
        total_pixels = self.video_width * self.video_height
        pixels_per_cm2 = total_pixels / total_area_cm2
        threshold_pixels = int(self.area_threshold_cm2 * pixels_per_cm2)
        
        print(f"📐 通道{self.thread_id}面积换算: 5cm² = {threshold_pixels}像素")
        return threshold_pixels
    
    def calculate_time_region(self):
        """计算时间标记区域（左上角）"""
        time_width = int(self.video_width * 0.25)
        time_height = int(self.video_height * 0.08)
        return {
            'x': 0,
            'y': 0,
            'width': time_width,
            'height': time_height
        }
    
    def mask_time_region(self, mask):
        """在检测掩码中屏蔽时间区域"""
        time_region = self.time_region
        mask[time_region['y']:time_region['y'] + time_region['height'],
             time_region['x']:time_region['x'] + time_region['width']] = 0
        return mask
    
    def detect_smoke(self, frame):
        """烟雾检测算法（整合面积检测）"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 1. 亮度过滤
            brightness_mask = cv2.inRange(gray, self.brightness_min, self.brightness_max)
            
            # 2. 运动检测
            motion_mask = np.ones_like(gray) * 255
            if self.prev_gray is not None:
                diff = cv2.absdiff(gray, self.prev_gray)
                motion_mask = cv2.threshold(diff, 20, 255, cv2.THRESH_BINARY)[1]
                
                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
                motion_mask = cv2.morphologyEx(motion_mask, cv2.MORPH_CLOSE, kernel)
            
            # 3. 结合亮度和运动
            combined_mask = cv2.bitwise_and(brightness_mask, motion_mask)
            
            # 4. 屏蔽时间标记区域
            combined_mask = self.mask_time_region(combined_mask)
            
            # 5. 形态学处理
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
            
            # 6. 轮廓检测
            contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 7. 分析轮廓并计算总面积
            smoke_regions = []
            total_area = 0
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if self.min_area < area < self.max_area:
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h
                    if 0.3 < aspect_ratio < 3.0:
                        confidence = min(area / 10000, 1.0)
                        smoke_regions.append({
                            'bbox': (x, y, w, h),
                            'area': area,
                            'confidence': confidence,
                            'contour': contour
                        })
                        total_area += area
            
            self.prev_gray = gray.copy()
            return smoke_regions, combined_mask, total_area
            
        except Exception as e:
            print(f"⚠️ 通道{self.thread_id}检测出错: {e}")
            return [], np.zeros_like(frame[:,:,0]), 0
    
    def check_area_alarm(self, total_area):
        """检查是否需要触发面积警报"""
        current_time = time.time()
        
        if total_area >= self.area_threshold_pixels:
            if current_time - self.last_alarm_time >= self.alarm_cooldown:
                self.trigger_alarm(total_area)
                self.last_alarm_time = current_time
                return True
        return False
    
    def trigger_alarm(self, total_area):
        """触发警报"""
        self.alarm_triggered = True
        area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels
        
        # 记录日志
        log_message = f"🚨 通道{self.thread_id}({self.thread_name}) 烟雾警报! 检测面积: {area_cm2:.2f}cm² (阈值: {self.area_threshold_cm2}cm²)"
        print(log_message)
        self.log_signal.emit(log_message)
        write_log(log_message)
        
        # 发送警报信号
        self.alarm_signal.emit(self.thread_id, area_cm2)
        
        # 播放警报音
        if self.alarm_sound and not self.stop_detection_flag:
            try:
                self.alarm_sound.play(-1)  # 循环播放
                print(f"🔊 通道{self.thread_id}播放警报音")
            except Exception as e:
                print(f"⚠️ 通道{self.thread_id}播放警报音失败: {e}")
    
    def draw_results(self, frame, smoke_regions, total_area):
        """绘制检测结果"""
        result_frame = frame.copy()
        
        # 计算实际面积
        area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels
        
        # 绘制时间区域标记
        time_region = self.time_region
        cv2.rectangle(result_frame,
                     (time_region['x'], time_region['y']),
                     (time_region['x'] + time_region['width'], 
                      time_region['y'] + time_region['height']),
                     (128, 128, 128), 1)
        cv2.putText(result_frame, "TIME IGNORED",
                   (time_region['x'] + 5, time_region['y'] + 15),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (128, 128, 128), 1)
        
        # 绘制检测区域
        for region in smoke_regions:
            x, y, w, h = region['bbox']
            area = region['area']
            
            # 根据面积选择颜色
            if total_area >= self.area_threshold_pixels:
                color = (0, 0, 255)  # 红色 - 警报
                thickness = 3
            elif region['confidence'] > 0.7:
                color = (0, 165, 255)  # 橙色 - 高置信度
                thickness = 2
            else:
                color = (0, 255, 255)  # 黄色 - 低置信度
                thickness = 2
            
            cv2.rectangle(result_frame, (x, y), (x + w, y + h), color, thickness)
            
            # 绘制区域面积
            region_cm2 = area * self.area_threshold_cm2 / self.area_threshold_pixels
            label = f"Area: {region_cm2:.1f}cm²"
            cv2.putText(result_frame, label, (x, y - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        # 绘制总面积信息
        total_text = f"Total: {area_cm2:.2f}cm² / {self.area_threshold_cm2}cm²"
        if total_area >= self.area_threshold_pixels:
            total_color = (0, 0, 255)  # 红色
            status_text = "ALARM!"
        else:
            total_color = (0, 255, 0)  # 绿色
            status_text = "Normal"
        
        cv2.putText(result_frame, total_text, (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, total_color, 2)
        cv2.putText(result_frame, status_text, (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, total_color, 2)
        
        # 绘制通道信息
        channel_text = f"CH{self.thread_id}: {self.thread_name}"
        cv2.putText(result_frame, channel_text, (10, self.video_height - 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 绘制停止警报按钮（如果警报激活）
        if self.alarm_triggered:
            button_x = self.video_width - 120
            button_y = 10
            button_w = 110
            button_h = 30
            
            cv2.rectangle(result_frame, (button_x, button_y), 
                         (button_x + button_w, button_y + button_h),
                         (0, 0, 255), -1)
            cv2.rectangle(result_frame, (button_x, button_y), 
                         (button_x + button_w, button_y + button_h),
                         (255, 255, 255), 2)
            cv2.putText(result_frame, "STOP ALARM", (button_x + 5, button_y + 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        return result_frame
    
    def save_alarm_image(self, frame, total_area):
        """保存警报图像到results文件夹"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels
        
        filename = f"ALARM_CH{self.thread_id}_{timestamp}_area_{area_cm2:.2f}cm2.jpg"
        filepath = os.path.join(self.output_dir, filename)
        
        # 在图像上添加警报信息
        alarm_frame = frame.copy()
        alarm_text = f"SMOKE ALARM CH{self.thread_id} - Area: {area_cm2:.2f}cm²"
        time_text = f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        cv2.putText(alarm_frame, alarm_text, (50, 100),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 255), 3)
        cv2.putText(alarm_frame, time_text, (50, 140),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)
        
        cv2.imwrite(filepath, alarm_frame)
        print(f"💾 通道{self.thread_id}警报图像已保存: {filename}")
        
        return filepath
    
    def update_params(self, detection_interval=None, contour_threshold=None, 
                     area_threshold=None, brightness_min=None, brightness_max=None):
        """更新检测参数"""
        if detection_interval is not None:
            self.detection_interval = detection_interval
        if contour_threshold is not None:
            self.contour_threshold = contour_threshold
        if area_threshold is not None:
            self.area_threshold_cm2 = area_threshold
            self.area_threshold_pixels = self.calculate_pixel_area_threshold()
        if brightness_min is not None:
            self.brightness_min = brightness_min
        if brightness_max is not None:
            self.brightness_max = brightness_max
    
    def run(self):
        """主运行循环"""
        while self._run_flag:
            ret, frame = self.cap.read()
            if not ret:
                # 尝试重新连接
                self.cap.release()
                time.sleep(1)
                self.cap = cv2.VideoCapture(self.video_path)
                continue
            
            self.frame_count += 1
            
            # 根据检测间隔进行检测
            if self.frame_count % self.detection_interval == 0 and not self.stop_detection_flag:
                try:
                    # 检测烟雾
                    smoke_regions, _, total_area = self.detect_smoke(frame)
                    
                    # 检查面积警报
                    alarm_triggered = self.check_area_alarm(total_area)
                    
                    # 绘制结果
                    result_frame = self.draw_results(frame, smoke_regions, total_area)
                    
                    # 如果触发警报，保存图像
                    if alarm_triggered:
                        self.save_alarm_image(result_frame, total_area)
                    
                    # 发送帧信号
                    self.change_pixmap_signal.emit(result_frame)
                    
                except Exception as e:
                    print(f"⚠️ 通道{self.thread_id}处理帧出错: {e}")
                    self.change_pixmap_signal.emit(frame)
            else:
                self.change_pixmap_signal.emit(frame)
            
            time.sleep(0.03)  # 约30fps
    
    def stop(self):
        """停止线程"""
        self._run_flag = False
        self.stop_detection_flag = True
        
        # 停止警报音
        if self.alarm_sound and HAS_PYGAME:
            try:
                pygame.mixer.stop()
            except:
                pass
        
        if hasattr(self, 'cap') and self.cap.isOpened():
            self.cap.release()
        
        self.quit()
        self.wait()


class MainWindow(QMainWindow):
    """16路实时监控面积警报烟雾检测主窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle('16路实时监控面积警报烟雾检测系统 V2.0')
        self.setGeometry(100, 100, 1600, 1000)

        # 初始化pygame
        if HAS_PYGAME:
            pygame.mixer.init()

        # 系统配置
        self.txt_file_path = 'video_paths.txt'
        self.output_dir = 'detection_results'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

        # 读取视频路径
        self.read_video_paths = read_video_paths_from_file(self.txt_file_path)
        if not self.read_video_paths:
            # 创建默认配置文件
            self.create_default_video_paths()
            self.read_video_paths = read_video_paths_from_file(self.txt_file_path)

        # 计算通道数（每两行为一个通道：路径+名称）
        self.num_channels = len(self.read_video_paths) // 2
        if self.num_channels > 16:
            self.num_channels = 16  # 限制最大16路

        # 初始化线程和UI组件
        self.video_thread = [None] * self.num_channels
        self.image_label = [None] * self.num_channels
        self.number_label = [None] * self.num_channels

        # 日志窗口
        self.log_window = None

        # 初始化UI
        self.initUI()

        print(f"✅ 16路监控系统初始化完成，支持{self.num_channels}个通道")

    def create_default_video_paths(self):
        """创建默认视频路径配置文件"""
        default_paths = []
        for i in range(16):
            default_paths.append(f"rtsp://admin:admin@192.168.1.{200+i}:554/stream2")
            default_paths.append(f"监控{i+1}")

        try:
            with open(self.txt_file_path, 'w', encoding='utf-8') as file:
                for path in default_paths:
                    file.write(path + '\n')
            print(f"✅ 创建默认配置文件: {self.txt_file_path}")
        except Exception as e:
            print(f"⚠️ 创建配置文件失败: {e}")

    def on_confirm_params(self):
        """应用参数设置到所有线程"""
        detection_interval = self.detection_interval_input.value()
        area_threshold = self.area_threshold_input.value()
        contour_threshold = self.contour_threshold_input.value()

        # 更新所有活跃线程的参数
        updated_count = 0
        for thread in self.video_thread:
            if thread is not None and thread.isRunning():
                thread.update_params(
                    detection_interval=detection_interval,
                    area_threshold=area_threshold,
                    contour_threshold=contour_threshold
                )
                updated_count += 1

        # 显示确认消息
        QMessageBox.information(
            self, "参数更新",
            f"已更新 {updated_count} 个活跃通道的参数：\n"
            f"检测间隔: {detection_interval}帧\n"
            f"面积阈值: {area_threshold}cm²\n"
            f"轮廓阈值: {contour_threshold}"
        )

        # 记录日志
        log_msg = f"参数更新: 检测间隔={detection_interval}, 面积阈值={area_threshold}cm², 轮廓阈值={contour_threshold}"
        write_log(log_msg)

    def initialize_monitoring(self):
        """重新初始化监控系统"""
        try:
            # 停止所有线程
            if any(self.video_thread):
                for i in range(len(self.video_thread)):
                    if self.video_thread[i] is not None:
                        try:
                            self.video_thread[i].stop()
                            self.video_thread[i] = None
                        except Exception as e:
                            print(f"停止线程 {i} 时出错: {str(e)}")

                # 清空显示
                for i in range(self.num_channels):
                    if self.image_label[i]:
                        self.image_label[i].clear()
                        self.image_label[i].setText(f"通道 {i+1}\n未连接")
                    if self.number_label[i]:
                        self.number_label[i].clear()

                # 重新初始化pygame
                if HAS_PYGAME:
                    pygame.mixer.quit()
                    pygame.mixer.init()

                time.sleep(0.5)

            # 重新读取配置
            self.read_video_paths = read_video_paths_from_file(self.txt_file_path)
            self.num_channels = min(len(self.read_video_paths) // 2, 16)

            QMessageBox.information(self, "初始化完成", f"系统已重新初始化，支持{self.num_channels}个通道")
            write_log("系统重新初始化完成")

        except Exception as e:
            print(f"初始化过程出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"初始化失败: {str(e)}")

    def start_detection(self):
        """开始全部检测"""
        started_count = 0

        for i in range(self.num_channels):
            if self.video_thread[i] is None:
                try:
                    # 获取视频路径和名称
                    video_path = self.read_video_paths[i * 2]
                    thread_name = self.read_video_paths[i * 2 + 1]

                    # 创建线程
                    self.video_thread[i] = AreaAlarmVideoThread(video_path, i, thread_name)

                    # 连接信号
                    self.video_thread[i].change_pixmap_signal.connect(
                        lambda frame, j=i: self.update_image(frame, j)
                    )
                    self.video_thread[i].log_signal.connect(self.log_message)
                    self.video_thread[i].alarm_signal.connect(self.handle_alarm)

                    # 启动线程
                    self.video_thread[i].start()
                    started_count += 1

                    print(f"✅ 启动通道{i}: {thread_name}")

                except Exception as e:
                    print(f"⚠️ 启动通道{i}失败: {e}")
                    QMessageBox.warning(self, "启动失败", f"通道{i+1}启动失败: {str(e)}")
            else:
                # 恢复检测
                self.video_thread[i].stop_detection_flag = False
                started_count += 1

        if started_count > 0:
            QMessageBox.information(self, "启动完成", f"已启动 {started_count} 个通道的检测")
            write_log(f"启动了 {started_count} 个通道的检测")
        else:
            QMessageBox.warning(self, "启动失败", "没有成功启动任何通道")

    def update_image(self, frame, channel_id):
        """更新视频显示"""
        try:
            rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            h, w, ch = rgb_image.shape
            bytes_per_line = ch * w
            q_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(q_image)

            if channel_id < len(self.image_label) and self.image_label[channel_id]:
                scaled_pixmap = pixmap.scaled(
                    self.image_label[channel_id].size(),
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )
                self.image_label[channel_id].setPixmap(scaled_pixmap)
        except Exception as e:
            print(f"⚠️ 更新通道{channel_id}图像失败: {e}")

    def handle_alarm(self, channel_id, area_cm2):
        """处理警报信号"""
        try:
            # 更新通道标签颜色为红色
            if channel_id < len(self.number_label) and self.number_label[channel_id]:
                self.number_label[channel_id].setStyleSheet("""
                    QLabel {
                        color: white;
                        background-color: red;
                        font-size: 32px;
                        font-weight: bold;
                        padding: 1px;
                        border: 2px solid red;
                    }
                """)

            # 显示警报消息
            alarm_msg = f"🚨 通道{channel_id+1} 烟雾警报！检测面积: {area_cm2:.2f}cm²"
            print(alarm_msg)

            # 可选：显示警报对话框
            # QMessageBox.warning(self, "烟雾警报", alarm_msg)

        except Exception as e:
            print(f"⚠️ 处理通道{channel_id}警报失败: {e}")

    def stop_detection(self):
        """停止全部检测和警报"""
        stopped_count = 0

        for i in range(len(self.video_thread)):
            if self.video_thread[i] is not None:
                try:
                    self.video_thread[i].stop_detection_flag = True

                    # 停止警报音
                    if self.video_thread[i].alarm_triggered:
                        if HAS_PYGAME:
                            pygame.mixer.stop()
                        self.video_thread[i].alarm_triggered = False

                    # 恢复通道标签颜色
                    if i < len(self.number_label) and self.number_label[i]:
                        self.number_label[i].setStyleSheet("""
                            QLabel {
                                color: green;
                                font-size: 32px;
                                font-weight: bold;
                                padding: 1px;
                                border: 1px solid black;
                                background-color: rgba(255, 255, 255, 200);
                            }
                        """)

                    stopped_count += 1

                except Exception as e:
                    print(f"⚠️ 停止通道{i}失败: {e}")

        QMessageBox.information(self, "停止完成", f"已停止 {stopped_count} 个通道的警报")
        write_log(f"停止了 {stopped_count} 个通道的警报")

    def on_button_clicked(self):
        """单通道停止警报"""
        choice = self.comboBox.currentText()

        if choice == '':
            QMessageBox.warning(self, "警告", "请先选择要停止的通道")
            return

        try:
            # 解析选择的通道号
            choice_index = int(choice.replace('号', '')) - 1

            if 0 <= choice_index < len(self.video_thread) and self.video_thread[choice_index] is not None:
                # 停止该通道的警报
                self.video_thread[choice_index].stop_detection_flag = True

                if self.video_thread[choice_index].alarm_triggered:
                    if HAS_PYGAME:
                        pygame.mixer.stop()
                    self.video_thread[choice_index].alarm_triggered = False

                # 恢复通道标签颜色
                if choice_index < len(self.number_label) and self.number_label[choice_index]:
                    self.number_label[choice_index].setStyleSheet("""
                        QLabel {
                            color: green;
                            font-size: 32px;
                            font-weight: bold;
                            padding: 1px;
                            border: 1px solid black;
                            background-color: rgba(255, 255, 255, 200);
                        }
                    """)

                QMessageBox.information(self, "停止成功", f"已停止通道{choice_index+1}的警报")
                write_log(f"停止了通道{choice_index+1}的警报")
            else:
                QMessageBox.warning(self, "警告", f"通道{choice_index+1}未运行")

        except Exception as e:
            print(f"⚠️ 停止单通道警报失败: {e}")
            QMessageBox.critical(self, "错误", f"停止失败: {str(e)}")

    def open_alarm_folder(self):
        """打开警报文件夹"""
        try:
            os.startfile(self.output_dir)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法打开文件夹: {str(e)}")

    def open_video_paths(self):
        """打开视频路径配置文件"""
        try:
            os.startfile(self.txt_file_path)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法打开配置文件: {str(e)}")

    def log_message(self, message):
        """处理日志消息"""
        if self.log_window:
            self.log_window.logTextEdit.append(message)
        print(message)

    def show_log_window(self):
        """显示日志窗口"""
        if self.log_window is None:
            self.log_window = LogWindow(self)
            self.log_window.logTextEdit.setStyleSheet("font-size: 14pt;")
        self.log_window.show()

    def exit_program(self):
        """退出程序"""
        try:
            # 停止所有线程
            for i in range(len(self.video_thread)):
                if self.video_thread[i] is not None:
                    self.video_thread[i].stop()
                    time.sleep(0.1)

            # 清理pygame
            if HAS_PYGAME:
                pygame.mixer.quit()
                pygame.quit()

            write_log("系统正常退出")
            QApplication.instance().quit()

        except Exception as e:
            print(f"退出时出错: {str(e)}")
            QApplication.instance().quit()

    def closeEvent(self, event):
        """处理窗口关闭事件"""
        try:
            # 停止所有视频线程
            for i in range(len(self.video_thread)):
                if self.video_thread[i]:
                    self.video_thread[i].stop()

            # 清理pygame
            if HAS_PYGAME:
                pygame.mixer.quit()
                pygame.quit()

            write_log("窗口关闭，系统退出")
            super().closeEvent(event)

        except Exception as e:
            print(f"关闭窗口时出错: {str(e)}")
            event.accept()


class LogWindow(QMainWindow):
    """日志窗口"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('16路监控系统日志')
        self.setGeometry(100, 100, 1200, 800)

        self.logTextEdit = QTextEdit()
        self.logTextEdit.setReadOnly(True)
        self.logTextEdit.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12pt;
            }
        """)

        self.setCentralWidget(self.logTextEdit)


if __name__ == '__main__':
    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #ecf0f1;
        }
        QWidget {
            font-family: "微软雅黑";
        }
    """)

    try:
        main_window = MainWindow()
        main_window.show()

        write_log("16路监控面积警报烟雾检测系统启动")
        print("🚀 16路监控面积警报烟雾检测系统启动成功")

        sys.exit(app.exec_())

    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        QMessageBox.critical(None, "启动失败", f"系统启动失败: {str(e)}")
        sys.exit(1)

    def create_default_video_paths(self):
        """创建默认视频路径配置文件"""
        default_paths = []
        for i in range(16):
            default_paths.append(f"rtsp://admin:admin@192.168.1.{200+i}:554/stream2")
            default_paths.append(f"监控{i+1}")

        try:
            with open(self.txt_file_path, 'w', encoding='utf-8') as file:
                for path in default_paths:
                    file.write(path + '\n')
            print(f"✅ 创建默认配置文件: {self.txt_file_path}")
        except Exception as e:
            print(f"⚠️ 创建配置文件失败: {e}")

    def initUI(self):
        """初始化用户界面（基于gpu20250514(1)样式）"""
        self.setStyleSheet("""
            QPushButton, QTextEdit, QSpinBox, QComboBox, QLabel {
                font-size: 14pt;
                font-family: "微软雅黑";
                padding: 8px 16px;
            }
        """)

        # 创建网格布局用于视频显示
        grid_layout = QGridLayout()

        # 创建视频显示标签和编号标签
        for i in range(self.num_channels):
            # 创建视频标签
            self.image_label[i] = SquareLabel(self)
            self.image_label[i].setAlignment(Qt.AlignCenter)
            self.image_label[i].setText(f"通道 {i+1}\n未连接")
            self.image_label[i].setStyleSheet("""
                QLabel {
                    background-color: #2c3e50;
                    color: white;
                    border: 2px solid #34495e;
                    font-size: 12pt;
                }
            """)

            # 创建编号标签
            self.number_label[i] = QLabel(f"{i + 1}", self)
            self.number_label[i].setAlignment(Qt.AlignTop | Qt.AlignLeft)
            self.number_label[i].setStyleSheet("""
                QLabel {
                    color: green;
                    font-size: 32px;
                    font-weight: bold;
                    padding: 1px;
                    border: 1px solid black;
                    background-color: rgba(255, 255, 255, 200);
                }
            """)

        # 标题框布局
        title_layout = QHBoxLayout()

        # 标题图片
        self.title_image_label = QLabel(self)
        if os.path.exists('title.jpg'):
            pixmap = QPixmap('title.jpg')
            scaled_pixmap = pixmap.scaled(100, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.title_image_label.setPixmap(scaled_pixmap)

        self.title_label = QLabel('16路实时监控面积警报烟雾检测系统', self)
        self.title_label.setStyleSheet("font-size: 48px; font-weight: bold; color: #2c3e50;")

        title_layout.addSpacerItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        title_layout.addWidget(self.title_image_label)
        title_layout.addWidget(self.title_label)
        title_layout.addSpacerItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))

        # 视频网格布局（4x4网格显示16路视频）
        video_grid_layout = QGridLayout()
        for i in range(4):  # 4行
            for j in range(4):  # 4列
                index = i * 4 + j
                if index < self.num_channels:
                    video_grid_layout.addWidget(self.image_label[index], i, j)
                    video_grid_layout.addWidget(self.number_label[index], i, j)
                    video_grid_layout.setRowStretch(i, 1)
                    video_grid_layout.setColumnStretch(j, 1)

        # 按钮布局
        button_layout = QHBoxLayout()

        # 开始检测按钮
        self.detect_button = QPushButton('开始全部检测', self)
        self.detect_button.clicked.connect(self.start_detection)
        self.detect_button.clicked.connect(self.show_log_window)
        self.detect_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)

        # 停止警报按钮
        self.stop_button = QPushButton('全部停止警报', self)
        self.stop_button.clicked.connect(self.stop_detection)
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        # 单通道控制
        self.comboBox = QComboBox()
        self.comboBox.addItem("")
        for i in range(self.num_channels):
            self.comboBox.addItem(f"{i + 1}号")

        self.one_stop_button = QPushButton('选择停止警报')
        self.one_stop_button.clicked.connect(self.on_button_clicked)

        # 其他按钮
        self.alarm_button = QPushButton('查看警报文件', self)
        self.alarm_button.clicked.connect(self.open_alarm_folder)

        self.add_video_button = QPushButton('管理信号源', self)
        self.add_video_button.clicked.connect(self.open_video_paths)

        self.initUI_button = QPushButton('重新初始化', self)
        self.initUI_button.clicked.connect(self.initialize_monitoring)

        self.log_button = QPushButton('查看日志', self)
        self.log_button.clicked.connect(self.show_log_window)

        self.exit_button = QPushButton('退出系统', self)
        self.exit_button.clicked.connect(self.exit_program)
        self.exit_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        # 添加按钮到布局
        button_layout.addWidget(self.detect_button)
        button_layout.addWidget(self.stop_button)
        button_layout.addWidget(self.comboBox)
        button_layout.addWidget(self.one_stop_button)
        button_layout.addWidget(self.add_video_button)
        button_layout.addWidget(self.initUI_button)
        button_layout.addWidget(self.alarm_button)
        button_layout.addWidget(self.log_button)
        button_layout.addWidget(self.exit_button)

        # 参数调整布局
        param_layout = QHBoxLayout()

        # 检测间隔
        self.detection_interval_label = QLabel("检测间隔（帧）:")
        self.detection_interval_input = QSpinBox()
        self.detection_interval_input.setRange(1, 100)
        self.detection_interval_input.setValue(25)

        # 面积阈值
        self.area_threshold_label = QLabel("面积阈值（cm²）:")
        self.area_threshold_input = QSpinBox()
        self.area_threshold_input.setRange(1, 50)
        self.area_threshold_input.setValue(5)

        # 轮廓阈值
        self.contour_threshold_label = QLabel("轮廓阈值:")
        self.contour_threshold_input = QSpinBox()
        self.contour_threshold_input.setRange(1, 1000)
        self.contour_threshold_input.setValue(400)

        # 参数确认按钮
        self.confirm_param_button = QPushButton("应用参数设置")
        self.confirm_param_button.clicked.connect(self.on_confirm_params)
        self.confirm_param_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        # 添加参数控件到布局
        param_layout.addWidget(self.detection_interval_label)
        param_layout.addWidget(self.detection_interval_input)
        param_layout.addWidget(self.area_threshold_label)
        param_layout.addWidget(self.area_threshold_input)
        param_layout.addWidget(self.contour_threshold_label)
        param_layout.addWidget(self.contour_threshold_input)
        param_layout.addWidget(self.confirm_param_button)

        # 主布局
        main_layout = QVBoxLayout()
        main_layout.addLayout(title_layout)

        # 创建滚动区域
        scroll_widget = QWidget()
        scroll_widget.setLayout(video_grid_layout)
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(scroll_widget)

        main_layout.addWidget(scroll_area)
        main_layout.addLayout(button_layout)
        main_layout.addLayout(param_layout)

        # 设置主布局
        central_widget = QWidget()
        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
16路监控系统画面显示问题诊断脚本
"""

import os
import sys

import time
import numpy as np
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QImage, QPixmap
try:
    os.add_dll_directory(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin')
    os.add_dll_directory(r'E:\JSZK\DICHENG\opencv_contrib_cuda_4.6.0.20221106_win_amd64\install\x64\vc17\bin')
except:
    pass

import cv2
def test_video_sources():
    """测试视频源连接"""
    print("🔍 测试视频源连接...")
    
    # 读取配置文件
    try:
        with open('video_paths.txt', 'r', encoding='utf-8') as file:
            lines = [line.strip() for line in file.readlines() if line.strip()]
        print(f"✅ 读取配置文件成功，共{len(lines)}行")
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return
    
    # 测试前几个视频源
    test_count = min(4, len(lines) // 2)
    print(f"📹 测试前{test_count}个视频源...")
    
    for i in range(test_count):
        video_path = lines[i * 2]
        channel_name = lines[i * 2 + 1]
        
        print(f"\n通道{i+1}: {channel_name}")
        print(f"路径: {video_path}")
        
        # 检查是否为本地文件
        if os.path.exists(video_path):
            size_mb = os.path.getsize(video_path) / (1024*1024)
            print(f"✅ 本地文件存在，大小: {size_mb:.1f}MB")
        elif video_path.startswith(('rtsp://', 'rtmp://')):
            print(f"🌐 RTSP流地址")
        else:
            print(f"❓ 未知类型")
        
        # 尝试连接
        print(f"🔄 尝试连接...")
        cap = cv2.VideoCapture(video_path)
        
        if cap.isOpened():
            print(f"✅ VideoCapture打开成功")
            
            # 尝试读取一帧
            ret, frame = cap.read()
            if ret and frame is not None:
                h, w = frame.shape[:2]
                print(f"✅ 成功读取帧，尺寸: {w}x{h}")
                
                # 检查帧内容
                if np.any(frame):
                    print(f"✅ 帧内容正常")
                else:
                    print(f"⚠️ 帧内容为空")
            else:
                print(f"❌ 无法读取帧")
            
            cap.release()
        else:
            print(f"❌ VideoCapture打开失败")

class TestVideoThread(QThread):
    """测试视频线程"""
    change_pixmap_signal = pyqtSignal(np.ndarray, int)
    
    def __init__(self, video_path, thread_id):
        super().__init__()
        self.video_path = video_path
        self.thread_id = thread_id
        self._run_flag = True
        
    def run(self):
        print(f"🎬 通道{self.thread_id}线程启动: {self.video_path}")
        
        cap = cv2.VideoCapture(self.video_path)
        if not cap.isOpened():
            print(f"❌ 通道{self.thread_id}无法打开视频源")
            return
        
        frame_count = 0
        while self._run_flag:
            ret, frame = cap.read()
            if not ret:
                if os.path.exists(self.video_path):
                    # 本地视频循环播放
                    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    continue
                else:
                    print(f"❌ 通道{self.thread_id}读取帧失败")
                    break
            
            frame_count += 1
            
            # 在帧上添加标识
            cv2.putText(frame, f"Channel {self.thread_id+1}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            cv2.putText(frame, f"Frame: {frame_count}", (10, 70),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # 发送信号
            self.change_pixmap_signal.emit(frame, self.thread_id)
            
            time.sleep(0.1)  # 10fps
        
        cap.release()
        print(f"🛑 通道{self.thread_id}线程结束")
    
    def stop(self):
        self._run_flag = False

class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle('画面显示测试')
        self.setGeometry(100, 100, 800, 600)
        
        # 读取配置
        try:
            with open('video_paths.txt', 'r', encoding='utf-8') as file:
                lines = [line.strip() for line in file.readlines() if line.strip()]
            self.video_paths = []
            for i in range(0, min(8, len(lines)), 2):  # 测试前4个通道
                if i + 1 < len(lines):
                    self.video_paths.append(lines[i])
        except:
            self.video_paths = ['1.mp4', '2.mp4'] * 2  # 默认测试视频
        
        self.num_channels = len(self.video_paths)
        self.video_threads = [None] * self.num_channels
        self.image_labels = [None] * self.num_channels
        
        self.initUI()
        self.start_test()
    
    def initUI(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 创建视频显示标签
        for i in range(self.num_channels):
            self.image_labels[i] = QLabel(f"通道 {i+1}\n等待连接...")
            self.image_labels[i].setAlignment(Qt.AlignCenter)
            self.image_labels[i].setStyleSheet("""
                QLabel {
                    background-color: #2c3e50;
                    color: white;
                    border: 2px solid #34495e;
                    font-size: 14pt;
                    min-height: 120px;
                }
            """)
            layout.addWidget(self.image_labels[i])
    
    def start_test(self):
        """开始测试"""
        print(f"🚀 开始测试{self.num_channels}个通道...")
        
        for i in range(self.num_channels):
            video_path = self.video_paths[i]
            print(f"启动通道{i}: {video_path}")
            
            # 创建线程
            self.video_threads[i] = TestVideoThread(video_path, i)
            
            # 连接信号
            self.video_threads[i].change_pixmap_signal.connect(self.update_image)
            
            # 启动线程
            self.video_threads[i].start()
    
    def update_image(self, frame, channel_id):
        """更新图像显示"""
        try:
            print(f"📺 更新通道{channel_id}图像，帧尺寸: {frame.shape}")
            
            # 转换颜色格式
            rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            h, w, ch = rgb_image.shape
            bytes_per_line = ch * w
            
            # 创建QImage
            q_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)
            
            # 创建QPixmap
            pixmap = QPixmap.fromImage(q_image)
            
            # 缩放并显示
            if channel_id < len(self.image_labels) and self.image_labels[channel_id]:
                scaled_pixmap = pixmap.scaled(
                    self.image_labels[channel_id].size(),
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )
                self.image_labels[channel_id].setPixmap(scaled_pixmap)
                print(f"✅ 通道{channel_id}图像更新成功")
            else:
                print(f"❌ 通道{channel_id}标签不存在")
                
        except Exception as e:
            print(f"❌ 更新通道{channel_id}图像失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        print("🛑 停止所有测试线程...")
        for thread in self.video_threads:
            if thread:
                thread.stop()
                thread.wait()
        event.accept()

def test_qt_display():
    """测试Qt显示功能"""
    print("\n🖥️ 测试Qt显示功能...")
    
    app = QApplication(sys.argv)
    
    try:
        window = TestMainWindow()
        window.show()
        
        print("✅ 测试窗口已显示")
        print("📋 观察要点:")
        print("   1. 窗口是否正常显示")
        print("   2. 各通道是否显示视频画面")
        print("   3. 控制台是否有错误信息")
        print("   4. 帧更新是否正常")
        
        # 运行5秒后自动关闭
        import threading
        def auto_close():
            time.sleep(10)
            app.quit()
        
        threading.Thread(target=auto_close, daemon=True).start()
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ Qt测试失败: {e}")

def main():
    """主函数"""
    print("🔧 16路监控系统画面显示问题诊断")
    print("=" * 60)
    
    # 1. 测试视频源连接
    test_video_sources()
    
    # 2. 测试Qt显示
    choice = input("\n是否进行Qt显示测试? (y/n): ").strip().lower()
    if choice in ['y', 'yes', '是']:
        test_qt_display()
    
    print("\n📋 诊断完成")
    print("如果发现问题，请检查:")
    print("1. 视频文件是否存在且可读")
    print("2. RTSP地址是否正确")
    print("3. 网络连接是否正常")
    print("4. OpenCV是否正确安装")
    print("5. PyQt5是否正确安装")
    print("6. 信号连接是否正确")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烟雾检测测试脚本
Test script for smoke detection algorithms
"""

import os
import time
import numpy as np

# 添加CUDA路径
try:
    os.add_dll_directory(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin')
    os.add_dll_directory(r'E:\JSZK\DICHENG\opencv_contrib_cuda_4.6.0.20221106_win_amd64\install\x64\vc17\bin')
except:
    pass

import cv2

def test_video_access():
    """测试视频文件访问"""
    print("🔍 测试视频文件访问...")
    
    video_files = ['1.mp4', '2.mp4']
    results = {}
    
    for video_file in video_files:
        if os.path.exists(video_file):
            cap = cv2.VideoCapture(video_file)
            if cap.isOpened():
                # 获取视频信息
                frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                fps = cap.get(cv2.CAP_PROP_FPS)
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                
                # 测试读取几帧
                frames_read = 0
                for i in range(10):
                    ret, frame = cap.read()
                    if ret:
                        frames_read += 1
                    else:
                        break
                
                results[video_file] = {
                    'accessible': True,
                    'frame_count': frame_count,
                    'fps': fps,
                    'resolution': f"{width}x{height}",
                    'test_frames_read': frames_read
                }
                
                cap.release()
                print(f"  ✅ {video_file}: {width}x{height}, {fps}fps, {frame_count}帧")
            else:
                results[video_file] = {'accessible': False, 'error': '无法打开视频'}
                print(f"  ❌ {video_file}: 无法打开")
        else:
            results[video_file] = {'accessible': False, 'error': '文件不存在'}
            print(f"  ❌ {video_file}: 文件不存在")
    
    return results

def test_opencv_cuda():
    """测试OpenCV CUDA支持"""
    print("\n🔍 测试OpenCV CUDA支持...")
    
    try:
        # 检查CUDA设备数量
        cuda_devices = cv2.cuda.getCudaEnabledDeviceCount()
        print(f"  CUDA设备数量: {cuda_devices}")
        
        if cuda_devices > 0:
            # 测试GPU内存分配
            try:
                gpu_mat = cv2.cuda_GpuMat(100, 100, cv2.CV_8UC1)
                gpu_mat.release()
                print("  ✅ GPU内存分配测试通过")
                
                # 测试光流计算器
                try:
                    flow_calculator = cv2.cuda_FarnebackOpticalFlow.create()
                    print("  ✅ GPU光流计算器创建成功")
                    return True
                except Exception as e:
                    print(f"  ⚠️ GPU光流计算器创建失败: {e}")
                    return False
                    
            except Exception as e:
                print(f"  ❌ GPU内存分配失败: {e}")
                return False
        else:
            print("  ⚠️ 未检测到CUDA设备")
            return False
            
    except Exception as e:
        print(f"  ❌ CUDA支持检查失败: {e}")
        return False

def test_basic_detection():
    """测试基本检测功能"""
    print("\n🔍 测试基本检测功能...")
    
    # 创建测试图像
    test_image = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 添加模拟烟雾区域 (灰色、模糊边缘)
    cv2.circle(test_image, (320, 240), 50, (120, 120, 120), -1)
    cv2.GaussianBlur(test_image[190:290, 270:370], (15, 15), 0, test_image[190:290, 270:370])
    
    # 基本检测算法测试
    gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
    
    # 亮度过滤
    brightness_mask = cv2.inRange(gray, 80, 180)
    
    # 轮廓检测
    contours, _ = cv2.findContours(brightness_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    detected_regions = 0
    for contour in contours:
        area = cv2.contourArea(contour)
        if 500 < area < 10000:
            detected_regions += 1
    
    print(f"  检测到的区域数量: {detected_regions}")
    
    if detected_regions > 0:
        print("  ✅ 基本检测功能正常")
        return True
    else:
        print("  ⚠️ 基本检测功能可能有问题")
        return False

def test_performance():
    """测试性能"""
    print("\n🔍 测试处理性能...")
    
    # 创建测试视频帧
    test_frames = []
    for i in range(100):
        frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        test_frames.append(frame)
    
    # 测试处理速度
    start_time = time.time()
    
    for frame in test_frames:
        # 模拟检测处理
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        edges = cv2.Canny(blurred, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    end_time = time.time()
    processing_time = end_time - start_time
    fps = len(test_frames) / processing_time
    
    print(f"  处理100帧用时: {processing_time:.2f}秒")
    print(f"  平均FPS: {fps:.2f}")
    
    if fps > 15:
        print("  ✅ 性能测试通过")
        return True
    else:
        print("  ⚠️ 性能可能不足")
        return False

def test_detector_import():
    """测试检测器导入"""
    print("\n🔍 测试检测器模块导入...")
    
    try:
        # 测试导入优化检测器
        import optimized_smoke_detector
        print("  ✅ optimized_smoke_detector 导入成功")
        
        # 测试导入高级检测器
        import advanced_smoke_detector
        print("  ✅ advanced_smoke_detector 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ 其他错误: {e}")
        return False

def run_quick_detection_test():
    """运行快速检测测试"""
    print("\n🔍 运行快速检测测试...")
    
    # 检查是否有可用的视频文件
    video_files = ['1.mp4', '2.mp4']
    available_video = None
    
    for video_file in video_files:
        if os.path.exists(video_file):
            available_video = video_file
            break
    
    if not available_video:
        print("  ⚠️ 没有可用的测试视频文件")
        return False
    
    try:
        # 简单的检测测试
        cap = cv2.VideoCapture(available_video)
        
        if not cap.isOpened():
            print("  ❌ 无法打开视频文件")
            return False
        
        # 读取前10帧进行测试
        frames_processed = 0
        detections = 0
        
        for i in range(10):
            ret, frame = cap.read()
            if not ret:
                break
                
            frames_processed += 1
            
            # 简单的烟雾检测逻辑
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            brightness_mask = cv2.inRange(gray, 80, 180)
            
            # 形态学处理
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            processed_mask = cv2.morphologyEx(brightness_mask, cv2.MORPH_CLOSE, kernel)
            
            # 轮廓检测
            contours, _ = cv2.findContours(processed_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if 800 < area < 30000:
                    detections += 1
                    break
        
        cap.release()
        
        print(f"  处理帧数: {frames_processed}")
        print(f"  检测次数: {detections}")
        
        if frames_processed > 0:
            print("  ✅ 快速检测测试完成")
            return True
        else:
            print("  ❌ 快速检测测试失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试过程中出错: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 烟雾检测系统测试")
    print("=" * 50)
    
    test_results = {}
    
    # 运行各项测试
    test_results['video_access'] = test_video_access()
    test_results['cuda_support'] = test_opencv_cuda()
    test_results['basic_detection'] = test_basic_detection()
    test_results['performance'] = test_performance()
    test_results['module_import'] = test_detector_import()
    test_results['quick_detection'] = run_quick_detection_test()
    
    # 汇总测试结果
    print("\n📊 测试结果汇总")
    print("=" * 50)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        if isinstance(result, bool):
            status = "✅ 通过" if result else "❌ 失败"
            if result:
                passed_tests += 1
        else:
            # 对于复杂结果，检查是否有可访问的视频
            accessible_videos = sum(1 for v in result.values() if v.get('accessible', False))
            status = f"✅ 通过 ({accessible_videos}/2 视频可访问)" if accessible_videos > 0 else "❌ 失败"
            if accessible_videos > 0:
                passed_tests += 1
        
        print(f"  {test_name}: {status}")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
    
    # 给出建议
    print("\n💡 建议:")
    if test_results.get('cuda_support', False):
        print("  - 建议使用GPU加速版本以获得最佳性能")
    else:
        print("  - 建议检查CUDA安装或使用CPU版本")
    
    if passed_tests >= total_tests * 0.8:
        print("  - 系统准备就绪，可以开始烟雾检测")
        print("  - 运行: python optimized_smoke_detector.py")
    else:
        print("  - 建议解决测试中发现的问题后再使用")
    
    print("\n🎯 快速开始:")
    print("  python optimized_smoke_detector.py  # 运行优化检测器")
    print("  python advanced_smoke_detector.py   # 运行高级检测器")

if __name__ == "__main__":
    main()

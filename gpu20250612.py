# 更新日志 更新线程池 降低监测视频的分辨率

import logging
import math
import sys
import os
import threading
import time
import concurrent.futures
from functools import lru_cache

# 设置GPU环境变量
os.environ["OPENCV_OPENCL_DEVICE"] = "NVIDIA"
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# GPU加速包路径 - 根据实际环境修改
os.add_dll_directory(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin')
os.add_dll_directory(r'E:\JSZK\DICHENG\opencv_contrib_cuda_4.6.0.20221106_win_amd64\install\x64\vc17\bin')
import cv2
import numpy as np
from datetime import datetime
import pygame
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QHBoxLayout, QPushButton, QWidget, \
    QSizePolicy, QComboBox, QScrollArea, QGridLayout, QTextEdit, QMessageBox, QSpacerItem, QSpinBox
from PyQt5.QtGui import QImage, QPixmap, QColor
from PyQt5.QtCore import Qt, QThread, pyqtSignal

# 启用OpenCV优化
cv2.setUseOptimized(True)


# GPU资源管理器 - 单例模式
class GPUResourceManager:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
                    # 初始化GPU光流计算对象
                    cls._instance.gpu_flow = cv2.cuda_FarnebackOpticalFlow.create(
                        numLevels=3, pyrScale=0.5, fastPyramids=True,
                        winSize=15, numIters=3, polyN=5, polySigma=1.2, flags=0
                    )
        return cls._instance

    def calculate_flow(self, prev_frame, curr_frame):
        with self._lock:
            return self.gpu_flow.calc(prev_frame, curr_frame, None)


# 线程池管理器
class ThreadPoolManager:
    def __init__(self, max_workers=4):
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
        self.futures = {}

    def submit(self, task_id, task_fn, *args):
        if task_id in self.futures:
            self.futures[task_id].cancel()
        future = self.executor.submit(task_fn, *args)
        self.futures[task_id] = future
        return future

    def shutdown(self):
        self.executor.shutdown(wait=True)


# 视频处理器 - 负责视频处理逻辑
class VideoProcessor:
    def __init__(self, video_path, thread_id, thread_name):
        self.video_path = video_path
        self.thread_id = thread_id
        self.thread_name = thread_name
        self._run_flag = True
        self.reset_state()
        self.gpu_manager = GPUResourceManager()

        # 异常图像输出文件夹
        self.output_dir = 'detection_results'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

        # 配置日志
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

        # 初始化报警声音
        pygame.mixer.init()
        self.alarm_sound = pygame.mixer.Sound('1.mp3') if os.path.exists('1.mp3') else None

    def reset_state(self):
        try:
            self.cap = cv2.VideoCapture(self.video_path)
            if not self.cap.isOpened():
                # 尝试作为数字索引打开
                try:
                    video_index = int(self.video_path)
                    self.cap = cv2.VideoCapture(video_index)
                except ValueError:
                    pass


            if not self.cap.isOpened():
                logging.error(f"无法打开视频源: {self.video_path}")
                return

            # 设置硬件加速解码
            #self.cap.set(cv2.CAP_PROP_HW_ACCELERATION, cv2.VIDEO_ACCELERATION_ANY)
        except Exception as e:
            logging.error(f"初始化视频捕获失败: {str(e)}")

        self.prev_gray = None
        self.frame_counter = 0
        self.detection_interval = 25
        self.contour_threshold = 150
        self.threshold_flow = 1.5
        self.min_area = 500
        self.alarm_triggered = False
        self.stop_detection_flag = False
        self.param_lock = threading.Lock()
        self.kernel = np.ones((5, 5), np.uint8)
        self.gpu_prev_frame = None

    def process_frame(self):
        if not self._run_flag or not self.cap or not self.cap.isOpened():
            return None, False

        try:
            ret, frame = self.cap.read()
            if not ret:
                # 尝试重新打开视频
                self.cap.release()
                time.sleep(1)
                self.reset_state()
                return None, False

            # 降低分辨率以节省资源
           # frame = cv2.resize(frame, (1920, 1080))
            frame = cv2.resize(frame, (640, 360))

            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            if self.prev_gray is None:
                self.prev_gray = gray
                self.gpu_prev_frame = cv2.cuda_GpuMat()
                self.gpu_prev_frame.upload(gray)
                return frame, False

            self.frame_counter += 1

            # 跳过非检测帧
            if self.frame_counter % self.detection_interval != 0:
                return frame, False

            # 上传当前帧到GPU
            gpu_frame = cv2.cuda_GpuMat()
            gpu_frame.upload(gray)

            # 计算光流
            gpu_flow = self.gpu_manager.calculate_flow(self.gpu_prev_frame, gpu_frame)
            flow = gpu_flow.download()
            magnitude, _ = cv2.cartToPolar(flow[..., 0], flow[..., 1])

            # 阈值处理
            mask = np.zeros_like(magnitude)
            mask[magnitude > self.threshold_flow] = 255
            mask = mask.astype(np.uint8)

            # 形态学处理
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, self.kernel)

            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            with self.param_lock:
                contour_threshold = self.contour_threshold

            # 检查是否需要触发警报
            if not self.stop_detection_flag:
                self.alarm_triggered = len(contours) > contour_threshold

                if self.alarm_triggered:
                    # 播放警报声音
                    if self.alarm_sound:
                        self.alarm_sound.play()

                    # 记录日志
                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    log_message = f"{self.thread_name}在 {current_time} 检测到烟雾"
                    self.write_log_to_file(log_message)

                    # 在帧上绘制检测结果
                    for contour in contours:
                        if cv2.contourArea(contour) > self.min_area:
                            x, y, w, h = cv2.boundingRect(contour)
                            cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 0, 255), 2)  # 红色框
                            cv2.putText(frame, "SMOKE DETECTED", (x, y - 10),
                                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)  # 红色文字


                    # 获取当前日期和时间
                    now = datetime.now().strftime('%Y-%m-%d-%H-%M-%S')
                    # 保存原始图像
                    output_img = now + '.jpg'
                    # thread_name_encoded = self.thread_name.encode('utf-8')
                    original_image_path = os.path.join(self.output_dir,
                                                       f'{self.thread_name}_detected_img_{output_img}')

                    cv2.imencode('.jpg', frame)[1].tofile(original_image_path)

            # 更新前一帧
            self.prev_gray = gray
            if self.gpu_prev_frame is not None:
                self.gpu_prev_frame.release()
            self.gpu_prev_frame = gpu_frame

            return frame, self.alarm_triggered

        except Exception as e:
            logging.error(f"处理帧时出错: {str(e)}")
            return None, False

    def update_params(self, detection_interval, contour_threshold):
        with self.param_lock:
            self.detection_interval = detection_interval
            self.contour_threshold = contour_threshold

    def stop(self):
        self._run_flag = False
        if hasattr(self, 'cap') and self.cap is not None:
            self.cap.release()
        if hasattr(self, 'gpu_prev_frame') and self.gpu_prev_frame is not None:
            self.gpu_prev_frame.release()
        if hasattr(self, 'alarm_sound') and self.alarm_sound:
            self.alarm_sound.stop()

    def mute_alarm(self):
        self.stop_detection_flag = True
        if self.alarm_triggered and self.alarm_sound:
            self.alarm_sound.stop()
            self.alarm_triggered = False

    def resume_detection(self):
        self.stop_detection_flag = False

    @staticmethod
    def write_log_to_file(message):
        try:
            current_directory = os.getcwd()
            file_name = "漏袋检测日志.txt"
            file_path = os.path.join(current_directory, file_name)
            with open(file_path, 'a', encoding='utf-8') as file:
                file.write(message + '\n')
        except Exception as e:
            logging.error(f"写入日志文件错误: {e}")


# 视频更新器 - 负责UI更新
class VideoUpdater(QThread):
    update_signal = pyqtSignal(int, np.ndarray)
    log_signal = pyqtSignal(str)

    def __init__(self, processor, label_index):
        super().__init__()
        self.processor = processor
        self.label_index = label_index
        self._run_flag = True

    def run(self):
        while self._run_flag:
            frame, alarm_triggered = self.processor.process_frame()
            if frame is not None:
                self.update_signal.emit(self.label_index, frame)
                if alarm_triggered:
                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    log_message = f"{self.processor.thread_name}在 {current_time} 检测到烟雾"
                    self.log_signal.emit(log_message)
            time.sleep(0.033)  # 约30FPS

    def stop(self):
        self._run_flag = False
        self.wait()


def read_video_paths_from_file(file_path):
    try:
        with open(file_path, 'r', encoding="utf-8") as file:
            paths = []
            for line in file:
                line = line.strip()
                if line:
                    paths.append(line)
            return paths
    except FileNotFoundError:
        logging.error(f"文件 {file_path} 不存在")
        return []
    except Exception as e:
        logging.error(f"读取视频路径文件错误: {e}")
        return []


class SquareLabel(QLabel):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setMinimumSize(320, 180)  # 设置最小尺寸
        self.setAlignment(Qt.AlignCenter)
        self.setStyleSheet("background-color: black; border: 2px solid gray;")

    def resizeEvent(self, event):
        super().resizeEvent(event)
        # 保持16:9的比例
        width = self.width()
        height = int(width * 9 / 16)
        self.setFixedHeight(height)


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('烟雾检测系统 V1.0')
        self.setGeometry(100, 100, 1200, 800)  # 增加窗口高度
        self.log_window = None
        self.txt_file_path = 'video_paths.txt'
        self.output_dir = 'detection_results'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

        # 初始化线程池
        self.thread_pool = ThreadPoolManager(max_workers=4)

        # 初始化GPU资源管理器
        self.gpu_manager = GPUResourceManager()

        # 初始化UI
        self.initUI()

    def initUI(self):
        self.txt_file_path = 'video_paths.txt'
        self.read_video_paths = read_video_paths_from_file(self.txt_file_path)

        # 创建处理器和更新器列表
        self.processors = []
        self.updaters = []

        # 计算视频流数量
        num_streams = int(len(self.read_video_paths) / 2)
        self.image_label = [None] * num_streams
        self.number_label = [None] * num_streams

        self.setStyleSheet("""
            QMainWindow {
                background-color: #2c3e50;
            }
            QPushButton, QTextEdit, QSpinBox, QComboBox, QLabel {
                font-size: 14pt;
                font-family: "微软雅黑";
                padding: 8px 16px;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border-radius: 5px;
                
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
            QComboBox, QSpinBox {
                background-color: white;
                color: #000000;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
            }
            QLabel#titleLabel {
                font-size: 48px;
                font-weight: bold;
                color: #ecf0f1;
                padding: 20px;
            }
        """)

        # 主布局
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # 标题布局
        title_layout = QHBoxLayout()
        title_layout.addStretch(1)

        # 标题图片
        title_image = QLabel()
        if os.path.exists('title.png'):
            pixmap = QPixmap('title.png')
            scaled_pixmap = pixmap.scaled(80, 80, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            title_image.setPixmap(scaled_pixmap)
        else:
            title_image.setText("LOGO")
            title_image.setStyleSheet("font-size: 24px; color: white;")

        # 标题文字
        title_label = QLabel('烟雾检测系统')
        title_label.setObjectName("titleLabel")

        title_layout.addWidget(title_image)
        title_layout.addWidget(title_label)
        title_layout.addStretch(1)

        main_layout.addLayout(title_layout)

        # 视频网格布局
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("background-color: #34495e; border: none;")

        video_container = QWidget()
        video_layout = QGridLayout(video_container)
        video_layout.setSpacing(15)

        # 计算行数和列数 (每行最多4个视频)
        cols = 2
        rows = math.ceil(num_streams / cols)

        for i in range(num_streams):
            row = i // cols
            col = i % cols

            # 创建容器部件
            video_widget = QWidget()
            video_widget.setStyleSheet("background-color: #2c3e50; border-radius: 8px;")
            video_box = QVBoxLayout(video_widget)
            video_box.setContentsMargins(5, 5, 5, 5)
            video_box.setSpacing(5)

            # 视频编号
            number_label = QLabel(f"监控点 {i + 1}: {self.read_video_paths[i * 2 + 1]}")
            number_label.setStyleSheet("""
                QLabel {
                    color: #ecf0f1;
                    font-size: 16px;
                    font-weight: bold;
                    padding: 5px;
                }
            """)
            video_box.addWidget(number_label)

            # 视频显示区域
            image_label = SquareLabel()
            self.image_label[i] = image_label
            video_box.addWidget(image_label, 1)

            # 状态指示器
            status_label = QLabel("状态: 未启动")
            status_label.setStyleSheet("""
                QLabel {
                    color: #bdc3c7;
                    font-size: 14px;
                    padding: 5px;
                }
            """)
            self.number_label[i] = status_label
            video_box.addWidget(status_label)

            video_layout.addWidget(video_widget, row, col)

        scroll_area.setWidget(video_container)
        main_layout.addWidget(scroll_area, 1)  # 添加伸缩因子

        # 控制按钮布局
        control_layout = QHBoxLayout()
        control_layout.setSpacing(15)

        # 左侧控制按钮
        left_control = QHBoxLayout()
        self.detect_button = QPushButton('开始检测')
        self.detect_button.clicked.connect(self.start_detection)
        self.detect_button.setStyleSheet("background-color: #2ecc71;")

        self.stop_button = QPushButton('停止报警')
        self.stop_button.clicked.connect(self.stop_detection)
        self.stop_button.setStyleSheet("background-color: #e74c3c;")

        self.alarm_button = QPushButton('查看报警图片')
        self.alarm_button.clicked.connect(self.open_alarm_folder)
        self.alarm_button.setStyleSheet("background-color: #f39c12;")

        left_control.addWidget(self.detect_button)
        left_control.addWidget(self.stop_button)
        left_control.addWidget(self.alarm_button)

        # 中间控制
        center_control = QHBoxLayout()
        self.comboBox = QComboBox()
        self.comboBox.addItem("选择监控点...")
        for i in range(num_streams):
            self.comboBox.addItem(f"{i + 1}号: {self.read_video_paths[i * 2 + 1]}")

        self.select_stop_button = QPushButton('暂停报警')
        self.select_stop_button.clicked.connect(self.on_select_stop)

        center_control.addWidget(self.comboBox)
        center_control.addWidget(self.select_stop_button)

        # 右侧控制
        right_control = QHBoxLayout()
        self.add_video_button = QPushButton('管理信号源')
        self.add_video_button.clicked.connect(self.open_video_paths)

        self.init_button = QPushButton('重新初始化')
        self.init_button.clicked.connect(self.initialize_monitoring)

        self.log_button = QPushButton('查看日志')
        self.log_button.clicked.connect(self.show_log_window)

        self.exit_button = QPushButton('退出系统')
        self.exit_button.clicked.connect(self.exit_program)
        self.exit_button.setStyleSheet("background-color: #7f8c8d;")

        right_control.addWidget(self.add_video_button)
        right_control.addWidget(self.init_button)
        right_control.addWidget(self.log_button)
        right_control.addWidget(self.exit_button)

        # 添加到主控制布局
        control_layout.addLayout(left_control, 1)
        control_layout.addLayout(center_control, 2)
        control_layout.addLayout(right_control, 1)

        main_layout.addLayout(control_layout)

        # 参数调整布局
        param_layout = QHBoxLayout()
        param_layout.setSpacing(10)

        self.detection_interval_label = QLabel("检测间隔(帧):")
        self.detection_interval_label.setStyleSheet("color: #ecf0f1;")

        self.detection_interval_input = QSpinBox()
        self.detection_interval_input.setRange(1, 1000)
        self.detection_interval_input.setValue(25)
        self.detection_interval_input.setStyleSheet("background-color: white;")

        self.contour_threshold_label = QLabel("轮廓阈值:")
        self.contour_threshold_label.setStyleSheet("color: #ecf0f1;")

        self.contour_threshold_input = QSpinBox()
        self.contour_threshold_input.setRange(1, 1000)
        self.contour_threshold_input.setValue(150)
        self.contour_threshold_input.setStyleSheet("background-color: white;")

        self.confirm_param_button = QPushButton("应用参数")
        self.confirm_param_button.clicked.connect(self.on_confirm_params)
        self.confirm_param_button.setStyleSheet("background-color: #9b59b6;")

        param_layout.addWidget(self.detection_interval_label)
        param_layout.addWidget(self.detection_interval_input)
        param_layout.addWidget(self.contour_threshold_label)
        param_layout.addWidget(self.contour_threshold_input)
        param_layout.addWidget(self.confirm_param_button)
        param_layout.addStretch(1)

        main_layout.addLayout(param_layout)

        self.setCentralWidget(main_widget)

        # 状态栏
        self.statusBar().showMessage("就绪 | 烟雾检测系统 V1.0")

        # 初始化pygame
        pygame.mixer.init()

    def on_confirm_params(self):
        new_interval = self.detection_interval_input.value()
        new_threshold = self.contour_threshold_input.value()

        for processor in self.processors:
            if processor:
                processor.update_params(new_interval, new_threshold)

        self.statusBar().showMessage(f"参数已更新: 检测间隔={new_interval}帧, 轮廓阈值={new_threshold}", 5000)

    def initialize_monitoring(self):
        try:
            # 停止所有更新器
            for updater in self.updaters:
                if updater is not None:
                    updater.stop()
                    updater.wait(1000)  # 最多等待1秒

            # 停止所有处理器
            for processor in self.processors:
                if processor is not None:
                    processor.stop()

            # 清空列表
            self.processors.clear()
            self.updaters.clear()

            # 重新初始化UI
            self.initUI()
            pygame.mixer.quit()
            pygame.mixer.init()

            self.statusBar().showMessage("系统已重新初始化", 3000)

        except Exception as e:
            logging.error(f"初始化监控错误: {str(e)}")
            QMessageBox.critical(self, "错误", f"初始化失败: {str(e)}")

    def on_select_stop(self):
        choice = self.comboBox.currentText()
        if choice == "选择监控点...":
            self.statusBar().showMessage("请先选择一个监控点", 3000)
            return

        try:
            index = int(choice.split("号")[0]) - 1  # 提取监控点编号
            if 0 <= index < len(self.processors) and self.processors[index]:
                self.processors[index].mute_alarm()
                self.number_label[index].setText("状态: 报警已暂停")
                self.number_label[index].setStyleSheet("color: #f39c12;")
                self.statusBar().showMessage(f"已暂停{choice}的报警", 3000)
        except Exception as e:
            logging.error(f"选择停止错误: {str(e)}")
            self.statusBar().showMessage(f"操作失败: {str(e)}", 5000)

    def exit_program(self):
        # 停止所有更新器
        for updater in self.updaters:
            if updater is not None:
                updater.stop()
                updater.wait(1000)

        # 停止所有处理器
        for processor in self.processors:
            if processor is not None:
                processor.stop()

        # 关闭线程池
        self.thread_pool.shutdown()

        # 退出应用
        pygame.quit()
        QApplication.instance().quit()

    def start_detection(self):
        num_streams = int(len(self.read_video_paths) / 2)

        if num_streams == 0:
            self.statusBar().showMessage("错误: 没有可用的视频源", 5000)
            return

        for i in range(num_streams):
            # 更新状态标签
            self.number_label[i].setText("状态: 运行中")
            self.number_label[i].setStyleSheet("color: #2ecc71;")

            if i >= len(self.processors):
                # 创建新的处理器 三个参数分别为：video_path, thread_id, thread_name，（视频路径，线程id，名称）
                processor = VideoProcessor(
                    self.read_video_paths[i * 2],
                    i,
                    self.read_video_paths[i * 2 + 1]
                )
                # processor = VideoProcessor(
                #     int(self.read_video_paths[i * 2]),
                #     i,
                #     self.read_video_paths[i * 2 + 1]
                # )
                self.processors.append(processor)

                # 创建更新器
                updater = VideoUpdater(processor, i)
                updater.update_signal.connect(self.update_image)
                updater.log_signal.connect(self.log_message)
                self.updaters.append(updater)

                # 启动更新器
                updater.start()
            else:
                # 恢复检测
                if self.processors[i]:
                    self.processors[i].resume_detection()

        self.statusBar().showMessage(f"已启动{num_streams}个监控点的检测", 3000)

    def update_image(self, label_index, frame):
        if label_index < len(self.image_label) and frame is not None:
            try:
                # 转换颜色空间
                rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                # 获取图像尺寸
                h, w, ch = rgb_image.shape
                bytes_per_line = ch * w

                # 创建QImage
                q_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)

                # 转换为QPixmap
                pixmap = QPixmap.fromImage(q_image)

                # 缩放以适应标签
                scaled_pixmap = pixmap.scaled(
                    self.image_label[label_index].width(),
                    self.image_label[label_index].height(),
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )

                # 设置到标签
                self.image_label[label_index].setPixmap(scaled_pixmap)

            except Exception as e:
                logging.error(f"更新图像错误: {str(e)}")
                self.number_label[label_index].setText("状态: 图像错误")
                self.number_label[label_index].setStyleSheet("color: #e74c3c;")

    def stop_detection(self):
        for i, processor in enumerate(self.processors):
            if processor is not None:
                processor.mute_alarm()
                self.number_label[i].setText("状态: 报警已停止")
                self.number_label[i].setStyleSheet("color: #f39c12;")

        self.statusBar().showMessage("已停止所有监控点的报警", 3000)

    def open_alarm_folder(self):
        if os.path.exists(self.output_dir):
            os.startfile(self.output_dir)
        else:
            self.statusBar().showMessage("报警目录不存在", 3000)

    def open_video_paths(self):
        if os.path.exists(self.txt_file_path):
            os.startfile(self.txt_file_path)
        else:
            self.statusBar().showMessage("视频源配置文件不存在", 3000)

    def closeEvent(self, event):
        try:
            # 停止所有更新器
            for updater in self.updaters:
                if updater is not None:
                    updater.stop()
                    updater.wait(1000)

            # 停止所有处理器
            for processor in self.processors:
                if processor is not None:
                    processor.stop()

            # 关闭线程池
            self.thread_pool.shutdown()

            # 清理pygame
            pygame.mixer.quit()
            pygame.quit()

            super().closeEvent(event)
        except Exception as e:
            logging.error(f"关闭窗口错误: {str(e)}")
            event.accept()

    def log_message(self, message):
        if self.log_window:
            self.log_window.logTextEdit.append(message)
        self.statusBar().showMessage(message, 5000)

    def show_log_window(self):
        if self.log_window is None:
            self.log_window = LogWindow(self)
            self.log_window.logTextEdit.setStyleSheet("font-size: 14pt; font-family: Consolas;")
        self.log_window.show()


class LogWindow(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle('系统日志')
        self.setGeometry(200, 200, 1000, 600)

        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)

        # 日志文本框
        self.logTextEdit = QTextEdit()
        self.logTextEdit.setReadOnly(True)
        self.logTextEdit.setStyleSheet("""
            QTextEdit {
                background-color: #1e272e;
                color: #ecf0f1;
                font-family: Consolas;
                font-size: 12pt;
                border: 1px solid #3498db;
            }
        """)

        # 控制按钮
        btn_layout = QHBoxLayout()
        self.clear_btn = QPushButton("清空日志")
        self.clear_btn.clicked.connect(self.clear_log)
        self.save_btn = QPushButton("保存日志")
        self.save_btn.clicked.connect(self.save_log)
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)

        btn_layout.addWidget(self.clear_btn)
        btn_layout.addWidget(self.save_btn)
        btn_layout.addStretch(1)
        btn_layout.addWidget(self.close_btn)

        layout.addWidget(self.logTextEdit, 1)
        layout.addLayout(btn_layout)

        self.setCentralWidget(central_widget)

        # 加载现有日志
        self.load_log()

    def load_log(self):
        log_file = "漏袋检测日志.txt"
        if os.path.exists(log_file):
            try:
                with open(log_file, "r", encoding="utf-8") as f:
                    self.logTextEdit.setText(f.read())
            except:
                pass

    def clear_log(self):
        self.logTextEdit.clear()

    def save_log(self):
        log_file = "漏袋检测日志.txt"
        try:
            with open(log_file, "w", encoding="utf-8") as f:
                f.write(self.logTextEdit.toPlainText())
            QMessageBox.information(self, "成功", "日志已保存")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败: {str(e)}")


if __name__ == '__main__':
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用Fusion样式

    # 设置应用样式
    palette = app.palette()
    palette.setColor(palette.Window, QColor(44, 62, 80))
    palette.setColor(palette.WindowText, QColor(236, 240, 241))
    palette.setColor(palette.Base, QColor(52, 73, 94))
    palette.setColor(palette.AlternateBase, QColor(44, 62, 80))
    palette.setColor(palette.ToolTipBase, QColor(236, 240, 241))
    palette.setColor(palette.ToolTipText, QColor(236, 240, 241))
    palette.setColor(palette.Text, QColor(236, 240, 241))
    palette.setColor(palette.Button, QColor(52, 152, 219))
    palette.setColor(palette.ButtonText, QColor(236, 240, 241))
    palette.setColor(palette.BrightText, Qt.red)
    palette.setColor(palette.Highlight, QColor(155, 89, 182))
    palette.setColor(palette.HighlightedText, Qt.white)
    app.setPalette(palette)

    main_window = MainWindow()
    main_window.show()
    sys.exit(app.exec_())
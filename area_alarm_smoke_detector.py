#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
面积警报烟雾检测器 - 当检测面积大于5平方厘米时发出警报
Area Alarm Smoke Detector - Triggers alarm when detected area > 5 cm²
"""

import os
import time
from datetime import datetime

# 添加CUDA路径
try:
    os.add_dll_directory(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin')
    os.add_dll_directory(r'E:\JSZK\DICHENG\opencv_contrib_cuda_4.6.0.20221106_win_amd64\install\x64\vc17\bin')
except:
    pass

import cv2
import numpy as np

# 导入pygame用于播放警报音
try:
    import pygame
    pygame.mixer.init()
    HAS_PYGAME = True
    print("✅ pygame音频支持已启用")
except ImportError:
    HAS_PYGAME = False
    print("⚠️ pygame未安装，将使用系统警报音")

class AreaAlarmSmokeDetector:
    """面积警报烟雾检测器"""
    
    def __init__(self, video_source):
        self.video_source = video_source
        self.cap = cv2.VideoCapture(video_source)
        
        if not self.cap.isOpened():
            raise ValueError(f"无法打开视频源: {video_source}")
        
        # 获取视频信息
        self.video_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.video_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # 面积阈值设置
        self.area_threshold_cm2 = 15.0  # 5平方厘米
        self.area_threshold_pixels = self.calculate_pixel_area_threshold()
        
        # 检测参数
        self.brightness_min = 80
        self.brightness_max = 180
        self.min_area = 500  # 最小检测面积（像素）
        self.max_area = 50000  # 最大检测面积（像素）
        
        # 状态变量
        self.frame_count = 0
        self.detection_count = 0
        self.alarm_count = 0
        self.prev_gray = None
        self.last_alarm_time = 0
        self.alarm_cooldown = 3.0  # 警报冷却时间（秒）

        # 警报控制
        self.alarm_active = False  # 当前是否有活跃的警报
        self.alarm_sound_playing = False  # 警报音是否正在播放

        # 时间标记区域设置（左上角区域，需要忽略）
        self.time_region = self.calculate_time_region()

        # 鼠标回调相关
        self.mouse_callback_set = False
        self.stop_alarm_button = {
            'x': self.video_width - 200,
            'y': 10,
            'width': 180,
            'height': 50,
            'visible': False
        }
        
        # 创建results文件夹
        self.results_dir = 'results'
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)
            print(f"✅ 创建了results文件夹: {self.results_dir}")
        
        # 加载警报音
        self.alarm_sound = None
        if HAS_PYGAME:
            try:
                if os.path.exists('1.mp3'):
                    self.alarm_sound = pygame.mixer.Sound('1.mp3')
                    print("✅ 警报音加载成功: 1.mp3")
                else:
                    print("⚠️ 未找到1.mp3文件")
            except Exception as e:
                print(f"⚠️ 警报音加载失败: {e}")
        
        print(f"✅ 面积警报烟雾检测器初始化完成")
        print(f"   视频源: {video_source}")
        print(f"   视频分辨率: {self.video_width}×{self.video_height}")
        print(f"   面积阈值: {self.area_threshold_cm2}cm² = {self.area_threshold_pixels}像素")
        
    def calculate_pixel_area_threshold(self):
        """计算5平方厘米对应的像素面积"""
        # 假设视频显示的是一个大约1米×0.6米的区域（常见的管道监控视角）
        # 这是一个估算，实际应用中需要根据具体的摄像头安装距离和角度调整
        
        # 假设视频覆盖区域：100cm × 60cm = 6000cm²
        total_area_cm2 = 100 * 60  # 6000平方厘米
        total_pixels = self.video_width * self.video_height  # 总像素数
        
        # 计算每平方厘米对应的像素数
        pixels_per_cm2 = total_pixels / total_area_cm2
        
        # 5平方厘米对应的像素面积
        threshold_pixels = int(self.area_threshold_cm2 * pixels_per_cm2)
        
        print(f"📐 面积换算:")
        print(f"   假设视频覆盖区域: 100cm × 60cm")
        print(f"   每平方厘米像素数: {pixels_per_cm2:.2f}")
        print(f"   5cm²对应像素: {threshold_pixels}")
        
        return threshold_pixels

    def calculate_time_region(self):
        """计算时间标记区域（左上角）"""
        # 监控视频的时间标记通常在左上角
        # 设置一个区域来忽略时间跳动的变化
        time_width = int(self.video_width * 0.25)  # 宽度为视频宽度的25%
        time_height = int(self.video_height * 0.08)  # 高度为视频高度的8%

        return {
            'x': 0,
            'y': 0,
            'width': time_width,
            'height': time_height
        }

    def mask_time_region(self, mask):
        """在检测掩码中屏蔽时间区域"""
        time_region = self.time_region
        mask[time_region['y']:time_region['y'] + time_region['height'],
             time_region['x']:time_region['x'] + time_region['width']] = 0
        return mask

    def mouse_callback(self, event, x, y, flags, param):
        """鼠标回调函数"""
        if event == cv2.EVENT_LBUTTONDOWN:
            # 检查是否点击了停止警报按钮
            button = self.stop_alarm_button
            if (button['visible'] and
                button['x'] <= x <= button['x'] + button['width'] and
                button['y'] <= y <= button['y'] + button['height']):
                self.stop_alarm()

    def stop_alarm(self):
        """停止警报"""
        self.alarm_active = False
        self.alarm_sound_playing = False
        self.stop_alarm_button['visible'] = False

        # 停止pygame音频
        if HAS_PYGAME and pygame.mixer.get_init():
            pygame.mixer.stop()

        print("🔇 警报已停止")

    def detect_smoke(self, frame):
        """烟雾检测算法"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # 1. 亮度过滤
            brightness_mask = cv2.inRange(gray, self.brightness_min, self.brightness_max)
            
            # 2. 运动检测
            motion_mask = np.ones_like(gray) * 255
            if self.prev_gray is not None:
                diff = cv2.absdiff(gray, self.prev_gray)
                motion_mask = cv2.threshold(diff, 20, 255, cv2.THRESH_BINARY)[1]
                
                # 形态学处理
                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
                motion_mask = cv2.morphologyEx(motion_mask, cv2.MORPH_CLOSE, kernel)
            
            # 3. 结合亮度和运动
            combined_mask = cv2.bitwise_and(brightness_mask, motion_mask)

            # 4. 屏蔽时间标记区域
            combined_mask = self.mask_time_region(combined_mask)

            # 5. 形态学处理
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
            
            # 5. 轮廓检测
            contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 6. 分析轮廓
            smoke_regions = []
            total_area = 0
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if self.min_area < area < self.max_area:
                    x, y, w, h = cv2.boundingRect(contour)
                    
                    # 形状过滤
                    aspect_ratio = w / h
                    if 0.3 < aspect_ratio < 3.0:
                        confidence = min(area / 10000, 1.0)
                        
                        smoke_regions.append({
                            'bbox': (x, y, w, h),
                            'area': area,
                            'confidence': confidence,
                            'contour': contour
                        })
                        
                        total_area += area
            
            self.prev_gray = gray.copy()
            return smoke_regions, combined_mask, total_area
            
        except Exception as e:
            print(f"⚠️ 检测出错: {e}")
            return [], np.zeros_like(frame[:,:,0]), 0
    
    def check_area_alarm(self, total_area):
        """检查是否需要触发面积警报"""
        current_time = time.time()
        
        # 检查是否超过面积阈值
        if total_area >= self.area_threshold_pixels:
            # 检查冷却时间
            if current_time - self.last_alarm_time >= self.alarm_cooldown:
                self.trigger_alarm(total_area)
                self.last_alarm_time = current_time
                return True
        
        return False
    
    def trigger_alarm(self, total_area):
        """触发警报"""
        self.alarm_count += 1
        self.alarm_active = True
        self.alarm_sound_playing = True
        self.stop_alarm_button['visible'] = True

        area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels

        print(f"🚨 警报触发! 检测面积: {area_cm2:.2f}cm² (阈值: {self.area_threshold_cm2}cm²)")
        print(f"   帧数: {self.frame_count}, 警报次数: {self.alarm_count}")

        # 播放警报音
        if self.alarm_sound and HAS_PYGAME:
            try:
                self.alarm_sound.play(-1)  # -1表示循环播放
                print("🔊 播放警报音: 1.mp3 (循环)")
            except Exception as e:
                print(f"⚠️ 播放警报音失败: {e}")
        else:
            # 使用系统警报音
            try:
                import winsound
                winsound.Beep(1000, 500)  # 1000Hz, 500ms
                print("🔊 播放系统警报音")
            except:
                print("🔊 警报触发（无音频）")
    
    def draw_results(self, frame, smoke_regions, total_area):
        """绘制检测结果"""
        result_frame = frame.copy()
        
        # 计算实际面积
        area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels
        
        # 绘制检测区域
        for region in smoke_regions:
            x, y, w, h = region['bbox']
            confidence = region['confidence']
            area = region['area']
            
            # 根据面积选择颜色
            if total_area >= self.area_threshold_pixels:
                color = (0, 0, 255)  # 红色 - 警报
                thickness = 3
            elif confidence > 0.7:
                color = (0, 165, 255)  # 橙色 - 高置信度
                thickness = 2
            else:
                color = (0, 255, 255)  # 黄色 - 低置信度
                thickness = 2
            
            # 绘制边界框
            cv2.rectangle(result_frame, (x, y), (x + w, y + h), color, thickness)
            
            # 绘制区域面积
            region_cm2 = area * self.area_threshold_cm2 / self.area_threshold_pixels
            label = f"Area: {region_cm2:.1f}cm²"
            cv2.putText(result_frame, label, (x, y - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        # 绘制总面积信息
        total_text = f"Total Area: {area_cm2:.2f}cm² / {self.area_threshold_cm2}cm²"
        if total_area >= self.area_threshold_pixels:
            total_color = (0, 0, 255)  # 红色
            status_text = "ALARM TRIGGERED!"
        else:
            total_color = (0, 255, 0)  # 绿色
            status_text = "Normal"
        
        cv2.putText(result_frame, total_text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, total_color, 2)
        cv2.putText(result_frame, status_text, (10, 70), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, total_color, 3)
        
        # 绘制时间区域标记（用于调试）
        time_region = self.time_region
        cv2.rectangle(result_frame,
                     (time_region['x'], time_region['y']),
                     (time_region['x'] + time_region['width'], time_region['y'] + time_region['height']),
                     (128, 128, 128), 1)
        cv2.putText(result_frame, "TIME REGION (IGNORED)",
                   (time_region['x'] + 5, time_region['y'] + 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (128, 128, 128), 1)

        # 绘制停止警报按钮
        if self.stop_alarm_button['visible']:
            button = self.stop_alarm_button
            # 绘制按钮背景
            cv2.rectangle(result_frame,
                         (button['x'], button['y']),
                         (button['x'] + button['width'], button['y'] + button['height']),
                         (0, 0, 255), -1)  # 红色背景
            cv2.rectangle(result_frame,
                         (button['x'], button['y']),
                         (button['x'] + button['width'], button['y'] + button['height']),
                         (255, 255, 255), 2)  # 白色边框

            # 绘制按钮文字
            cv2.putText(result_frame, "STOP ALARM",
                       (button['x'] + 20, button['y'] + 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

        # 绘制统计信息
        stats_text = f"Frame: {self.frame_count} | Detections: {len(smoke_regions)} | Alarms: {self.alarm_count}"
        cv2.putText(result_frame, stats_text, (10, self.video_height - 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        return result_frame
    
    def save_alarm_image(self, frame, total_area):
        """保存警报图像到results文件夹"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        area_cm2 = total_area * self.area_threshold_cm2 / self.area_threshold_pixels
        
        filename = f"ALARM_{timestamp}_area_{area_cm2:.2f}cm2_frame_{self.frame_count}.jpg"
        filepath = os.path.join(self.results_dir, filename)
        
        # 在图像上添加警报信息
        alarm_frame = frame.copy()
        alarm_text = f"SMOKE ALARM - Area: {area_cm2:.2f}cm²"
        time_text = f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        cv2.putText(alarm_frame, alarm_text, (50, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 2.0, (0, 0, 255), 4)
        cv2.putText(alarm_frame, time_text, (50, 150), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)
        
        cv2.imwrite(filepath, alarm_frame)
        print(f"💾 警报图像已保存: {filename}")
        
        return filepath
    
    def run(self):
        """运行检测"""
        print("🚀 开始面积警报烟雾检测...")
        print("功能说明:")
        print("  - 自动忽略左上角时间标记区域")
        print("  - 当警报触发时显示停止警报按钮")
        print("  - 点击停止警报按钮可关闭警报音")
        print("按键说明:")
        print("  'q' - 退出")
        print("  's' - 手动截图")
        print("  'a' - 调整面积阈值")
        print("  'x' - 手动停止警报")
        print("  空格 - 暂停/继续")

        start_time = time.time()
        paused = False

        # 设置鼠标回调
        cv2.namedWindow('Area Alarm Smoke Detection')
        cv2.setMouseCallback('Area Alarm Smoke Detection', self.mouse_callback)
        
        try:
            while True:
                if not paused:
                    ret, frame = self.cap.read()
                    if not ret:
                        print("📹 视频结束")
                        break
                    
                    self.frame_count += 1
                    
                    # 每5帧检测一次
                    if self.frame_count % 5 == 0:
                        try:
                            # 检测烟雾
                            smoke_regions, mask, total_area = self.detect_smoke(frame)
                            
                            # 检查面积警报
                            alarm_triggered = self.check_area_alarm(total_area)
                            
                            # 绘制结果
                            result_frame = self.draw_results(frame, smoke_regions, total_area)
                            
                            # 如果触发警报，保存图像
                            if alarm_triggered:
                                self.save_alarm_image(result_frame, total_area)
                            
                            # 显示结果
                            cv2.imshow('Area Alarm Smoke Detection', result_frame)
                            
                            # 显示掩码（可选）
                            if len(smoke_regions) > 0:
                                cv2.imshow('Detection Mask', mask)
                            
                        except Exception as e:
                            print(f"⚠️ 处理帧 {self.frame_count} 时出错: {e}")
                            cv2.imshow('Area Alarm Smoke Detection', frame)
                    else:
                        cv2.imshow('Area Alarm Smoke Detection', frame)
                
                # 键盘控制
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    print("🛑 用户退出")
                    break
                elif key == ord('s'):
                    # 手动截图
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    filename = f"manual_screenshot_{timestamp}.jpg"
                    cv2.imwrite(os.path.join(self.results_dir, filename), frame)
                    print(f"📸 截图保存: {filename}")
                elif key == ord(' '):
                    # 暂停/继续
                    paused = not paused
                    print(f"⏸️ {'暂停' if paused else '继续'}")
                elif key == ord('a'):
                    # 调整面积阈值
                    try:
                        new_threshold = float(input("请输入新的面积阈值(cm²): "))
                        if new_threshold > 0:
                            self.area_threshold_cm2 = new_threshold
                            self.area_threshold_pixels = self.calculate_pixel_area_threshold()
                            print(f"✅ 面积阈值已更新为: {new_threshold}cm²")
                    except:
                        print("⚠️ 输入无效")
                elif key == ord('x'):
                    # 手动停止警报
                    if self.alarm_active:
                        self.stop_alarm()
                    else:
                        print("ℹ️ 当前没有活跃的警报")
                
        except KeyboardInterrupt:
            print("\n🛑 检测被用户中断")
        except Exception as e:
            print(f"\n❌ 运行时错误: {e}")
        finally:
            self.cleanup(start_time)
    
    def cleanup(self, start_time):
        """清理资源并显示统计"""
        elapsed_time = time.time() - start_time
        fps = self.frame_count / elapsed_time if elapsed_time > 0 else 0
        
        print(f"\n📊 检测统计:")
        print(f"  - 总帧数: {self.frame_count}")
        print(f"  - 检测次数: {self.detection_count}")
        print(f"  - 警报次数: {self.alarm_count}")
        print(f"  - 平均FPS: {fps:.2f}")
        print(f"  - 运行时间: {elapsed_time:.2f}秒")
        print(f"  - 面积阈值: {self.area_threshold_cm2}cm²")
        print(f"  - 结果保存在: {self.results_dir}")
        
        self.cap.release()
        cv2.destroyAllWindows()
        print("✅ 检测完成")


def main():
    """主函数"""
    print("🚨 面积警报烟雾检测系统")
    print("=" * 50)
    print("功能: 当检测到的烟雾面积大于5平方厘米时触发警报")
    print("警报: 播放1.mp3音频文件")
    print("保存: 警报图像保存到results文件夹")
    print("=" * 50)
    
    # 检查视频文件
    video_files = ['1.mp4', '2.mp4']
    available_files = [f for f in video_files if os.path.exists(f)]
    
    if available_files:
        print("📁 可用视频文件:")
        for i, file in enumerate(available_files, 1):
            print(f"  {i}. {file}")
        print(f"  {len(available_files) + 1}. 摄像头")
        
        try:
            choice = int(input(f"请选择 (1-{len(available_files) + 1}): "))
            if 1 <= choice <= len(available_files):
                video_source = available_files[choice - 1]
            else:
                video_source = 0
        except:
            video_source = available_files[0]
            print(f"使用默认视频: {video_source}")
    else:
        print("❌ 未找到视频文件")
        video_source = input("请输入视频路径 (或按回车使用摄像头): ").strip()
        if not video_source:
            video_source = 0
    
    try:
        detector = AreaAlarmSmokeDetector(video_source)
        detector.run()
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("💡 建议:")
        print("  1. 检查视频文件是否存在")
        print("  2. 检查1.mp3警报音文件是否存在")
        print("  3. 确保有足够的磁盘空间保存结果")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
16路实时监控面积警报烟雾检测系统演示
Demo for 16-Channel Real-time Area Alarm Smoke Detection System
"""

import os
import sys

def check_system_requirements():
    """检查系统要求"""
    print("🔍 检查16路监控系统要求...")
    print("=" * 60)
    
    # 检查Python包
    required_packages = {
        'cv2': 'OpenCV',
        'numpy': 'NumPy', 
        'PyQt5': 'PyQt5',
        'pygame': 'Pygame (可选)'
    }
    
    missing_packages = []
    
    for package, name in required_packages.items():
        try:
            if package == 'cv2':
                # 添加CUDA路径
                try:
                    os.add_dll_directory(r'C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.8\bin')
                    os.add_dll_directory(r'E:\JSZK\DICHENG\opencv_contrib_cuda_4.6.0.20221106_win_amd64\install\x64\vc17\bin')
                except:
                    pass
                import cv2
                print(f"✅ {name}: {cv2.__version__}")
            elif package == 'numpy':
                import numpy
                print(f"✅ {name}: {numpy.__version__}")
            elif package == 'PyQt5':
                from PyQt5.QtCore import QT_VERSION_STR
                print(f"✅ {name}: {QT_VERSION_STR}")
            elif package == 'pygame':
                import pygame
                print(f"✅ {name}: {pygame.version.ver}")
        except ImportError:
            if package == 'pygame':
                print(f"⚠️ {name}: 未安装 (可选，用于警报音)")
            else:
                print(f"❌ {name}: 未安装")
                missing_packages.append(name)
    
    # 检查文件
    print("\n📁 文件检查:")
    files_to_check = {
        'multi_channel_area_alarm_detector_fixed.py': '主程序文件',
        '1.mp3': '警报音文件',
        '1.mp4': '测试视频1',
        '2.mp4': '测试视频2'
    }
    
    for filename, description in files_to_check.items():
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            if filename.endswith('.mp4'):
                size_str = f"{size/(1024*1024):.1f}MB"
            elif filename.endswith('.mp3'):
                size_str = f"{size/1024:.1f}KB"
            else:
                size_str = f"{size/1024:.1f}KB"
            print(f"  ✅ {filename} - {description} ({size_str})")
        else:
            print(f"  ⚠️ {filename} - {description} (未找到)")
    
    return len(missing_packages) == 0

def show_system_features():
    """显示系统功能特点"""
    print("\n🚀 16路实时监控面积警报烟雾检测系统功能:")
    print("=" * 60)
    
    features = [
        "✅ 支持16路并行视频检测",
        "✅ 基于gpu20250514(1)界面效果",
        "✅ 整合area_alarm_smoke_detector面积警报功能",
        "✅ 智能忽略时间标记区域",
        "✅ 面积阈值警报 (默认5cm²)",
        "✅ 循环播放1.mp3警报音",
        "✅ 自动保存警报图像到detection_results文件夹",
        "✅ 实时显示检测状态和面积信息",
        "✅ 支持单通道和全部通道控制",
        "✅ 参数实时调整功能",
        "✅ 系统日志记录",
        "✅ 4×4网格视频显示布局"
    ]
    
    for feature in features:
        print(feature)

def show_usage_guide():
    """显示使用指南"""
    print("\n📖 使用指南:")
    print("=" * 60)
    
    guide = [
        "1. 启动系统:",
        "   python multi_channel_area_alarm_detector_fixed.py",
        "",
        "2. 配置视频源:",
        "   - 系统会自动创建video_paths.txt配置文件",
        "   - 每两行为一个通道：视频路径 + 通道名称",
        "   - 支持RTSP流、本地视频文件等",
        "",
        "3. 界面布局:",
        "   - 顶部：系统标题",
        "   - 中间：4×4网格显示16路视频",
        "   - 底部：控制按钮和参数设置",
        "",
        "4. 主要控制:",
        "   - 开始全部检测：启动所有通道检测",
        "   - 全部停止警报：停止所有警报音",
        "   - 选择停止警报：停止指定通道警报",
        "   - 管理信号源：编辑video_paths.txt",
        "   - 重新初始化：重启系统",
        "",
        "5. 参数设置:",
        "   - 检测间隔：每N帧检测一次",
        "   - 面积阈值：触发警报的面积(cm²)",
        "   - 轮廓阈值：检测敏感度",
        "",
        "6. 警报功能:",
        "   - 面积超过阈值时自动触发",
        "   - 通道编号变红色显示",
        "   - 循环播放1.mp3警报音",
        "   - 自动保存警报图像",
        "",
        "7. 文件输出:",
        "   - detection_results/：警报图像保存目录",
        "   - 16路烟雾检测日志.txt：系统日志文件"
    ]
    
    for line in guide:
        print(line)

def show_technical_details():
    """显示技术细节"""
    print("\n🔧 技术细节:")
    print("=" * 60)
    
    details = [
        "架构设计:",
        "  - 基于PyQt5的现代GUI界面",
        "  - 多线程并行处理16路视频",
        "  - OpenCV + CUDA加速图像处理",
        "  - Pygame音频播放支持",
        "",
        "检测算法:",
        "  - 多特征融合：亮度 + 运动 + 纹理",
        "  - 时间区域屏蔽：忽略左上角时间标记",
        "  - 形态学处理：降噪和轮廓优化",
        "  - 面积精确计算：像素到实际面积转换",
        "",
        "性能优化:",
        "  - 可调检测间隔：平衡精度和性能",
        "  - GPU加速：支持CUDA并行计算",
        "  - 内存管理：智能缓存和资源清理",
        "  - 异常处理：自动重连和错误恢复",
        "",
        "界面特色:",
        "  - 仿gpu20250514(1)经典界面风格",
        "  - 16:9比例视频显示",
        "  - 实时状态指示：绿色正常/红色警报",
        "  - 响应式布局：支持窗口缩放"
    ]
    
    for line in details:
        print(line)

def create_demo_config():
    """创建演示配置文件"""
    print("\n⚙️ 创建演示配置...")
    
    # 创建video_paths.txt
    config_content = []
    test_videos = ['1.mp4', '2.mp4']
    
    for i in range(16):
        if i < len(test_videos) and os.path.exists(test_videos[i]):
            config_content.append(test_videos[i])
            config_content.append(f"测试视频{i+1}")
        else:
            config_content.append(f"rtsp://admin:admin@192.168.1.{200+i}:554/stream2")
            config_content.append(f"监控摄像头{i+1}")
    
    try:
        with open('video_paths.txt', 'w', encoding='utf-8') as f:
            for line in config_content:
                f.write(line + '\n')
        print("✅ 创建video_paths.txt配置文件")
    except Exception as e:
        print(f"⚠️ 创建配置文件失败: {e}")
    
    # 创建输出目录
    if not os.path.exists('detection_results'):
        os.makedirs('detection_results')
        print("✅ 创建detection_results输出目录")

def main():
    """主函数"""
    print("🎬 16路实时监控面积警报烟雾检测系统演示")
    print("=" * 60)
    
    # 检查系统要求
    if not check_system_requirements():
        print("\n❌ 系统要求检查失败")
        print("请安装缺失的Python包:")
        print("  pip install opencv-python numpy PyQt5 pygame")
        return
    
    # 显示功能特点
    show_system_features()
    
    # 显示使用指南
    show_usage_guide()
    
    # 显示技术细节
    show_technical_details()
    
    # 创建演示配置
    create_demo_config()
    
    print("\n🚀 准备启动系统:")
    print("=" * 60)
    
    try:
        choice = input("是否现在启动16路监控系统? (y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            print("正在启动16路监控系统...")
            try:
                # 导入并运行主程序
                import multi_channel_area_alarm_detector_fixed
                # 注意：这里需要确保主程序文件存在
                os.system('python multi_channel_area_alarm_detector_fixed.py')
            except ImportError as e:
                print(f"❌ 导入失败: {e}")
                print("请确保multi_channel_area_alarm_detector_fixed.py文件存在")
            except Exception as e:
                print(f"❌ 启动失败: {e}")
        else:
            print("演示结束")
    except KeyboardInterrupt:
        print("\n演示被中断")
    
    print("\n📋 相关文件清单:")
    print("- multi_channel_area_alarm_detector_fixed.py  # 主程序")
    print("- demo_16_channel_system.py                   # 本演示脚本")
    print("- video_paths.txt                             # 视频源配置")
    print("- 1.mp3                                       # 警报音文件")
    print("- 1.mp4, 2.mp4                               # 测试视频")
    print("- detection_results/                          # 警报图像输出")
    print("- 16路烟雾检测日志.txt                        # 系统日志")

if __name__ == "__main__":
    main()

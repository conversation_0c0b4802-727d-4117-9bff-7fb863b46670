# 本地视频测试功能说明

## 功能概述

我已经成功为16路实时监控面积警报烟雾检测系统添加了**本地视频测试功能**。现在系统可以使用本地视频文件进行测试，除了视频源是本地文件外，其他所有功能与实时视频检测完全一样。

## 修改部分详细说明

### 1. 修改位置：`create_default_video_paths` 方法
**文件位置：** 第533-563行

**修改内容：**
```python
def create_default_video_paths(self):
    """创建默认视频路径配置文件"""
    default_paths = []
    
    # 检查本地测试视频文件
    test_videos = ['1.mp4', '2.mp4', 'test.mp4', 'smoke_test.mp4', 'demo.mp4']
    available_videos = [v for v in test_videos if os.path.exists(v)]
    
    print(f"🔍 检测到 {len(available_videos)} 个本地视频文件用于测试")
    
    for i in range(16):
        if i < len(available_videos):
            # 使用本地视频文件
            video_path = available_videos[i % len(available_videos)]
            default_paths.append(video_path)
            default_paths.append(f"本地测试视频{i+1}({video_path})")
            print(f"   通道{i+1}: 使用本地视频 {video_path}")
        else:
            # 使用RTSP流
            default_paths.append(f"rtsp://admin:admin@192.168.1.{200+i}:554/stream2")
            default_paths.append(f"网络监控{i+1}")
```

**功能说明：**
- 自动检测当前目录下的本地视频文件
- 支持多种视频格式：mp4, avi, mov, mkv, flv, wmv, m4v, 3gp
- 优先使用本地视频文件，不足16路时补充网络流
- 多个本地视频文件会循环分配给不同通道

### 2. 修改位置：`AreaAlarmVideoThread.__init__` 方法
**文件位置：** 第95-125行

**修改内容：**
```python
# 检测视频源类型
self.is_local_video = self.is_local_video_file(video_path)

# 本地视频特有属性
if self.is_local_video:
    self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
    self.fps = self.cap.get(cv2.CAP_PROP_FPS)
    self.current_frame = 0
    print(f"📹 本地视频信息: {self.total_frames}帧, {self.fps:.2f}fps")
else:
    self.total_frames = 0
    self.fps = 30.0  # 默认FPS
    self.current_frame = 0
```

**功能说明：**
- 自动识别视频源类型（本地文件 vs 网络流）
- 获取本地视频的总帧数和FPS信息
- 初始化播放进度跟踪变量

### 3. 新增方法：`is_local_video_file`
**文件位置：** 第169-185行

**修改内容：**
```python
def is_local_video_file(self, video_path):
    """检测是否为本地视频文件"""
    # 检查是否为本地文件路径
    if os.path.exists(video_path):
        return True
    
    # 检查文件扩展名
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v', '.3gp']
    _, ext = os.path.splitext(video_path.lower())
    if ext in video_extensions:
        return True
    
    # 检查是否为网络流
    if video_path.startswith(('rtsp://', 'rtmp://', 'http://', 'https://')):
        return False
    
    return False
```

**功能说明：**
- 智能识别视频源类型
- 支持多种视频文件格式
- 区分本地文件和网络流

### 4. 修改位置：`run` 方法
**文件位置：** 第467-522行

**修改内容：**
```python
def run(self):
    """主运行循环"""
    while self._run_flag:
        ret, frame = self.cap.read()
        if not ret:
            if self.is_local_video:
                # 本地视频播放完毕，重新开始循环播放
                print(f"🔄 通道{self.thread_id}本地视频播放完毕，重新开始循环播放")
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 重置到第一帧
                self.current_frame = 0
                ret, frame = self.cap.read()
                if not ret:
                    print(f"⚠️ 通道{self.thread_id}无法重新读取视频")
                    break
            else:
                # 网络流重新连接
                print(f"🔄 通道{self.thread_id}网络流断开，尝试重新连接...")
                self.cap.release()
                time.sleep(1)
                self.cap = cv2.VideoCapture(self.video_path)
                continue
        
        # 根据视频类型调整播放速度
        if self.is_local_video and self.fps > 0:
            time.sleep(1.0 / self.fps)  # 按原始FPS播放
        else:
            time.sleep(0.03)  # 约30fps
```

**功能说明：**
- 本地视频播放完毕后自动循环播放
- 网络流断开时自动重连
- 根据视频类型调整播放速度
- 本地视频按原始FPS播放，网络流按30FPS播放

### 5. 修改位置：`draw_results` 方法
**文件位置：** 第406-422行

**修改内容：**
```python
# 绘制通道信息
if self.is_local_video:
    # 本地视频显示播放进度
    progress = (self.current_frame / self.total_frames * 100) if self.total_frames > 0 else 0
    channel_text = f"CH{self.thread_id}: {self.thread_name} [{progress:.1f}%]"
else:
    # 网络流显示帧数
    channel_text = f"CH{self.thread_id}: {self.thread_name} [Frame:{self.frame_count}]"

# 本地视频额外显示循环标识
if self.is_local_video:
    loop_text = f"LOCAL VIDEO (Loop)"
    cv2.putText(result_frame, loop_text, (10, self.video_height - 40),
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
```

**功能说明：**
- 本地视频显示播放进度百分比
- 网络流显示当前帧数
- 本地视频显示"LOCAL VIDEO (Loop)"标识
- 实时更新播放状态

## 功能特点

### 1. 智能视频源识别
- ✅ **自动检测** - 自动识别本地文件和网络流
- ✅ **多格式支持** - 支持mp4, avi, mov, mkv等多种格式
- ✅ **灵活配置** - 可混合使用本地视频和网络流

### 2. 本地视频循环播放
- ✅ **无缝循环** - 播放完毕自动重新开始
- ✅ **进度显示** - 实时显示播放进度百分比
- ✅ **原始FPS** - 按视频原始帧率播放
- ✅ **循环标识** - 清晰标识本地视频模式

### 3. 完整检测功能
- ✅ **面积警报** - 完全保持原有的5cm²阈值检测
- ✅ **时间区域忽略** - 继续屏蔽左上角时间标记
- ✅ **不重复报警** - 保持智能警报管理功能
- ✅ **图像保存** - 警报时自动保存图像

### 4. 混合模式支持
- ✅ **本地+网络** - 可同时使用本地视频和网络流
- ✅ **独立管理** - 每个通道独立处理
- ✅ **状态区分** - 清晰区分不同视频源类型

## 使用方法

### 1. 准备本地视频文件
将测试视频文件放在程序目录下：
```
Pro1/
├── 1.mp4                    # 测试视频1
├── 2.mp4                    # 测试视频2
├── test.mp4                 # 测试视频3
├── smoke_test.mp4           # 烟雾测试视频
├── demo.mp4                 # 演示视频
└── multi_channel_area_alarm_detector_fixed.py
```

### 2. 启动系统
```bash
python multi_channel_area_alarm_detector_fixed.py
```

### 3. 自动配置
系统会自动：
- 检测可用的本地视频文件
- 创建video_paths.txt配置文件
- 分配本地视频到各个通道
- 不足16路时补充网络流配置

### 4. 观察运行状态
- **本地视频通道** - 显示播放进度和"LOCAL VIDEO (Loop)"标识
- **网络流通道** - 显示帧数和通道名称
- **循环播放** - 本地视频播放完毕自动重新开始

## 配置文件示例

自动生成的`video_paths.txt`文件示例：
```
1.mp4
本地测试视频1(1.mp4)
2.mp4
本地测试视频2(2.mp4)
1.mp4
本地测试视频3(1.mp4)
2.mp4
本地测试视频4(2.mp4)
rtsp://admin:admin@192.168.1.204:554/stream2
网络监控5
...
```

## 控制台输出示例

```
🔍 检测到 2 个本地视频文件用于测试
   通道1: 使用本地视频 1.mp4
   通道2: 使用本地视频 2.mp4
   通道3: 使用本地视频 1.mp4
   通道4: 使用本地视频 2.mp4
✅ 创建默认配置文件: video_paths.txt
   本地视频通道: 4
   网络流通道: 12

📹 本地视频信息: 1500帧, 25.00fps
✅ 通道0(本地测试视频1(1.mp4))初始化完成
📹 本地视频信息: 1200帧, 30.00fps
✅ 通道1(本地测试视频2(2.mp4))初始化完成

🔄 通道0本地视频播放完毕，重新开始循环播放
🔄 通道1本地视频播放完毕，重新开始循环播放
```

## 测试建议

### 1. 准备测试视频
- **包含烟雾场景** - 用于测试警报功能
- **不同分辨率** - 测试系统兼容性
- **不同时长** - 测试循环播放功能
- **不同格式** - 验证格式支持

### 2. 功能验证
1. **循环播放测试** - 验证视频播放完毕后自动重新开始
2. **进度显示测试** - 验证播放进度百分比正确显示
3. **警报功能测试** - 验证本地视频中的烟雾检测和警报
4. **混合模式测试** - 同时使用本地视频和网络流

### 3. 性能测试
- **多通道并发** - 测试多个本地视频同时播放
- **资源占用** - 监控CPU和内存使用情况
- **播放流畅性** - 验证按原始FPS播放的效果

## 技术优势

### 1. 开发测试便利
- **离线测试** - 无需网络环境即可测试
- **可重复测试** - 相同场景可重复验证
- **快速调试** - 本地文件加载速度快

### 2. 功能完整性
- **完全兼容** - 所有检测功能完全保持
- **智能适配** - 自动适配不同视频源类型
- **无缝切换** - 本地视频和网络流无缝混合

### 3. 用户友好
- **自动配置** - 无需手动编辑配置文件
- **状态清晰** - 清楚显示视频源类型和状态
- **操作简单** - 只需放置视频文件即可使用

## 总结

通过这次改进，16路监控系统现在具备了完善的本地视频测试功能：

✅ **智能识别** - 自动检测和配置本地视频文件  
✅ **循环播放** - 本地视频自动循环播放  
✅ **进度显示** - 实时显示播放进度和状态  
✅ **完整功能** - 保持所有原有检测和警报功能  
✅ **混合支持** - 支持本地视频和网络流混合使用  
✅ **用户友好** - 简单易用的自动配置机制  

这个功能使得系统在开发、测试和演示阶段更加便利，同时保持了生产环境的完整功能。
